#!/bin/bash 
export WANDB_MODE="disabled"
export NCCL_BLOCKING_WAIT=1 
export NCCL_DEBUG=INFO
export PYTHONFAULTHANDLER=1

for i in $(seq 0.0 0.05 1.0)
do 
    python -m torch.distributed.launch \
    --nproc_per_node 1 \
    --master_port 12346  main.py --eval \
    --cfg configs/swin_erp/stanford_erp_3.yaml \
    --data-path  /home-local2/akath.extra.nobkp/semantic2d3d \
    --resume /home-local2/akath.extra.nobkp/stanford_erp/stanford_high/default/ckpt_epoch_best_new_resume.pth \
    --output /home-local2/akath.extra.nobkp/swin_high/  \
    --fov 175.0 \
    --xi $i \
    --batch-size 4
done



