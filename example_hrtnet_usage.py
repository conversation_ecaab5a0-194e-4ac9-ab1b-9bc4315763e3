#!/usr/bin/env python3
"""
Example usage of the clean HRTNet implementation
"""

import torch
import torch.nn as nn
from models.build import SwinUnet

def create_hrtnet_config():
    """Create a simple config for HRTNet"""
    class Config:
        def __init__(self):
            self.MODEL = type('obj', (object,), {
                'TYPE': 'hrtnet',
                'NUM_CLASSES': 19,           # Number of segmentation classes
                'BASE_CHANNELS': 32,         # Base channels (32=small, 64=medium, 128=large)
                'NORM': 'BatchNorm2d',       # Normalization type
                'ALIGN_CORNERS': False       # Bilinear interpolation setting
            })()
            self.DATA = type('obj', (object,), {
                'IMG_SIZE_WOOD': (512, 1024)  # Input image size (H, W)
            })()
    return Config()

def main():
    print("🚀 HRTNet Clean Implementation Example")
    print("=" * 50)
    
    # 1. Create config
    config = create_hrtnet_config()
    print(f"✓ Config created:")
    print(f"  - Model type: {config.MODEL.TYPE}")
    print(f"  - Classes: {config.MODEL.NUM_CLASSES}")
    print(f"  - Base channels: {config.MODEL.BASE_CHANNELS}")
    print(f"  - Input size: {config.DATA.IMG_SIZE_WOOD}")
    
    # 2. Create model
    model = SwinUnet(config, img_size=512, num_classes=19)
    print(f"✓ Model created successfully")
    
    # 3. Model info
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"✓ Model parameters:")
    print(f"  - Total: {total_params:,}")
    print(f"  - Trainable: {trainable_params:,}")
    
    # 4. Test forward pass
    batch_size = 2
    height, width = config.DATA.IMG_SIZE_WOOD
    
    # Create dummy inputs (as required by your existing system)
    dummy_input = torch.randn(batch_size, 3, height, width)
    dummy_dist = torch.randn(batch_size, 1)
    dummy_class_batch = torch.randn(batch_size, 1)
    
    print(f"✓ Testing forward pass:")
    print(f"  - Input shape: {dummy_input.shape}")
    
    # Forward pass
    model.eval()
    with torch.no_grad():
        output = model(dummy_input, dummy_dist, dummy_class_batch)
    
    print(f"  - Output shape: {output.shape}")
    print(f"  - Expected: ({batch_size}, {config.MODEL.NUM_CLASSES}, {height}, {width})")
    
    # 5. Verify output
    expected_shape = (batch_size, config.MODEL.NUM_CLASSES, height, width)
    if output.shape == expected_shape:
        print("✅ SUCCESS: Forward pass works correctly!")
    else:
        print("❌ ERROR: Output shape mismatch!")
        return
    
    # 6. Show how to use in training
    print("\n📚 Usage in Training:")
    print("```python")
    print("# In your training loop:")
    print("model = SwinUnet(config)")
    print("optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)")
    print("criterion = nn.CrossEntropyLoss()")
    print("")
    print("# Forward pass")
    print("logits = model(images, dist, class_batch)")
    print("loss = criterion(logits, targets)")
    print("")
    print("# Backward pass")
    print("optimizer.zero_grad()")
    print("loss.backward()")
    print("optimizer.step()")
    print("```")
    
    print("\n🎯 Key Benefits:")
    print("- ✅ No complex NAS parameters")
    print("- ✅ No missing dependencies") 
    print("- ✅ No loss functions in model")
    print("- ✅ Clean, simple architecture")
    print("- ✅ Easy to understand and modify")
    
    print("\n🔧 Configuration Options:")
    print("- BASE_CHANNELS: 16 (tiny), 32 (small), 64 (medium), 128 (large)")
    print("- NORM: 'BatchNorm2d', 'GroupNorm', 'naiveSyncBN'")
    print("- NUM_CLASSES: Your dataset's number of classes")
    print("- IMG_SIZE_WOOD: Your input image dimensions")

if __name__ == "__main__":
    main()
