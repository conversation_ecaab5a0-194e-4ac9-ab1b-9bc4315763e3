#!/bin/bash 
export WANDB_MODE="disabled"
export NCCL_BLOCKING_WAIT=1 
export NCCL_DEBUG=INFO
export PYTHONFAULTHANDLER=1

for i in $(seq 0.0 0.05 1.0)
do 
    python -m torch.distributed.launch \
    --nproc_per_node 1 \
    --master_port 12346  main_wood_hp.py --eval \
    --cfg configs/swin/stan_hp.yaml \
    --data-path  /home-local2/akath.extra.nobkp/hp_test \
    --resume /home-local2/akath.extra.nobkp/stan_hp_n/stanford_hp_med/default/ckpt_epoch_best_new_resume.pth \
    --output /home-local2/akath.extra.nobkp/swin_high/  \
    --fov 175.0 \
    --xi $i \
    --batch-size 4
done



