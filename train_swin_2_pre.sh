#!/bin/bash 
export WANDB_MODE="disabled"
# export NCCL_BLOCKING_WAIT=1 
# export NCCL_DEBUG=INFO
# export PYTHONFAULTHANDLER=1


python -m torch.distributed.launch \
--nproc_per_node 4 \
--master_port 12345  main.py \
--cfg configs/swin/stanford_pre_2.yaml \
--output /home/<USER>/scratch/stanford \
--data-path  $SLURM_TMPDIR/data_new/semantic2d3d \
--fov 175.0 \
--xi 0.0 \
--batch-size 4

# --resume /home-local2/akath.extra.nobkp/rad_unet/stanford_norm_swin_low/default/ckpt_epoch_best_new_resume.pth \


# --resume /home-local2/akath.extra.nobkp/rad_unet/stanford_norm_swin/default/ckpt_epoch_best_new_resume.pth \


# --cfg configs/swin/CVRG_den_unet.yaml \
