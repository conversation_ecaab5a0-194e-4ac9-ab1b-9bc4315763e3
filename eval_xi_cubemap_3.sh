#!/bin/bash 
export WANDB_MODE="disabled"

for i in $(seq 0.0 0.05 1.0)
# for i in $(seq 0.1 0.3 0.9)
do 
    echo $i
    python -m torch.distributed.launch \
    --nproc_per_node 1 \
    --master_port 12346  main.py --eval \
    --cfg configs/swin_cube/stanford_cube_3.yaml \
    --data-path  /home-local2/akath.extra.nobkp/semantic2d3d \
    --resume /home-local2/akath.extra.nobkp/stanford_cube/stanford_high/default/ckpt_epoch_best_new_resume.pth \
    --output /home-local2/akath.extra.nobkp/rad_unet \
    --fov 175.0 \
    --xi $i \
    --batch-size 4
done


# /home/<USER>/scratch/Radial-unet/swin/default/ckpt_epoch_1800.pth
