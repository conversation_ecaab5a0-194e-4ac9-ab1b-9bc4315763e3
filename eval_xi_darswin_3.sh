#!/bin/bash 
export WANDB_MODE="disabled"
for j in $(seq 0.08 0.08 0.4)
do
    for i in $(seq 0.0 0.1 1.0)
    do 
        echo $i
        error=$(awk -v i=$j 'BEGIN {printf "%.6f", -0.4 + i * (0.8 / 39)}')
        echo $j
        python -m torch.distributed.launch \
        --nproc_per_node 1 \
        --master_port 12346  main.py --eval \
        --cfg configs/swin_ellip/stanford_ellip_3.yaml \
        --data-path  /home-local2/akath.extra.nobkp/semantic2d3d \
        --resume /home-local2/akath.extra.nobkp/rad_unet_/stanford_norm_inv_low/default/ckpt_epoch_best_new_resume.pth \
        --output /home-local2/akath.extra.nobkp/stanford_high \
        --fov 175.0 \
        --xi $i \
        --delta $j \
        --batch-size 4
    done
    # echo $j
done


# /home/<USER>/scratch/Radial-unet/swin/default/ckpt_epoch_1800.pth
