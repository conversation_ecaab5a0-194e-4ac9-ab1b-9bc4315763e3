# --------------------------------------------------------
# Swin Transformer
# Copyright (c) 2021 Microsoft
# Licensed under The MIT License [see LICENSE for details]
# Written by <PERSON><PERSON>
# --------------------------------------------------------

# coding=utf-8
from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

import copy
import logging
import math

from os.path import join as pjoin

import torch
import torch.nn as nn
import numpy as np

from torch.nn import CrossEntropyLoss, Dropout, Softmax, Linear, Conv2d, LayerNorm
from torch.nn.modules.utils import _pair
from scipy import ndimage
from .swin_hp_transformer import SwinHPTransformerSys



    # patch_size: int = 4
    # window_size: int = 4
    # shift_size: int = 2
    # shift_strategy: Literal["nest_roll", "nest_grid_shift", "ring_shift"] = "nest_roll"
    # rel_pos_bias: Optional[Literal["flat"]] = None
    # embed_dim: int = 96
    # patch_embed_norm_layer: Optional[Literal[nn.LayerNorm]] = None
    # depths: List[int] = field(default_factory=lambda: [2, 2, 2, 2])
    # num_heads: List[int] = field(default_factory=lambda: [3, 6, 12, 24])
    # mlp_ratio: float = 4.0
    # qkv_bias: bool = True
    # qk_scale: Optional[float] = None
    # use_cos_attn: bool = False
    # drop_rate: float = 0.0
    # attn_drop_rate: float = 0.0
    # drop_path_rate: float = 0.1
    # norm_layer: Literal[nn.LayerNorm] = nn.LayerNorm
    # use_v2_norm_placement: bool = False
    # ape: bool = False
    # patch_norm: bool = True
    # use_checkpoint: bool = False
    # dev_mode: bool = False  # Developer mode for printing extra information
    # decoder_class: Literal[UnetDecoder] = UnetDecoder
    
class SwinUnet(nn.Module):
    def __init__(self, config, img_size=224, num_classes=21843, zero_head=False, vis=False):
        super(SwinUnet, self).__init__()
        model_type = config.MODEL.TYPE
        if model_type == 'swin_unet':
            self.swin_unet = SwinHPTransformerSys(
                        patch_size=4,
                        shift_size=4,
                        window_size=16,
                        shift_strategy = "ring_shift",
                        rel_pos_bias = "flat",
                        patch_embed_norm_layer= None,
                        depths=[2, 2, 6, 2],
                        num_heads=[3, 6, 12, 24],
                        mlp_ratio = 4.0,
                        qkv_bias = True,
                        qk_scale = None,
                        use_cos_attn=False,
                        drop_rate=0.1,
                        attn_drop_rate=0.1,
                        drop_path_rate = 0.1,
                        norm_layer = nn.LayerNorm,
                        use_v2_norm_placement=False,
                        ape = False,
                        patch_norm = True,
                        embed_dim = 96,
                        use_checkpoint = False,
                        decoder_class = 'UnetDecoder',
                        num_classes = config.MODEL.NUM_CLASSES
                        )
        else: 
            raise NotImplementedError(f"Unkown model: {model_type}")

    def forward(self, x):
        if x.size()[1] == 1:
            x = x.repeat(1,3,1,1)
        logits = self.swin_unet(x)
        return logits


