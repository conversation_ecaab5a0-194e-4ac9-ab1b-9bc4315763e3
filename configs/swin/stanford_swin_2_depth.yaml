MODEL:
  TYPE: swin_unet_depth
  NAME: stanford_swin_med
  DROP_PATH_RATE: 0.2
  NUM_CLASSES: 14
  NRADIUS: 3
  ACTIVATION_TYPE: 'softplus'  # Use SoftPlus instead of sigmoid
  SWIN:
    EMBED_DIM: 96
    FINAL_UPSAMPLE: "expand_first"
    DEPTHS: [ 2, 2, 18, 2 ]
    NUM_HEADS: [ 3, 6, 12, 24 ]
    WINDOW_SIZE_GRID: 4
DATA:
  HIGH: 0.5
  LOW: 0.7
  FOV: 175
  DATASET: stanford_depth
  MEAN: [0.4264, 0.4104, 0.3645]
  STD: [0.2761, 0.2691, 0.2573]

TRAIN:  
  START_EPOCH: 0
  EPOCHS: 500
  WARMUP_EPOCHS: 0
  WEIGHT_DECAY: 0.01
  BASE_LR: 1e-4
  WARMUP_LR: 1e-4
  MIN_LR: 1e-4
  OPTIMIZER:
    NAME: 'adamw'
    EPS: 1e-8
    BETAS: [0.9, 0.999]
  LR_SCHEDULER:
    NAME: 'cosine'
    DECAY_EPOCHS: 1000
    DECAY_RATE: 1.0
