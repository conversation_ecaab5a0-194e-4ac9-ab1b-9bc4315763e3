MODEL:
  TYPE: swin_unet_depth
  NAME: stanford_swin_vlow
  DROP_PATH_RATE: 0.2
  NUM_CLASSES: 14
  NRADIUS: 3
  ACTIVATION_TYPE: 'softplus'  # Use SoftPlus instead of sigmoid
  SWIN:
    EMBED_DIM: 96
    FINAL_UPSAMPLE: "expand_first"
    DEPTHS: [ 2, 2, 18, 2 ]
    NUM_HEADS: [ 3, 6, 12, 24 ]
    WINDOW_SIZE_GRID: 4
DATA:
  HIGH: 0.0
  LOW: 0.05
  FOV: 175
  DATASET: stanford_depth
  MEAN: [0.4216, 0.4049, 0.3585]
  STD: [0.2735, 0.2661, 0.2534]

TRAIN:  
  START_EPOCH: 0
  EPOCHS: 500
  WARMUP_EPOCHS: 0
  WEIGHT_DECAY: 0.01
  BASE_LR: 1e-4
  WARMUP_LR: 1e-4
  MIN_LR: 1e-4
  OPTIMIZER:
    NAME: 'adamw'
    EPS: 1e-8
    BETAS: [0.9, 0.999]
  LR_SCHEDULER:
    NAME: 'cosine'
    DECAY_EPOCHS: 1000
    DECAY_RATE: 1.0
