MODEL:
  TYPE: hrtnet
  NAME: hrtnet_segmentation
  NUM_CLASSES: 19
  DROP_PATH_RATE: 0.2

  # HRTNet specific parameters (simplified)
  BASE_CHANNELS: 32  # Base number of channels
  NORM: 'BatchNorm2d'  # Normalization type (<PERSON><PERSON><PERSON>orm2d, GroupNorm, naiveSyncBN)
  ALIGN_CORNERS: False  # Bilinear interpolation alignment

DATA:
  DATASET: 'your_dataset'
  IMG_SIZE: [1024, 2048]  # Input image size (H, W)
  BATCH_SIZE: 4
  NUM_WORKERS: 8

TRAIN:
  EPOCHS: 100
  BASE_LR: 0.001
  WEIGHT_DECAY: 0.0001
  OPTIMIZER:
    NAME: 'adamw'
