MODEL:
  TYPE: hrtnet
  NAME: hrtnet_segmentation
  NUM_CLASSES: 19
  DROP_PATH_RATE: 0.2
  
  # HRTNet specific parameters
  OPS: [1, 2, 3, 4, 5, 6, 7, 8, 9]  # Operation indices
  PATHS: [[0, 0, 0, 0, 0, 0, 0, 0, 0]]  # Path configuration
  DOWNS: [[0, 0, 0, 0, 0, 0, 0, 0, 0]]  # Downsample configuration
  WIDTHS: [[1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]]  # Width multipliers
  LASTS: [0]  # Last layer configuration
  LAYERS: 9  # Number of layers
  FCH: 12  # Feature channels
  WIDTH_MULT_LIST: [1.0]  # Width multiplier list
  STEM_HEAD_WIDTH: [1.0, 1.0]  # Stem and head width
  NORM: 'naiveSyncBN'  # Normalization type
  ALIGN_CORNERS: False  # Bilinear interpolation alignment
  HRT_MODEL_TYPE: '0'  # HRT model type (0: s=8 no fusion, 1: fuse s4, 2: fuse s4+s8)

DATA:
  DATASET: 'your_dataset'
  IMG_SIZE: [1024, 2048]  # Input image size (H, W)
  BATCH_SIZE: 4
  NUM_WORKERS: 8

TRAIN:
  EPOCHS: 100
  BASE_LR: 0.001
  WEIGHT_DECAY: 0.0001
  OPTIMIZER:
    NAME: 'adamw'
