MODEL:
  TYPE: swin_unet_inv_depth
  NAME: stanford_high
  DROP_PATH_RATE: 0.2
  NUM_CLASSES: 14
  NRADIUS: 3
  ACTIVATION_TYPE: 'softplus'  # Use SoftPlus instead of sigmoid
  SWIN:
    EMBED_DIM: 96
    FINAL_UPSAMPLE: "expand_first"
    DEPTHS: [ 2, 2, 18, 2 ]
    NUM_HEADS: [ 3, 6, 12, 24 ]
    WINDOW_SIZE_GRID: 4
DATA:
  HIGH: 0.8
  LOW: 0.9
  FOV: 175
  DATASET: stanford_da_depth
  MEAN: [0.5426, 0.5224, 0.4627]
  STD: [0.1747, 0.1747, 0.1869]

TRAIN:  
  START_EPOCH: 0
  EPOCHS: 500
  WARMUP_EPOCHS: 0
  WEIGHT_DECAY: 0.01
  BASE_LR: 1e-4
  WARMUP_LR: 1e-4
  MIN_LR: 1e-4
  OPTIMIZER:
    NAME: 'adamw'
    EPS: 1e-8
    BETAS: [0.9, 0.999]
  LR_SCHEDULER:
    NAME: 'cosine'
    DECAY_EPOCHS: 1000
    DECAY_RATE: 1.0
