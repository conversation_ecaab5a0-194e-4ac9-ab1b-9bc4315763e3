#!/bin/bash
#SBATCH --account=def-jlalonde
#SBATCH --nodes=1
#SBATCH --gres=gpu:4  # Request GPU "generic resources"
#SBATCH --cpus-per-task=8  # Refer to cluster's documentation for the right CPU/GPU ratio
#SBATCH --mem=62000M       # Memory proportional to GPUs: 32000 Cedar, 47000 Béluga, 64000 Graham.
#SBATCH --time=12:00:00     # DD-HH:MM:SS
#SBATCH --tmp=500G
#SBATCH --output cube_resume_0
#nvidia-info
nvidia-smi

# Prepare virtualenv
module load StdEnv/2020 python/3.8.2
module load  gcc/9.3.0  opencv/4.5.1 cuda/11.4
module load scipy-stack/2020a
source ~/projects/def-jlalonde/prongs/dat/bin/activate
# You could also create your environment here, on the local storage ($SLURM_TMPDIR), for better performance. See our docs on virtual environments.

# #copy data
mkdir $SLURM_TMPDIR/data_new/
# tar xf ~/scratch/CVRG.tar.gz -C $SLURM_TMPDIR/data
# tar xf ~/scratch/Stanford.tar.gz -C $SLURM_TMPDIR/data
tar xf ~/scratch/stan.tar.gz -C $SLURM_TMPDIR/data_new
scp ~/scratch/deg_stan.pkl $SLURM_TMPDIR/data_new/semantic2d3d
# tar xf ~/scratch/wood_swin.tar.gz -C $SLURM_TMPDIR/data_new
# tar xf ~/scratch/hp.tar.gz -C $SLURM_TMPDIR/data_new

scp ~/scratch/swin_tiny_patch4_window7_224.pth $SLURM_TMPDIR/data_new/


pip -V

echo "Current working directory: `pwd`"
echo "Starting run at: `date`"
echo ""
echo "Job ID: $SLURM_JOB_ID"
echo ""

# Start training
# ./train_ellip_0_pre.sh
# ./train_swin_0_pre.sh
# ./train_ellip_1_pre.sh
# ./train_swin_1_pre.sh
# ./train_ellip_2_pre.sh
# ./train_swin_2_pre.sh
# ./train_ellip_3_pre.sh
# ./train_swin_3_pre.sh
# ./train_cube_0_pre.sh
# ./train_cube_1_pre.sh
# ./train_cube_2_pre.sh
# ./train_cube_3_pre.sh
./train_cube_0.sh
# ./train_cube_1.sh
# ./train_cube_2.sh
# ./train_cube_3.sh
# ./train_wood_da.sh
# ./train_wood_hp.sh
