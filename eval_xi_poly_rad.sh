#!/bin/bash 
export WANDB_MODE="disabled"
export NCCL_BLOCKING_WAIT=1 
export NCCL_DEBUG=INFO
export PYTHONFAULTHANDLER=1

for i in $(seq 0.0 0.05 0.0)
do 
    python -m torch.distributed.launch \
    --nproc_per_node 1 \
    --master_port 12347  main.py --eval \
    --cfg configs/poly/stanford_ellip_0.yaml \
    --data-path  /home-local2/akath.extra.nobkp/semantic2d3d \
    --resume /home-local2/akath.extra.nobkp/rad_unet_/stanford_norm_inv_low/default/ckpt_epoch_best_new_resume.pth \
    --output /home-local2/akath.extra.nobkp/swin_high/  \
    --fov 175.0 \
    --xi $i \
    --batch-size 4
done



