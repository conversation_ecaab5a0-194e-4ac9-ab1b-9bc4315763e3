# --------------------------------------------------------
# Swin Transformer
# Copyright (c) 2021 Microsoft
# Licensed under The MIT License [see LICENSE for details]
# Written by <PERSON><PERSON>
# --------------------------------------------------------

import os
from re import L
import torch
import numpy as np
import torch.distributed as dist
from torchvision import datasets, transforms
from timm.data.constants import IMAGENET_DEFAULT_MEAN, IMAGENET_DEFAULT_STD
from timm.data import Mixup
from timm.data import create_transform

from .cached_image_folder import CachedImageFolder
from .samplers import SubsetRandomSampler
from .Woodsc import Woodscape_dataset, RandomGenerator
from .Woodsc_swin import Woodscape_dataset_swin
from .Woodsc_hp import Woodscape_hp
from .Woodsc_depth import Woodscape_depth_dataset
from .Woodsc_depth_swin import Woodscape_depth_swin_dataset
from .stanford2d3d import <PERSON>
from .stanford2d3d_eval import Stanford_eval
from .stanford2d3d_da import Stanford_da
from .stanford2d3d_depth_da import Stanford_da_depth
from .stanford2d3d_depth import Stanford_depth
from .stanford2d3d_da_eval import Stanford_da_eval
from .stanford2d3d_cubemap import Stanford_cubemap
from .stanford2d3d_ERP import Stanford_erp
from .stanford_hp_ import Stanford_hp
from .stanford2d3d_poly import Stanford_poly

try:
    from torchvision.transforms import InterpolationMode


    def _pil_interp(method):
        if method == 'bicubic':
            return InterpolationMode.BICUBIC
        elif method == 'lanczos':
            return InterpolationMode.LANCZOS
        elif method == 'hamming':
            return InterpolationMode.HAMMING
        else:
            # default bilinear, do we want to allow nearest?
            return InterpolationMode.BILINEAR


    import timm.data.transforms as timm_transforms

    timm_transforms._pil_interp = _pil_interp
except:
    from timm.data.transforms import _pil_interp


def build_loader(config):
    config.defrost()
    dataset_train, config.MODEL.NUM_CLASSES = build_dataset(is_train=True, config=config)
    config.freeze()
    print(f"local rank {config.LOCAL_RANK} / global rank {dist.get_rank()} successfully build train dataset")
    
    dataset_val, _ = build_dataset(is_train=False, config=config)
    print(f"local rank {config.LOCAL_RANK} / global rank {dist.get_rank()} successfully build val dataset")

    num_tasks = dist.get_world_size()
    global_rank = dist.get_rank()
    if config.DATA.ZIP_MODE and config.DATA.CACHE_MODE == 'part':
        indices = np.arange(dist.get_rank(), len(dataset_train), dist.get_world_size())
        sampler_train = SubsetRandomSampler(indices)
    else:
        sampler_train = torch.utils.data.DistributedSampler(
            dataset_train, num_replicas=num_tasks, rank=global_rank, shuffle=True
        )

    if config.TEST.SEQUENTIAL:
        sampler_val = torch.utils.data.SequentialSampler(dataset_val)
    else:
        sampler_val = torch.utils.data.distributed.DistributedSampler(
            dataset_val, shuffle=False
        )


    data_loader_train = torch.utils.data.DataLoader(
        dataset_train, sampler=sampler_train,
        batch_size=config.DATA.BATCH_SIZE,
        num_workers=config.DATA.NUM_WORKERS,
        pin_memory=config.DATA.PIN_MEMORY,
        drop_last=True,
    )

    data_loader_val = torch.utils.data.DataLoader(
        dataset_val, sampler=sampler_val,
        batch_size=config.DATA.BATCH_SIZE,
        shuffle=False,
        num_workers=config.DATA.NUM_WORKERS,
        pin_memory=config.DATA.PIN_MEMORY,
        drop_last=False
    )



    # setup mixup / cutmix
    mixup_fn = None
    mixup_active = config.AUG.MIXUP > 0 or config.AUG.CUTMIX > 0. or config.AUG.CUTMIX_MINMAX is not None
    if mixup_active:
        mixup_fn = Mixup(
            mixup_alpha=config.AUG.MIXUP, cutmix_alpha=config.AUG.CUTMIX, cutmix_minmax=config.AUG.CUTMIX_MINMAX,
            prob=config.AUG.MIXUP_PROB, switch_prob=config.AUG.MIXUP_SWITCH_PROB, mode=config.AUG.MIXUP_MODE,
            label_smoothing=config.MODEL.LABEL_SMOOTHING, num_classes=config.MODEL.NUM_CLASSES)

    return dataset_train, dataset_val, data_loader_train, data_loader_val, mixup_fn


def build_dataset(is_train, config):
    if config.DATA.DATASET == 'Woodscapes':
        if is_train:
            dataset = Woodscape_dataset(config.DATA.DATA_PATH, split = 'train', img_size=config.DATA.IMG_SIZE_WOOD, transform = transforms.Compose([RandomGenerator(output_size=[config.DATA.IMG_SIZE, config.DATA.IMG_SIZE])]))
            nb_classes = 10
        else:
            dataset = Woodscape_dataset(config.DATA.DATA_PATH, split = 'val', img_size=config.DATA.IMG_SIZE_WOOD)
            nb_classes = 10
    elif config.DATA.DATASET == 'Woodscapes_swin':
        if is_train:
            dataset = Woodscape_dataset_swin(config.DATA.DATA_PATH, split = 'train', img_size=config.DATA.IMG_SIZE, transform = transforms.Compose([RandomGenerator(output_size=[config.DATA.IMG_SIZE, config.DATA.IMG_SIZE])]))
            nb_classes = 10
        else:
            dataset = Woodscape_dataset_swin(config.DATA.DATA_PATH, split = 'val', img_size=config.DATA.IMG_SIZE)
            nb_classes = 10
    elif config.DATA.DATASET == 'Woodscapes_hp':
        if is_train:
            dataset = Woodscape_hp(config.DATA.DATA_PATH, split = 'train', img_size=config.DATA.IMG_SIZE, transform = transforms.Compose([RandomGenerator(output_size=[config.DATA.IMG_SIZE, config.DATA.IMG_SIZE])]))
            nb_classes = 10
        else:
            dataset = Woodscape_hp(config.DATA.DATA_PATH, split = 'val', img_size=config.DATA.IMG_SIZE)
            nb_classes = 10
    elif config.DATA.DATASET == 'stanford_hp':
        # breakpoint()
        if is_train:
            dataset = Stanford_hp(config.DATA.DATA_PATH, split = 'train', xi=config.DATA.XI, fov = config.DATA.FOV, high = config.DATA.HIGH, low = config.DATA.LOW, img_size=config.DATA.IMG_SIZE, transform = transforms.Compose([RandomGenerator(output_size=[config.DATA.IMG_SIZE, config.DATA.IMG_SIZE])]))
            nb_classes = 14
        else:
            dataset = Stanford_hp(config.DATA.DATA_PATH, split = 'val', xi=config.DATA.XI, fov = config.DATA.FOV, high = config.DATA.HIGH, low = config.DATA.LOW, img_size=config.DATA.IMG_SIZE)
            nb_classes = 14

    elif config.DATA.DATASET == 'stanford':
        normalize= transforms.Normalize(
            mean= config.DATA.MEAN ,
            std= config.DATA.STD)
        if is_train:
            dataset = Stanford(config.DATA.DATA_PATH, normalize=normalize, split = 'train', n_rad = config.MODEL.NRADIUS, img_size=config.DATA.IMG_SIZE, num_classes=config.MODEL.NUM_CLASSES, xi = config.DATA.XI, fov = config.DATA.FOV, high = config.DATA.HIGH, low = config.DATA.LOW, transform = transforms.Compose([RandomGenerator(output_size=[config.DATA.IMG_SIZE, config.DATA.IMG_SIZE])]))
            nb_classes = config.MODEL.NUM_CLASSES
        else:
            dataset = Stanford(config.DATA.DATA_PATH, normalize=normalize, split = 'val', n_rad = config.MODEL.NRADIUS, img_size=config.DATA.IMG_SIZE, num_classes=config.MODEL.NUM_CLASSES, xi = config.DATA.XI, fov = config.DATA.FOV, high = config.DATA.HIGH, low = config.DATA.LOW, transform = None)
            nb_classes = config.MODEL.NUM_CLASSES
    elif config.DATA.DATASET == 'stanford_depth':
        normalize= transforms.Normalize(
            mean= config.DATA.MEAN ,
            std= config.DATA.STD)
        if is_train:
            dataset = Stanford_depth(config.DATA.DATA_PATH, normalize=normalize, split = 'train', n_rad = config.MODEL.NRADIUS, img_size=config.DATA.IMG_SIZE, num_classes=config.MODEL.NUM_CLASSES, xi = config.DATA.XI, fov = config.DATA.FOV, high = config.DATA.HIGH, low = config.DATA.LOW, transform = transforms.Compose([RandomGenerator(output_size=[config.DATA.IMG_SIZE, config.DATA.IMG_SIZE])]))
            nb_classes = config.MODEL.NUM_CLASSES
        else:
            dataset = Stanford_depth(config.DATA.DATA_PATH, normalize=normalize, split = 'val', n_rad = config.MODEL.NRADIUS, img_size=config.DATA.IMG_SIZE, num_classes=config.MODEL.NUM_CLASSES, xi = config.DATA.XI, fov = config.DATA.FOV, high = config.DATA.HIGH, low = config.DATA.LOW, transform = None)
            nb_classes = config.MODEL.NUM_CLASSES
    elif config.DATA.DATASET == 'stanford_eval':
        # breakpoint()
        normalize= transforms.Normalize(
            mean= config.DATA.MEAN ,
            std= config.DATA.STD )
        if is_train:
            dataset = Stanford_eval(config.DATA.DATA_PATH, normalize=normalize, split = 'train', n_rad = config.MODEL.NRADIUS, img_size=config.DATA.IMG_SIZE, num_classes=config.MODEL.NUM_CLASSES, xi = config.DATA.XI, fov = config.DATA.FOV, high = config.DATA.HIGH, low = config.DATA.LOW, transform = transforms.Compose([RandomGenerator(output_size=[config.DATA.IMG_SIZE, config.DATA.IMG_SIZE])]))
            nb_classes = config.MODEL.NUM_CLASSES
        else:
            dataset = Stanford_eval(config.DATA.DATA_PATH, normalize=normalize, split = 'test', n_rad = config.MODEL.NRADIUS, img_size=config.DATA.IMG_SIZE, num_classes=config.MODEL.NUM_CLASSES, xi = config.DATA.XI, fov = config.DATA.FOV, high = config.DATA.HIGH, low = config.DATA.LOW, transform = None)
            nb_classes = config.MODEL.NUM_CLASSES
    elif config.DATA.DATASET == 'stanford_da':
        normalize= transforms.Normalize(
            mean= config.DATA.MEAN ,
            std= config.DATA.STD )
        if is_train:
            dataset = Stanford_da(config.DATA.DATA_PATH, normalize=normalize, split = 'train', n_rad = config.MODEL.NRADIUS, img_size=config.DATA.IMG_SIZE, num_classes=config.MODEL.NUM_CLASSES, xi = config.DATA.XI, delta = config.DATA.DELTA, fov = config.DATA.FOV, high = config.DATA.HIGH, low = config.DATA.LOW, transform = transforms.Compose([RandomGenerator(output_size=[config.DATA.IMG_SIZE, config.DATA.IMG_SIZE])]))
            nb_classes = config.MODEL.NUM_CLASSES
        else:
            dataset = Stanford_da(config.DATA.DATA_PATH, normalize=normalize, split = 'test', n_rad = config.MODEL.NRADIUS, img_size=config.DATA.IMG_SIZE, num_classes=config.MODEL.NUM_CLASSES, xi = config.DATA.XI, delta = config.DATA.DELTA, fov = config.DATA.FOV, high = config.DATA.HIGH, low = config.DATA.LOW, transform = None)
            nb_classes = config.MODEL.NUM_CLASSES
    elif config.DATA.DATASET == 'Stanford_poly':
        normalize= transforms.Normalize(
            mean= config.DATA.MEAN ,
            std= config.DATA.STD )
        if is_train:
            dataset = Stanford_poly(config.DATA.DATA_PATH, normalize=normalize, split = 'train', n_rad = config.MODEL.NRADIUS, img_size=config.DATA.IMG_SIZE, num_classes=config.MODEL.NUM_CLASSES, xi = config.DATA.XI, delta = config.DATA.DELTA, fov = config.DATA.FOV, high = config.DATA.HIGH, low = config.DATA.LOW, transform = transforms.Compose([RandomGenerator(output_size=[config.DATA.IMG_SIZE, config.DATA.IMG_SIZE])]))
            nb_classes = config.MODEL.NUM_CLASSES
        else:
            dataset = Stanford_poly(config.DATA.DATA_PATH, normalize=normalize, split = 'test', n_rad = config.MODEL.NRADIUS, img_size=config.DATA.IMG_SIZE, num_classes=config.MODEL.NUM_CLASSES, xi = config.DATA.XI, delta = config.DATA.DELTA, fov = config.DATA.FOV, high = config.DATA.HIGH, low = config.DATA.LOW, transform = None)
            nb_classes = config.MODEL.NUM_CLASSES
    elif config.DATA.DATASET == 'stanford_da_depth':
        normalize= transforms.Normalize(
            mean= config.DATA.MEAN ,
            std= config.DATA.STD )
        if is_train:
            dataset = Stanford_da_depth(config.DATA.DATA_PATH, normalize=normalize, split = 'train', n_rad = config.MODEL.NRADIUS, img_size=config.DATA.IMG_SIZE, num_classes=config.MODEL.NUM_CLASSES, xi = config.DATA.XI, fov = config.DATA.FOV, high = config.DATA.HIGH, low = config.DATA.LOW, transform = transforms.Compose([RandomGenerator(output_size=[config.DATA.IMG_SIZE, config.DATA.IMG_SIZE])]))
            nb_classes = config.MODEL.NUM_CLASSES
        else:
            dataset = Stanford_da_depth(config.DATA.DATA_PATH, normalize=normalize, split = 'val', n_rad = config.MODEL.NRADIUS, img_size=config.DATA.IMG_SIZE, num_classes=config.MODEL.NUM_CLASSES, xi = config.DATA.XI, fov = config.DATA.FOV, high = config.DATA.HIGH, low = config.DATA.LOW, transform = None)
            nb_classes = config.MODEL.NUM_CLASSES
    elif config.DATA.DATASET == 'woodscape_depth':
        if is_train:
            dataset = Woodscape_depth_dataset(config.DATA.DATA_PATH, split = 'train', img_size=config.DATA.IMG_SIZE_WOOD, transform = transforms.Compose([RandomGenerator(output_size=[config.DATA.IMG_SIZE, config.DATA.IMG_SIZE])]))
            nb_classes = 10  # Placeholder for depth (single channel output)
        else:
            dataset = Woodscape_depth_dataset(config.DATA.DATA_PATH, split = 'val', img_size=config.DATA.IMG_SIZE_WOOD)
            nb_classes = 10
    elif config.DATA.DATASET == 'woodscape_depth_swin':
        if is_train:
            dataset = Woodscape_depth_swin_dataset(config.DATA.DATA_PATH, split = 'train', img_size=config.DATA.IMG_SIZE, transform = transforms.Compose([RandomGenerator(output_size=[config.DATA.IMG_SIZE, config.DATA.IMG_SIZE])]))
            nb_classes = 10  # Placeholder for depth (single channel output)
        else:
            dataset = Woodscape_depth_swin_dataset(config.DATA.DATA_PATH, split = 'val', img_size=config.DATA.IMG_SIZE)
            nb_classes = 10
    elif config.DATA.DATASET == 'stanford_da_eval':
        normalize= transforms.Normalize(
            mean= config.DATA.MEAN ,
            std= config.DATA.STD )
        if is_train:
            dataset = Stanford_da_eval(config.DATA.DATA_PATH, normalize=normalize, split = 'train', n_rad = config.MODEL.NRADIUS, img_size=config.DATA.IMG_SIZE, num_classes=config.MODEL.NUM_CLASSES, xi = config.DATA.XI, fov = config.DATA.FOV, high = config.DATA.HIGH, low = config.DATA.LOW, transform = transforms.Compose([RandomGenerator(output_size=[config.DATA.IMG_SIZE, config.DATA.IMG_SIZE])]))
            nb_classes = config.MODEL.NUM_CLASSES
        else:
            dataset = Stanford_da_eval(config.DATA.DATA_PATH, normalize=normalize, split = 'test', n_rad = config.MODEL.NRADIUS, img_size=config.DATA.IMG_SIZE, num_classes=config.MODEL.NUM_CLASSES, xi = config.DATA.XI, fov = config.DATA.FOV, high = config.DATA.HIGH, low = config.DATA.LOW, transform = None)
            nb_classes = config.MODEL.NUM_CLASSES
    elif config.DATA.DATASET == 'stanford_cubemap':
        normalize= transforms.Normalize(
            mean= config.DATA.MEAN ,
            std= config.DATA.STD )
        if is_train:
            dataset = Stanford_cubemap(config.DATA.DATA_PATH, normalize=normalize, split = 'train', n_rad = config.MODEL.NRADIUS, img_size=config.DATA.IMG_SIZE, num_classes=config.MODEL.NUM_CLASSES, xi = config.DATA.XI, fov = config.DATA.FOV, high = config.DATA.HIGH, low = config.DATA.LOW, transform = transforms.Compose([RandomGenerator(output_size=[config.DATA.IMG_SIZE, config.DATA.IMG_SIZE])]))
            nb_classes = config.MODEL.NUM_CLASSES
        else:
            dataset = Stanford_cubemap(config.DATA.DATA_PATH, normalize=normalize, split = 'test', n_rad = config.MODEL.NRADIUS, img_size=config.DATA.IMG_SIZE, num_classes=config.MODEL.NUM_CLASSES, xi = config.DATA.XI, fov = config.DATA.FOV, high = config.DATA.HIGH, low = config.DATA.LOW, transform = None)
            nb_classes = config.MODEL.NUM_CLASSES
    elif config.DATA.DATASET == 'stanford_erp':
        normalize= transforms.Normalize(
            mean= config.DATA.MEAN ,
            std= config.DATA.STD )
        if is_train:
            dataset = Stanford_erp(config.DATA.DATA_PATH, normalize=normalize, split = 'train', n_rad = config.MODEL.NRADIUS, img_size=config.DATA.IMG_SIZE, num_classes=config.MODEL.NUM_CLASSES, xi = config.DATA.XI, fov = config.DATA.FOV, high = config.DATA.HIGH, low = config.DATA.LOW, transform = transforms.Compose([RandomGenerator(output_size=[config.DATA.IMG_SIZE, config.DATA.IMG_SIZE])]))
            nb_classes = config.MODEL.NUM_CLASSES
        else:
            dataset = Stanford_erp(config.DATA.DATA_PATH, normalize=normalize, split = 'val', n_rad = config.MODEL.NRADIUS, img_size=config.DATA.IMG_SIZE, num_classes=config.MODEL.NUM_CLASSES, xi = config.DATA.XI, fov = config.DATA.FOV, high = config.DATA.HIGH, low = config.DATA.LOW, transform = None)
            nb_classes = config.MODEL.NUM_CLASSES
    else:
        raise NotImplementedError("We only support ImageNet Now.")

    return dataset, nb_classes
