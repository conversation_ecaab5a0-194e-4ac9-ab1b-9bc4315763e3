import os
import numpy as np
import torch
from torchvision import transforms
from torch.utils.data import Dataset
from torch.utils.data import Data<PERSON>oader
import json
from PIL import Image
import pickle as pkl
import torch.nn.functional as F
# from utils_theta_inverse import concentric_dic_sampling, DA_grid_inv_
import torch.nn as nn
from PIL import Image
import tqdm
import healpy as hp
import scipy

# Transpose.FLIP_LEFT_RIGHT 

T = transforms.ToTensor()
pil = transforms.ToPILImage()


normalize = transforms.Normalize(
            mean=[0.2699, 0.2800, 0.2869],
            std=[0.2276, 0.2298, 0.2390])

def imresize(im, size, interp='bilinear'):
    if interp == 'nearest':
        resample = Image.NEAREST
    elif interp == 'bilinear':
        resample = Image.BILINEAR
    elif interp == 'bicubic':
        resample = Image.BICUBIC
    else:
        raise Exception('resample method undefined!')

    return im.resize(size, resample)



def img_transform(img):
    # 0-255 to 0-1
    img = np.float32(np.array(img))
    img = img.transpose((2, 0, 1))
    # img = normalize(torch.from_numpy(img.copy()))
    return img

def segm_transform(segm):
    # to tensor, -1 to 149
    segm = torch.from_numpy(np.array(segm)).long()
    return segm

################### healpix dataset generation ############################################
def save_metadata(save_dir, theta, phi):
    metadata = {
        "nside": 64,
        "base_pix": 8,
        "grid_type": "HEALPix",
        "part": 'val'
    }
    with open(os.path.join(save_dir, "metadata.json"), "w") as f:
        json.dump(metadata, f)
    np.savez(os.path.join(save_dir, "grid.npz"), theta, phi)

def sample_within_bounds(signal, x, y, bounds, background_value):
    """from the original S2CNN code"""
    xmin, xmax, ymin, ymax = bounds

    idxs = (xmin <= x) & (x < xmax) & (ymin <= y) & (y < ymax)

    if len(signal.shape) > 2:
        # import pdb; pdb.set_trace()
        sample = np.full((signal.shape[0], *x.shape), background_value)
        sample[:, idxs] = signal[:, x[idxs], y[idxs]]
    else:
        sample = np.full(x.shape, background_value)
        sample[idxs] = signal[x[idxs], y[idxs]]
    return sample

def sample_bilinear(signal, rx, ry):
    """adapted from the original S2CNN code

    note: since we use the first dimension of signal as x and the second as y, x corresponds to v
    and y to u
    """

    signal_dim_x = signal.shape[1]
    signal_dim_y = signal.shape[2]

    # discretize sample position
    # ix = rx.astype(int)
    # iy = ry.astype(int)
    # obtain four sample coordinates
    ix0 = np.floor(rx).astype(int)
    iy0 = np.floor(ry).astype(int)
    ix1 = np.ceil(rx).astype(int)
    iy1 = np.ceil(ry).astype(int)

    bounds = (0, signal_dim_x, 0, signal_dim_y)

    # sample signal at each four positions
    signal_00 = sample_within_bounds(signal, ix0, iy0, bounds, 0)
    signal_10 = sample_within_bounds(signal, ix1, iy0, bounds, 0)
    signal_01 = sample_within_bounds(signal, ix0, iy1, bounds, 0)
    signal_11 = sample_within_bounds(signal, ix1, iy1, bounds, 0)
    # breakpoint()
    # linear interpolation in x-direction
    fx1 = (ix1 - rx) * signal_00 + (rx - ix0) * signal_10
    fx2 = (ix1 - rx) * signal_01 + (rx - ix0) * signal_11

    # linear interpolation in y-direction
    return (iy1 - ry) * fx1 + (ry - iy0) * fx2


def sample_mask(mask, u, v, s2_bkgd_class):
    bounds = (0, mask.shape[0], 0, mask.shape[1])
    u_int = np.around(u, 0).astype(int)
    v_int = np.around(v, 0).astype(int)
    return sample_within_bounds(mask, u_int, v_int, bounds, s2_bkgd_class).astype(np.uint8)

def rot_grid(theta, phi, cal_info, inv=False):
    r = scipy.spatial.transform.Rotation.from_quat(cal_info["extrinsic"]["quaternion"])
    if cal_info["name"] == "FV":
        ext_ref = [1, 0, 0]
    elif cal_info["name"] == "RV":
        ext_ref = [-1, 0, 0]
    elif cal_info["name"] == "MVL":
        ext_ref = [0, 1, 0]
    elif cal_info["name"] == "MVR":
        ext_ref = [0, -1, 0]
    int_ref = r.inv().apply(ext_ref)
    phi_ref = np.arctan2(int_ref[1], int_ref[0])
    theta_ref = np.arccos(int_ref[2])
    r_grid = scipy.spatial.transform.Rotation.from_euler("yz", [theta_ref, phi_ref])
    if inv:
        r_grid = r_grid.inv()
    x = (np.cos(phi) * np.sin(theta)).reshape(-1)
    y = (np.sin(phi) * np.sin(theta)).reshape(-1)
    z = (np.cos(theta)).reshape(-1)
    xyz_rot = r_grid.apply(np.stack((x, y, z), axis=-1))
    phi_rot = np.arctan2(xyz_rot[:, 1], xyz_rot[:, 0])
    # arctan2 takes values in [-pi,pi], so move this back to [0,2pi] as in the DH grid
    # phi_rot[phi_rot < 0] = phi_rot[phi_rot < 0] + 2 * np.pi
    theta_rot = np.arccos(xyz_rot[:, 2])
    phi_rot = phi_rot.reshape(phi.shape)
    theta_rot = theta_rot.reshape(theta.shape)

    return theta_rot, phi_rot

def project_s2_points_to_img_cached(
    theta, phi, theta_shape, aspect_ratio, cx_offset, cy_offset, width, height, poly_order, ks
):
    theta = np.fromstring(theta).reshape(theta_shape)
    phi = np.fromstring(phi).reshape(theta_shape)

    rho = 0
    rho_max = 0
    for order in range(1, poly_order + 1):
        rho += ks[order - 1] * theta**order
    rho = rho/rho.max()

    u = rho * np.cos(phi)
    v = rho * np.sin(phi)

    return u*96 + 96, v*96 + 96

def project_s2_points_to_img(theta, phi, cal_info, rotate_pole):
    """Returns pixel coordinates (floats) corresponding to spherical points"""

    if rotate_pole:
        theta, phi = rot_grid(theta, phi, cal_info, inv=False)

    # Make hashable for caching:
    aspect_ratio = cal_info["intrinsic"]["aspect_ratio"]
    cx_offset = cal_info["intrinsic"]["cx_offset"]
    cy_offset = cal_info["intrinsic"]["cy_offset"]
    width = 192
    height = 192
    poly_order = cal_info["intrinsic"]["poly_order"]
    ks = tuple([cal_info["intrinsic"]["k" + str(order)] for order in range(1, poly_order + 1)])

    u, v = project_s2_points_to_img_cached(
        theta=theta.tostring(),
        phi=phi.tostring(),
        theta_shape=theta.shape,
        aspect_ratio=aspect_ratio,
        cx_offset=cx_offset,
        cy_offset=cy_offset,
        width=width,
        height=height,
        poly_order=poly_order,
        ks=ks,
    )

    return u, v

def project_dataset_hp(dataset):
    # breakpoint()
    img_save_dir = '/home-local2/akath.extra.nobkp/woodscapes_hp'
    os.makedirs(img_save_dir, exist_ok=True)
    max_idx = len(dataset) 
    npix = hp.pixelfunc.nside2npix(64)
    ipix = np.arange(npix)
    theta, phi = hp.pixelfunc.pix2ang(64, ipix, nest=True)

    half_idcs = np.arange(npix * 8 // 12)
    theta = theta[half_idcs]
    phi = phi[half_idcs]

    save_metadata(img_save_dir, theta, phi)

    for idx in range(max_idx):
        # breakpoint()
        print(idx)
        img, mask, cal_info, file_name = dataset[idx]
        u, v = project_s2_points_to_img(theta, phi, cal_info, False)
        # breakpoint()
        hp_img = sample_bilinear(img.transpose(2, 0, 1), v, u).astype(np.uint8)
        hp_mask = sample_mask(mask, v, u, 0)
        # breakpoint()
        np.savez(
            os.path.join(img_save_dir, file_name + ".npz"),
            hp_img=hp_img,
            hp_mask=hp_mask,
        )

    # if args.plot_last_on_s2:
    #     overlay = utils.get_overlay(args.woodscape_version, hp_mask, hp_img)
    #     pole_adjusted = "_pole_adjusted" if args.rotate_pole else ""
    #     base_pix = f"_base_pix={args.base_pix}"
    #     save_name = file_name + f"_on_s2_nside={args.nside}{base_pix}{pole_adjusted}.png"
    #     save_path = os.path.join(img_save_dir, save_name)
    #     healpy_utils.plot_hp_img(
    #         torch.tensor(overlay), npix, save_path, projection="orthview", n_colors=12
    #     )
################### healpix dataset generation ############################################
# H, W = (128, 128)
# x = torch.linspace(0, H, H+1) - H//2 - 0.5
# y = torch.linspace(0, W, W+1) - H//2 - 0.5
# grid_x, grid_y = torch.meshgrid(x[1:], y[1:])
# x_ = grid_x.reshape(H*H, 1)
# y_ = grid_y.reshape(W*W, 1)
# grid_pix = torch.cat((x_, y_), dim=1)
# grid_pix = grid_pix.reshape(1, H*W, 2)



class Woodscape_dataset(Dataset):
    def __init__(self, base_dir, split, img_size = (192, 192), transform=None):
        self.transform = transform  # using transform in torch!
        self.split = split
        
        if split == 'train':
            with open(base_dir + '/train.json', 'r') as f:
                data = json.load(f)
        elif split == 'val':
            with open(base_dir + '/val.json', 'r') as f:
                data = json.load(f)
        elif split == 'test':
            with open(base_dir + '/test.json', 'r') as f:
                data = json.load(f)

        self.data = data
        self.data_dir = base_dir
        self.img_size = img_size

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        name = self.data[idx]
        img_path = self.data_dir + '/rgb_images/' + self.data[idx]
        lbl_path = self.data_dir + '/gtLabels/' + self.data[idx]
        calib_path = self.data_dir + '/calibration/' + self.data[idx][:-4] + '.json'

        with open(calib_path, 'r') as f:
            calib = json.load(f)

        img = Image.open(img_path).convert('RGB')
        segm = Image.open(lbl_path).convert('L')
    

        # dist = torch.tensor([k1, k2, k3, k4], dtype=torch.float32)

        assert(segm.mode == "L")
        assert(img.size[0] == segm.size[0])
        assert(img.size[1] == segm.size[1])
        
        img = imresize(img, (self.img_size), interp='bilinear')
        segm = imresize(segm, (self.img_size), interp='nearest')

        image = np.array(img)
        segm = np.array(segm)

        sample = {'image': image, 'label': segm}
         
        return sample['image'], sample['label'], calib, self.data[idx][:-4]

def get_mean_std(base_dir ):
    db= Woodscape_dataset(base_dir, split="train", transform=None)
    print(len(db))
    #sample= db.__getitem__(0)
    #print(sample['image'].shape)
    #print(sample['label'].shape)
    #print(sample['dist'].shape)
    loader = DataLoader(db, batch_size=len(db), shuffle=False,num_workers=0)
    im_lab_dict = next(iter(loader))
    images, labels, dist, cls, mask, one_hot = im_lab_dict
    # shape of images = [b,c,w,h]
    mean, std = images.mean([0,2,3]), images.std([0,2,3])
    print("mean",mean)
    print("std", std)
    return mean , std



if __name__=='__main__':
    db_train = Woodscape_dataset(base_dir="/home-local2/akath.extra.nobkp/woodscapes", split="val")
    
    project_dataset_hp(db_train)    

    # data_loader_train = torch.utils.data.DataLoader(
    #     db_train,
    #     batch_size=8,
    #     num_workers=3,
    #     pin_memory=True,
    #     drop_last=True,
    # )
    # psum = 0
    # psum_sq = 0
    # for i_batch, sampled_batch in enumerate(data_loader_train):
    #     images, labels, dist, cls, mask, one_hot = sampled_batch
    #     # breakpoint()
    #     # pil(images[0]).save('pol.png')
    #     # print(i_batch)
    #     psum  += images.sum(axis = [0, 2, 3])
    #     psum_sq += (images ** 2).sum(axis = [0, 2, 3])
    # # breakpoint()
    # count = len(db_train)*192*192
    # total_mean = psum / count
    # total_var  = (psum_sq / count) - (total_mean ** 2)
    # total_std  = torch.sqrt(total_var)
    # print(total_std, total_mean)