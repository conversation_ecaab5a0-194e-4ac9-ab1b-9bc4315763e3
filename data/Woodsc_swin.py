import os
import random
import h5py
import numpy as np
import torch
from torchvision import transforms
from scipy import ndimage
from scipy.ndimage.interpolation import zoom
from torch.utils.data import Dataset
from torch.utils.data import DataLoader
import json
from PIL import Image
import pickle as pkl
import torch.nn.functional as F
import cv2
# Transpose.FLIP_LEFT_RIGHT 

T = transforms.ToTensor()
pil = transforms.ToPILImage()

res_bi = transforms.Resize(size=(384, 384), interpolation=Image.BILINEAR)
res_n = transforms.Resize(size=(384, 384), interpolation=Image.NEAREST)

normalize = transforms.Normalize(
            mean=[0.3187, 0.3283, 0.3381],
            std=[0.2148, 0.2167, 0.2248])

def imresize(im, size, interp='bilinear'):
    if interp == 'nearest':
        resample = Image.NEAREST
    elif interp == 'bilinear':
        resample = Image.BILINEAR
    elif interp == 'bicubic':
        resample = Image.BICUBIC
    else:
        raise Exception('resample method undefined!')

    return im.resize(size, resample)

def random_rot_flip(image, label):
    k = np.random.randint(0, 4)
    image = torch.rot90(image, k, dims=[1, 2])
    label = torch.rot90(label, k, dims=[0, 1])
    axis = np.random.randint(1, 3)
    image = torch.flip(image, dims=[1,2])
    label = torch.flip(label, dims=[0,1])
    return image, label


def random_rotate(image, label):
    angle = np.random.randint(-20, 20)
    image = transforms.functional.rotate(image, angle)
    label = transforms.functional.rotate(label.reshape(1, label.shape[0], label.shape[1]), angle)
    return image, label[0]


def img_transform(img):
    # 0-255 to 0-1
    img = np.float32(np.array(img))
    img = img.transpose((2, 0, 1))
    # img = normalize(torch.from_numpy(img.copy()))
    return img

def segm_transform(segm):
    # to tensor, -1 to 149
    segm = torch.from_numpy(np.array(segm)).long()
    return segm

# H, W = (128, 128)
# x = torch.linspace(0, H, H+1) - H//2 - 0.5
# y = torch.linspace(0, W, W+1) - H//2 - 0.5
# grid_x, grid_y = torch.meshgrid(x[1:], y[1:])
# x_ = grid_x.reshape(H*H, 1)
# y_ = grid_y.reshape(W*W, 1)
# grid_pix = torch.cat((x_, y_), dim=1)
# grid_pix = grid_pix.reshape(1, H*W, 2)

class RandomGenerator(object):
    def __init__(self, output_size):
        self.output_size = output_size

    def __call__(self, sample):
        image, label = sample['image'], sample['label']

        if random.random() > 0.5:
            image, label = random_rot_flip(image, label)
        elif random.random() > 0.5:
            image, label = random_rotate(image, label)
        # print(image.shape, label.shape, type(image), type(label))
        # x, y = image.shape
        # if x != self.output_size[0] or y != self.output_size[1]:
        #     image = zoom(image, (self.output_size[0] / x, self.output_size[1] / y), order=3)  # why not 3?
        #     label = zoom(label, (self.output_size[0] / x, self.output_size[1] / y), order=0)
        # image = torch.from_numpy(image.astype(np.float32)).unsqueeze(0)
        # label = torch.from_numpy(label.astype(np.float32))
        sample = {'image': image.type(torch.float32), 'label': label.type(torch.uint8)}
        return sample


class Woodscape_dataset_swin(Dataset):
    def __init__(self, base_dir, split, img_size = 192, transform=None):
        self.transform = transform  # using transform in torch!
        self.split = split
        
        if split == 'train':
            with open(base_dir + '/train.json', 'r') as f:
                data = json.load(f)
        elif split == 'val':
            with open(base_dir + '/val.json', 'r') as f:
                data = json.load(f)
        elif split == 'test':
            with open(base_dir + '/test.json', 'r') as f:
                data = json.load(f)
        with open(base_dir + '/calib.pkl', 'rb') as f:
            calib = pkl.load(f)

        self.calib = calib
        self.data = data
        self.data_dir = base_dir
        self.img_size = img_size

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):

        name = self.data[idx]
        img_path = self.data_dir + '/rgb_images/' + self.data[idx]
        lbl_path = self.data_dir + '/gtLabels/' + self.data[idx]
        img = Image.open(img_path).convert('RGB')
        img = img.resize((self.img_size, self.img_size), resample = 2)
        img = np.array(img)
        img = img[28:-28, 5:-5]
        img = cv2.resize(img, (self.img_size, self.img_size),interpolation = cv2.INTER_LINEAR).astype(np.uint8)
        img = Image.fromarray(img)
        # pil(img).save("wood.png")
        # breakpoint()

        segm = Image.open(lbl_path).convert('L')
        segm = segm.resize((self.img_size, self.img_size), resample = 0)
        segm = np.array(segm)
        segm = np.array(segm)
        segm = segm[28:-28, 5:-5]
        segm= cv2.resize(segm, (self.img_size, self.img_size), interpolation = cv2.INTER_NEAREST)
        segm = Image.fromarray(segm).convert('L')
        # img = img.resize((768, 640))
        # segm = segm.resize((768, 640), resample = 0)
        # import pdb;pdb.set_trace()
        # segm = imresize(segm, (768, 640), interp='nearest')

        # np_im = np.array(img)
        # print(img.size)
        # img = Image.fromarray(np_im[192:-192, 35:-35])
        # print(img.size)
        # np_sem = np.array(segm)
        # segm = Image.fromarray(np_sem[192:-192, 35:-35])

        key_calib = self.data[idx][:-4] + "_img.png"
        # breakpoint()
        dist = self.calib[key_calib]

        # dist = torch.tensor([k1, k2, k3, k4], dtype=torch.float32)

        assert(segm.mode == "L")
        assert(img.size[0] == segm.size[0])
        assert(img.size[1] == segm.size[1])

            # random_flip
        # if np.random.choice([0, 1]):
        #     img = img.transpose(Image.FLIP_LEFT_RIGHT)
        #     segm = segm.transpose(Image.FLIP_LEFT_RIGHT)

        # import pdb;pdb.set_trace()
        # note that each sample within a mini batch has different scale param
        # import pdb;pdb.set_trace()
        image = T(img)
        # image = res_bi(image)
        label = segm_transform(segm)
        # label = res_n(label.unsqueeze(0))
        label = label.squeeze(0)
        ############################################# masks ############################################################
        res = 1024
        cartesian = torch.cartesian_prod(
            torch.linspace(-1, 1, res),
            torch.linspace(1, -1, res)
        ).reshape(res, res, 2).transpose(2, 1).transpose(1, 0).transpose(1, 2)
        radius = cartesian.norm(dim=0)
        mask = (radius > 0.0) & (radius < 1) 
        mask1 = torch.nn.functional.interpolate(mask.unsqueeze(0).unsqueeze(0) * 1.0, (self.img_size), mode="nearest")
        ############################################# masks ############################################################

        sample = {'image': image, 'label': label}
        if self.transform:
        # if None:
            sample = self.transform(sample)
        else:
            sample['image']= image.type(torch.float32)
            sample['label']= label.type(torch.uint8)
        # print(sample['label'].max())
        one_hot = F.one_hot(sample['label'].to(torch.int64), num_classes=10).to(torch.float32)
        sample['dist'] = dist
        if normalize is not None:
            sample['image']= normalize(sample['image'])
        sample['one_hot'] = one_hot
        sample['mask'] = mask1[0].to(torch.long)

        grid_ = torch.tensor(0)
        return sample['image'], sample['label'], sample['dist'], grid_, sample['mask'], one_hot

def get_mean_std(base_dir ):
    db= Woodscape_dataset_swin(base_dir, split="train", transform=None)
    print(len(db))
    #sample= db.__getitem__(0)
    #print(sample['image'].shape)
    #print(sample['label'].shape)
    #print(sample['dist'].shape)
    loader = DataLoader(db, batch_size=len(db), shuffle=False,num_workers=0)
    im_lab_dict = next(iter(loader))
    images, labels, dist, cls, mask, one_hot = im_lab_dict
    # shape of images = [b,c,w,h]
    mean, std = images.mean([0,2,3]), images.std([0,2,3])
    print("mean",mean)
    print("std", std)
    return mean , std



if __name__=='__main__':
    db_train = Woodscape_dataset_swin(base_dir="/home-local2/akath.extra.nobkp/woodscapes", split="train")
    # db_train[10]
    trainloader = DataLoader(db_train, batch_size=8, shuffle=True, num_workers=1, pin_memory=True)
    psum = 0
    psum_sq = 0
    for i_batch, sampled_batch in enumerate(trainloader):
        images, labels, dist, cls, mask, one_hot = sampled_batch
        # breakpoint()
        psum  += images.sum(axis = [0, 2, 3])
        psum_sq += (images ** 2).sum(axis = [0, 2, 3])
    # breakpoint()
    count = len(db_train)*192*192
    total_mean = psum / count
    total_var  = (psum_sq / count) - (total_mean ** 2)
    total_std  = torch.sqrt(total_var)

    # mean,std= get_mean_std(base_dir="/home-local2/akath.extra.nobkp/woodscape")
    m = db_train[0]
    import pdb;pdb.set_trace()
    print("ass")
