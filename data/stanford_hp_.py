import os
import numpy as np
import torch
from torchvision import transforms
from torch.utils.data import Dataset
from torch.utils.data import DataLoader
import json
from PIL import Image
import pickle as pkl
import torch.nn.functional as F
# from utils_theta_inverse import concentric_dic_sampling, DA_grid_inv_
import torch.nn as nn
from PIL import Image
import tqdm
import scipy
from envmap import EnvironmentMap
from envmap import rotation_matrix
import os
os.environ["OPENCV_IO_ENABLE_OPENEXR"]="1"
import cv2
import random
import numpy as np
import torch
from torchvision import transforms
from scipy import ndimage
from scipy.ndimage.interpolation import zoom
from torch.utils.data import Dataset
from torch.utils.data import DataLoader
from skimage.transform import resize
import torchvision
import json
import pickle as pkl 
from PIL import Image
import torch.nn.functional as F
from utils_curve_inverse import concentric_dic_sampling, DA_grid_inv_
import torch.nn as nn
import healpy as hp

# Transpose.FLIP_LEFT_RIGHT 

T = transforms.ToTensor()
pil = transforms.ToPILImage()


normalize = transforms.Normalize(
            mean=[0.2392, 0.2417, 0.2433],
            std=[0.2455, 0.2463, 0.2527])

def imresize(im, size, interp='bilinear'):
    if interp == 'nearest':
        resample = Image.NEAREST
    elif interp == 'bilinear':
        resample = Image.BILINEAR
    elif interp == 'bicubic':
        resample = Image.BICUBIC
    else:
        raise Exception('resample method undefined!')

    return im.resize(size, resample)



def img_transform(img):
    # 0-255 to 0-1
    img = np.float32(np.array(img))
    img = img.transpose((2, 0, 1))
    # img = normalize(torch.from_numpy(img.copy()))
    return img

def segm_transform(segm):
    # to tensor, -1 to 149
    segm = torch.from_numpy(np.array(segm)).long()
    return segm

def sph2cart(az, el, r):
    x = r * np.cos(el) * np.cos(az)
    y = r * np.cos(el) * np.sin(az)
    z = r * np.sin(el)
    return x, y, z

#rad
def compute_focal(fov, xi, width):
    return width / 2 * (xi + np.cos(fov/2)) / np.sin(fov/2)

def warpToFisheye(pano,outputdims,viewingAnglesPYR=[np.deg2rad(0), np.deg2rad(0), np.deg2rad(0)],xi=0.9, fov=150, order=1):

    outputdims1=outputdims[0]
    outputdims2=outputdims[1]
   
    pitch, yaw, roll = np.array(viewingAnglesPYR)
    #print(pano.shape)
    # breakpoint()
    e = EnvironmentMap(pano, format_='latlong')
    e = e.rotate(rotation_matrix(yaw, -pitch, -roll).T)
    r_max = max(outputdims1/2,outputdims2/2)

    h= min(outputdims1,outputdims2)
    f = compute_focal(np.deg2rad(fov),xi,h)
    
    t = np.linspace(0,fov/2, 100)
   

    #test spherical
    # print('xi  {}, f {}'.format(xi,f))
    theta= np.deg2rad(t)
    funT = (f* np.sin(theta))/(np.cos(theta)+xi)
    funT= funT/r_max


    #creates the empty image
    [u, v] = np.meshgrid(np.linspace(-1, 1, outputdims1), np.linspace(-1, 1, outputdims2))
    r = np.sqrt(u ** 2 + v ** 2)
    phi = np.arctan2(v, u)
    validOut = r <= 1
    # interpolate the _inverse_ function!
    fovWorld = np.deg2rad(np.interp(x=r, xp=funT, fp=t))
    # fovWorld = np.pi / 2 - np.arccos(r)
    FOV = np.rad2deg((fovWorld))

    el = fovWorld + np.pi / 2

    # convert to XYZ
    #ref
    x, y, z = sph2cart(phi, fovWorld + np.pi / 2, 1)

    x = -x
    z = -z

    #return values in [0,1]
    #the source pixel from panorama 
    [u1, v1] = e.world2image(x, y, z)
    # breakpoint()
    # Interpolate
    #validout to set the background to black (the circle part)
    eOut= e.interpolate(u1, v1, validOut, order)
    #eOut= e.interpolate(u1, v1)

    return eOut.data, f

################### healpix dataset generation ############################################
def sample_within_bounds(signal, x, y, bounds, background_value):
    """from the original S2CNN code"""
    xmin, xmax, ymin, ymax = bounds

    idxs = (xmin <= x) & (x < xmax) & (ymin <= y) & (y < ymax)

    if len(signal.shape) > 2:
        # import pdb; pdb.set_trace()
        sample = np.full((signal.shape[0], *x.shape), background_value)
        sample[:, idxs] = signal[:, x[idxs], y[idxs]]
    else:
        sample = np.full(x.shape, background_value)
        sample[idxs] = signal[x[idxs], y[idxs]]
    return sample

def sample_bilinear(signal, rx, ry):
    """adapted from the original S2CNN code

    note: since we use the first dimension of signal as x and the second as y, x corresponds to v
    and y to u
    """

    signal_dim_x = signal.shape[1]
    signal_dim_y = signal.shape[2]

    # discretize sample position
    # ix = rx.astype(int)
    # iy = ry.astype(int)
    # obtain four sample coordinates
    ix0 = np.floor(rx).astype(int)
    iy0 = np.floor(ry).astype(int)
    ix1 = np.ceil(rx).astype(int)
    iy1 = np.ceil(ry).astype(int)

    bounds = (0, signal_dim_x, 0, signal_dim_y)

    # sample signal at each four positions
    signal_00 = sample_within_bounds(signal, ix0, iy0, bounds, 0)
    signal_10 = sample_within_bounds(signal, ix1, iy0, bounds, 0)
    signal_01 = sample_within_bounds(signal, ix0, iy1, bounds, 0)
    signal_11 = sample_within_bounds(signal, ix1, iy1, bounds, 0)
    # breakpoint()
    # linear interpolation in x-direction
    fx1 = (ix1 - rx) * signal_00 + (rx - ix0) * signal_10
    fx2 = (ix1 - rx) * signal_01 + (rx - ix0) * signal_11

    # linear interpolation in y-direction
    return (iy1 - ry) * fx1 + (ry - iy0) * fx2


def sample_mask(mask, u, v, s2_bkgd_class):
    bounds = (0, mask.shape[0], 0, mask.shape[1])
    u_int = np.around(u, 0).astype(int)
    v_int = np.around(v, 0).astype(int)
    return sample_within_bounds(mask, u_int, v_int, bounds, s2_bkgd_class).astype(np.uint8)

def rot_grid(theta, phi, cal_info, inv=False):
    r = scipy.spatial.transform.Rotation.from_quat(cal_info["extrinsic"]["quaternion"])
    if cal_info["name"] == "FV":
        ext_ref = [1, 0, 0]
    elif cal_info["name"] == "RV":
        ext_ref = [-1, 0, 0]
    elif cal_info["name"] == "MVL":
        ext_ref = [0, 1, 0]
    elif cal_info["name"] == "MVR":
        ext_ref = [0, -1, 0]
    int_ref = r.inv().apply(ext_ref)
    phi_ref = np.arctan2(int_ref[1], int_ref[0])
    theta_ref = np.arccos(int_ref[2])
    r_grid = scipy.spatial.transform.Rotation.from_euler("yz", [theta_ref, phi_ref])
    if inv:
        r_grid = r_grid.inv()
    x = (np.cos(phi) * np.sin(theta)).reshape(-1)
    y = (np.sin(phi) * np.sin(theta)).reshape(-1)
    z = (np.cos(theta)).reshape(-1)
    xyz_rot = r_grid.apply(np.stack((x, y, z), axis=-1))
    phi_rot = np.arctan2(xyz_rot[:, 1], xyz_rot[:, 0])
    # arctan2 takes values in [-pi,pi], so move this back to [0,2pi] as in the DH grid
    # phi_rot[phi_rot < 0] = phi_rot[phi_rot < 0] + 2 * np.pi
    theta_rot = np.arccos(xyz_rot[:, 2])
    phi_rot = phi_rot.reshape(phi.shape)
    theta_rot = theta_rot.reshape(theta.shape)

    return theta_rot, phi_rot


def f(x, n=5.0, a=3.48, b=8.31578947368421):
    return b*torch.pow(x/a, n)
def h(x, m=2.288135593220339, a=3.48):
    return -torch.pow(-x/a + 1, m) + 1
def g(x, m=2.288135593220339, n= 5.0, a=3.48, b=8.31578947368421, c=0.3333333333333333):
    return x

def g_inv(y, theta_d_max):
    # Create test_x values
    test_x = torch.linspace(0, theta_d_max, 1000)  # Reduced number of points
    test_y = g(test_x, a=theta_d_max)

    # Use torch.searchsorted for efficient binary search
    indices = torch.searchsorted(test_y, y, right=True)  # Find insertion points
    indices = torch.clamp(indices, 1, test_x.size(0) - 1)  # Clamp to valid index range

    # Get the two closest points around each y value for interpolation
    x0, x1 = test_x[indices - 1], test_x[indices]
    y0, y1 = test_y[indices - 1], test_y[indices]

    # Linear interpolation
    x = x0 + (y - y0) * (x1 - x0) / (y1 - y0)

    return y

def project_s2_points_to_img_cached(
    theta, phi, theta_shape, xi
):
    theta = np.fromstring(theta).reshape(theta_shape)
    phi = np.fromstring(phi).reshape(theta_shape)
    theta = np.clip(theta, 0,1.527165)
    theta = torch.tensor(theta)
    # breakpoint()
    theta_d_max = theta.max()
    # rho = 0
    # rho_max = 0
    # for order in range(1, poly_order + 1):
    #     rho += ks[order - 1] * theta**order
    k_d = ((torch.cos(theta_d_max) + xi)/torch.sin(theta_d_max))
    # print(num_points)
    funct = g_inv(theta, theta_d_max)
#     import pdb;pdb.set_trace()
#     funct = funct.reshape(theta.shape[0], num_points)
    radius = k_d*torch.sin(funct)/(torch.cos(funct) + xi)
    radius = np.array(radius)
    # breakpoint()
    u = radius * np.cos(phi)
    v = radius * np.sin(phi)

    return u*96 + 96, v*96 + 96

def project_s2_points_to_img(theta, phi, xi, rotate_pole):
    """Returns pixel coordinates (floats) corresponding to spherical points"""

    # Make hashable for caching:
    # aspect_ratio = cal_info["intrinsic"]["aspect_ratio"]
    # cx_offset = cal_info["intrinsic"]["cx_offset"]
    # cy_offset = cal_info["intrinsic"]["cy_offset"]
    # width = 192
    # height = 192
    # poly_order = cal_info["intrinsic"]["poly_order"]
    # ks = tuple([cal_info["intrinsic"]["k" + str(order)] for order in range(1, poly_order + 1)])
    # breakpoint()
    # D_min = D_min

    u, v = project_s2_points_to_img_cached(
        theta=theta.tostring(),
        phi=phi.tostring(),
        theta_shape=theta.shape,
        xi=xi
    )

    return u, v

def project_dataset_hp(img, mask, xi):
    # breakpoint()
    # img_save_dir = '/home-local2/akath.extra.nobkp/hp_med_test/'
    # os.makedirs(img_save_dir, exist_ok=True)
    # max_idx = len(dataset) 
    npix = hp.pixelfunc.nside2npix(64)
    ipix = np.arange(npix)
    theta, phi = hp.pixelfunc.pix2ang(64, ipix, nest=True)

    half_idcs = np.arange(npix * 8 // 12)
    theta = theta[half_idcs]
    phi = phi[half_idcs]

    # save_metadata(img_save_dir, theta, phi)

    # for idx in range(max_idx):
    #     # breakpoint()/
    #     print(idx)
    # img, mask, file_name, xi = dataset[idx]
    u, v = project_s2_points_to_img(theta, phi, xi, False)
    # breakpoint()
    hp_img = sample_bilinear(img.transpose(2, 0, 1), v, u).astype(np.uint8)
    hp_mask = sample_mask(mask, v, u, 0)
        # breakpoint()
        # np.savez(
        #     img_save_dir + str(xi) + '/' + file_name + ".npz", 
        #     hp_img=hp_img,
        #     hp_mask=hp_mask,
        # )
    return hp_img, hp_mask

    # if args.plot_last_on_s2:
    #     overlay = utils.get_overlay(args.woodscape_version, hp_mask, hp_img)
    #     pole_adjusted = "_pole_adjusted" if args.rotate_pole else ""
    #     base_pix = f"_base_pix={args.base_pix}"
    #     save_name = file_name + f"_on_s2_nside={args.nside}{base_pix}{pole_adjusted}.png"
    #     save_path = os.path.join(img_save_dir, save_name)
    #     healpy_utils.plot_hp_img(
    #         torch.tensor(overlay), npix, save_path, projection="orthview", n_colors=12
    #     )
################### healpix dataset generation ############################################
# H, W = (128, 128)
# x = torch.linspace(0, H, H+1) - H//2 - 0.5
# y = torch.linspace(0, W, W+1) - H//2 - 0.5
# grid_x, grid_y = torch.meshgrid(x[1:], y[1:])
# x_ = grid_x.reshape(H*H, 1)
# y_ = grid_y.reshape(W*W, 1)
# grid_pix = torch.cat((x_, y_), dim=1)
# grid_pix = grid_pix.reshape(1, H*W, 2)



class Stanford_hp(Dataset):
    def __init__(self, base_dir, split, xi, fov, high, low, img_size = (192, 192), transform=None):
        self.fov = 175
        self.transform = transform  # using transform in torch!
        self.split = split
        self.img_size = img_size
        self.data_dir = base_dir
        self.calib = None
        self.low = low
        self.xi = xi
        self.high = high
        self.normalize = normalize
        
        if split == 'train':
            with open(base_dir + '/train.pkl', 'rb') as f:
                img = pkl.load(f)
            with open(base_dir + '/train_sem.pkl', 'rb') as f:
                sem = pkl.load(f)

        elif split == 'val':
            with open(base_dir + '/val.pkl', 'rb') as f:
                img = pkl.load(f)
            with open(base_dir + '/val_sem.pkl', 'rb') as f:
                sem = pkl.load(f)

        elif split == 'test':
            # print('/12NN_' + str(n_rad) + '_' + str(img_size) +  '_concentric_test.pkl')
            with open(base_dir + '/test.pkl', 'rb') as f:
                img = pkl.load(f)
            with open(base_dir + '/test_sem.pkl', 'rb') as f:
                sem = pkl.load(f)
            # with open(base_dir + '/32_3_cl_curve_test.pkl', 'rb') as f:
            #     dist = pkl.load(f)
            with open(base_dir + '/deg_stan.pkl', 'rb') as f:
                deg = pkl.load(f)
                self.deg = deg

            # with open(self.data_dir + '/test_calib.pkl', 'rb') as f:
            #     self.calib = pkl.load(f)
        # print('/12NN_' + str(n_rad) + '_' + str(img_size) +  '_concentric .pkl')
        # if split == 'train' or split == 'val':
        #     with open(base_dir + '/32_3_cl_curve.pkl', 'rb') as f:
        #         dist = pkl.load(f)
        self.img = img #['1LXtFkjw3qL/85_spherical_1_emission_center_0.png'] #data[:5]
        self.sem = sem
        # self.dist = dist

        # if self.calib is None and os.path.exists(self.data_dir+ '/calib_gp2.pkl') :
        #     with open(self.data_dir + '/calib_gp2.pkl', 'rb') as f:
        #         self.calib = pkl.load(f)

    def __len__(self):
        return len(self.img)

    def __getitem__(self, idx):
        if self.split == 'train' or self.split == 'val':
            # breakpoint()/
            img_path = self.data_dir + '/' + self.img[idx]
            sem_path = self.data_dir + '/' + self.sem[idx]
            # i = random.randint(0, len(self.dist) - 1)
            # print(self.dist)
            # cls = self.dist[i][0]
        elif self.split == 'test':
            img_path = self.data_dir + '/' + self.img[idx]
            sem_path = self.data_dir + '/' + self.sem[idx]
                
                
        # image= load_color(img_path)['color']
        # print(len(self.dist))
        image = Image.open(img_path)
        image = image.resize((1024, 512), resample = 2)
        image = np.array(image)
        image = image[80:-80]
        image = cv2.resize(image, (704, 352),interpolation = cv2.INTER_LINEAR).astype(np.uint8)

        segm= Image.open(sem_path).convert('L')
        segm = segm.resize((1024, 512), resample = 0)
        segm = np.array(segm)
        segm = segm[80:-80]
        segm= cv2.resize(segm, (704, 352), interpolation = cv2.INTER_NEAREST)
        
        
        # breakpoint()
        segm = segm.reshape(352, 704, 1)
        # mat_path= img_path.replace('png','npy')
        #cl= np.load(mat_path)

        # image=image.permute(1,2,0)
        # segm=segm.permute(1,2,0)
        if True:
            # h= self.img_size
            h = 512
            fov=self.fov
            # print("field of view", fov)
            if self.split=='train' or self.split=='val':
                xi = random.uniform(self.low, self.high)
                deg = random.uniform(0, 360)
                # print(xi, self.low, self.high)
                # deg = 30
                # print(xi, fov, deg)
            elif self.split=='test':
                xi= self.xi
                # print(xi)
                deg = self.deg[idx]
                # print(xi, deg)
                # cls = self.dist[xi][0]

            # print(xi, deg)
            image, f = warpToFisheye(image[:, :, :3], viewingAnglesPYR=[np.deg2rad(0), np.deg2rad(deg), np.deg2rad(0)], outputdims=(h,h),xi=xi, fov=fov, order=1)
            segm,_= warpToFisheye(segm, viewingAnglesPYR=[np.deg2rad(0), np.deg2rad(deg), np.deg2rad(0)], outputdims=(h,h),xi=xi, fov=fov, order=0)
            dist= np.array([xi, f/(h/self.img_size), np.deg2rad(fov)]).astype(np.float32)
      
            segm = segm.astype(np.uint8)
            # print(xi, f, fov, h, deg)
        #resizing to image_size
        #image = resize(image,(self.img_size, self.img_size), order=1)
        #label= resize(depth,(self.img_size, self.img_size), order=0)
        image = cv2.resize(image, (self.img_size,self.img_size),interpolation = cv2.INTER_LINEAR).astype(np.uint8)
        label= cv2.resize(segm, (self.img_size,self.img_size), interpolation = cv2.INTER_NEAREST)
        # print(xi)
        image, label = project_dataset_hp(image, label, xi)


        image = T(image)
        image = image[0]

        

        
        sample = {'image': image, 'label': label}

        

        #sample['label']= sample['label'].squeeze(0)
        # if self.normalize is not None:
        #     # print(self.normalize)
        # # if None:
        #     # print(sample['image'].shape, normalize)
        #     sample['image']= self.normalize(sample['image'])

     
        return sample['image'], sample['label']

def get_mean_std(base_dir ):
    db= Stanford_hp(base_dir, split="train", transform=None)
    print(len(db))
    #sample= db.__getitem__(0)
    #print(sample['image'].shape)
    #print(sample['label'].shape)
    #print(sample['dist'].shape)
    loader = DataLoader(db, batch_size=len(db), shuffle=False,num_workers=0)
    im_lab_dict = next(iter(loader))
    images, labels = im_lab_dict
    # shape of images = [b,c,w,h]
    mean, std = images.mean([0,2,3]), images.std([0,2,3])
    print("mean",mean)
    print("std", std)
    return mean , std



if __name__=='__main__':
    db_train = Stanford_hp(base_dir="/home-local2/akath.extra.nobkp/woodscapes_hp", split="train")
    db_train[0]
    # project_dataset_hp(db_train)    

    data_loader_train = torch.utils.data.DataLoader(
        db_train,
        batch_size=8,
        num_workers=3,
        pin_memory=True,
        drop_last=True,
    )
    psum = 0
    psum_sq = 0
    for i_batch, sampled_batch in enumerate(data_loader_train):
        images, labels = sampled_batch
        # breakpoint()
        # im = np.array(images[0])
        # pil(images[0]).save('pol.png')
        print(i_batch)
        psum  += images.sum(axis = [0, 2])
        psum_sq += (images ** 2).sum(axis = [0, 2])
    # breakpoint()
    count = len(db_train)*192*192
    total_mean = psum / count
    total_var  = (psum_sq / count) - (total_mean ** 2)
    total_std  = torch.sqrt(total_var)
    print(total_std, total_mean)