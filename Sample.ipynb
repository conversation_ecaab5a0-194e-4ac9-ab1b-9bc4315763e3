{"cells": [{"cell_type": "code", "execution_count": 1, "id": "b51e093a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["from PIL import Image\n", "import torch\n", "import torch.nn as nn\n", "import json"]}, {"cell_type": "code", "execution_count": 2, "id": "a8dd75a9", "metadata": {}, "outputs": [], "source": ["with open('/home-local2/akath.extra.nobkp/woodscapes/calibration/00000_FV.json', 'r') as f:\n", "    FV = json.load(f)\n", "with open('/home-local2/akath.extra.nobkp/woodscapes/calibration/00070_MVR.json', 'r') as f:\n", "    MVR = json.load(f)\n", "with open('/home-local2/akath.extra.nobkp/woodscapes/calibration/00030_RV.json', 'r') as f:\n", "    RV = json.load(f)\n", "with open('/home-local2/akath.extra.nobkp/woodscapes/calibration/00141_MVL.json', 'r') as f:\n", "    MVL = json.load(f)"]}, {"cell_type": "code", "execution_count": 3, "id": "14126d03", "metadata": {}, "outputs": [], "source": ["from torchvision.transforms import transforms\n", "t2pil = transforms.ToTensor()\n", "pil = transforms.ToPILImage()"]}, {"cell_type": "code", "execution_count": 4, "id": "b09d61f3", "metadata": {}, "outputs": [], "source": ["import numpy as np"]}, {"cell_type": "code", "execution_count": 5, "id": "1785e59e", "metadata": {}, "outputs": [], "source": ["from utils_curve_inverse import concentric_dic_sampling, DA_grid_inv_, DA_grid_inv\n", "\n", "# from utils_theta_inverse import concentric_dic_sampling, DA_grid_inv_, DA_grid_inv"]}, {"cell_type": "code", "execution_count": 6, "id": "1b0a1940", "metadata": {}, "outputs": [], "source": ["a = torch.tensor([0.0, 1.5, 3.047911227757854, 0.5, 17.517568479641348, 3.047911227757854, 1.0, 33.535136959282696, 3.047911227757854]).reshape(3, 3).transpose(0,1)"]}, {"cell_type": "code", "execution_count": 7, "id": "6ef1e84a", "metadata": {}, "outputs": [{"data": {"text/plain": ["'/home-local/akath.nobkp/Radial-Unet'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["pwd"]}, {"cell_type": "code", "execution_count": 8, "id": "a38a82e5", "metadata": {}, "outputs": [], "source": ["im1 = Image.open('/home-local2/akath.extra.nobkp/woodscapes/rgb_images/00000_FV.png')\n", "im2 = Image.open('/home-local2/akath.extra.nobkp/woodscapes/rgb_images/00070_MVR.png')\n", "im3 = Image.open('/home-local2/akath.extra.nobkp/woodscapes/rgb_images/00030_RV.png')\n", "im4 = Image.open('/home-local2/akath.extra.nobkp/woodscapes/rgb_images/00141_MVL.png')\n", "im1 = im1.resize((512,512))\n", "im2 = im2.resize((512,512))\n", "im3 = im3.resize((512,512))\n", "im4 = im4.resize((512,512))"]}, {"cell_type": "code", "execution_count": 13, "id": "553ddac4", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/ipykernel_launcher.py:8: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  \n"]}], "source": ["dist = torch.tensor([339.749, -31.988, 48.275, -7.201, 337.657, -16.126, 27.487, 0.888, 339.039, -29.815, 46.483, -6.655, 335.497, -11.41, 22.009, 2.539]).reshape(4, 4).transpose(0,1)\n", "a = torch.tensor([0.8, 1.5, 3.047911227757854]).reshape(1, 3).transpose(0,1)\n", "H = 128\n", "rad = 3\n", "div = H*rad//2\n", "img_size = (H, H)\n", "subdiv = (div, div)\n", "dist = torch.tensor(dist).unsqueeze(-1)\n", "\n", "xc, yc = concentric_dic_sampling(\n", "    subdiv=subdiv,\n", "    img_size= img_size,\n", "    distortion_model = \"spherical\",\n", "    D = a)\n", "# sample_locations = get_sample_locations(**params)  ## B, azimuth_cuts*radius_cuts, n_radius*n_azimut"]}, {"cell_type": "code", "execution_count": 14, "id": "52599a2c", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'x_' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m/tmp/ipykernel_1502426/797466074.py\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mx_\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mmax\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mxc\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mmax\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mNameError\u001b[0m: name 'x_' is not defined"]}], "source": ["x_.max(), xc.max()"]}, {"cell_type": "code", "execution_count": 11, "id": "ebf65e07", "metadata": {}, "outputs": [{"data": {"text/plain": ["<PERSON>.<PERSON><PERSON>([33, 33])"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["H, W = 33, 33\n", "y = torch.linspace(0, W, W+1) - (W//2+0.5)\n", "x = torch.linspace(0,H,H+1) - (H//2 + 0.5)\n", "grid_x, grid_y = torch.meshgrid(x[1:], y[1:])\n", "x_a = grid_x.reshape(H*W, 1)\n", "y_a = grid_y.reshape(H*W, 1)\n", "grid_x.shape"]}, {"cell_type": "code", "execution_count": 47, "id": "0ba76ef2", "metadata": {}, "outputs": [{"data": {"text/plain": ["(-17.6, 17.6, -17.6, 17.6)"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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***************************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\n", "text/plain": ["<Figure size 1000x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "# for i in range(10):\n", "\n", "\n", "_, ax = plt.subplots(figsize=(10, 10))\n", "# ax.set_title(\"Sampling locations\")\n", "colors = ['#e41a1c', '#377eb8', '#4daf4a', '#984ea3', '#ff7f00', '#ffff33', '#a65628']\n", "# sampling_loc[0][0].shape[1]\n", "for i in range(xc.shape[1]):\n", "    x = xc[0][i].detach().cpu().numpy()\n", "    y = yc[0][i].detach().cpu().numpy()\n", "    ax.scatter(x, y , color='darkblue', s=20)\n", "plt.axis('off')\n", "# plt.savefig(\"ellip_grid_4.png\", bbox_inches='tight', pad_inches=0, transparent=True)"]}, {"cell_type": "code", "execution_count": 49, "id": "a55437ab", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAxoAAAMWCAYAAAB2gvApAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjUuMywgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/NK7nSAAAACXBIWXMAAA9hAAAPYQGoP6dpAAAzqklEQVR4nO3cMU7jTBzG4b8/U2y54QKJRICLWDJFSiravde2bmhXAomThESCCyR0VMhft7s00RLNeBj0POVY+mnaV47TjOM4BgAAQEL/lb4AAADw9RgaAABAcoYGAACQnKEBAAAkZ2gAAADJGRoAAEByhgYAAJCcoQEAACRnaAAAAMkZGgAAQHKGBgAAkJyhAQAAJGdoAAAAyRkaAABAcoYGAACQnKEBAAAkZ2gAAADJGRoAAEByhgYAAJCcoQEAACRnaAAAAMkZGgAAQHKGBgAAkJyhAQAAJGdoAAAAyRkaAABAcoYGAACQnKEBAAAkZ2gAAADJGRoAAEByhgYAAJCcoQEAACRnaAAAAMkZGgAAQHKGBgAAkJyhAQAAJGdoAAAAyRkaAABAcoYGAACQnKEBAAAkd1L6Ah+1Xu9iu32J5XIW5+czXV1dXd2Jujnburq6urplujlVMzR2u9e4ufkV9/dPv8/6fhHDsIrZ7Juurq6ubqZuzraurq6ubpnuFJpxHMfSl/gXV1e38fDwHG9vf67btk103Tzu7q51dXV1dTN1a7yzrq6urm55VQyN9XoXl5c/Dzz/cdQrJF1dXV3dcm1dXV1d3TLdqVTxMfh2+3Lw+Waz19XV1dXN0M3Z1tXV1dUt051KFUPj7Oz7wefL5XFLTldXV1e3XFtXV1dXt0x3KlUMjYuL0+j7RbRt8+68bZvo+8XRr4x0dXV1db/enXV1dXV1P4cqhkZExDCsouvm7866bh7DsNLV1dXVzdjN2dbV1dXVLdOdQhUfg//t8XEfm80++X8I6+rq6uqWa+vq6urqlunmVN3QAAAAPr9qfjoFAADUw9AAAACSMzQAAIDkDA0AACA5QwMAAEjO0AAAAJIzNAAAgOQMDQAAIDlDAwAASM7QAAAAkjM0AACA5AwNAAAguZPSF/io9XoX2+1LLJezOD+f6erq6upO1M3Z1tXV1dUt082pmqGx273Gzc2vuL9/+n3W94sYhlXMZt90dXV1dTN1c7Z1dXV1dct0p9CM4ziWvsS/uLq6jYeH53h7+3Pdtm2i6+Zxd3etq6urq5upW+OddXV1dXXLq2JorNe7uLz8eeD5j6NeIenq6urqlmvr6urq6pbpTqWKj8G325eDzzebva6urq5uhm7Otq6urq5ume5UqhgaZ2ffDz5fLo9bcrq6urq65dq6urq6umW6U6liaFxcnEbfL6Jtm3fnbdtE3y+OfmWkq6urq/v17qyrq6ur+zlUMTQiIoZhFV03f3fWdfMYhpWurq6ubsZuzraurq6ubpnuFKr4GPxvj4/72Gz2yf9DWFdXV1e3XFtXV1dXt0w3p+qGBgAA8PlV89MpAACgHoYGAACQnKEBAAAkZ2gAAADJGRoAAEByhgYAAJCcoQEAACRnaAAAAMkZGgAAQHKGBgAAkJyhAQAAJGdoAAAAyZ2UvsBHrde72G5fYrmcxfn5TFdXV1d3om7Otq6urq5umW5O1QyN3e41bm5+xf390++zvl/EMKxiNvumq6urq5upm7Otq6urq1umO4VmHMex9CX+xdXVbTw8PMfb25/rtm0TXTePu7trXV1dXd1M3RrvrKurq6tbXhVDY73exeXlzwPPfxz1CklXV1dXt1xbV1dXV7dMdypVfAy+3b4cfL7Z7HV1dXV1M3RztnV1dXV1y3SnUsXQODv7fvD5cnncktPV1dXVLdfW1dXV1S3TnUoVQ+Pi4jT6fhFt27w7b9sm+n5x9CsjXV1dXd2vd2ddXV1d3c+hiqERETEMq+i6+buzrpvHMKx0dXV1dTN2c7Z1dXV1dct0p1DFx+B/e3zcx2azT/4fwrq6urq65dq6urq6umW6OVU3NAAAgM+vmp9OAQAA9TA0AACA5AwNAAAgOUMDAABIztAAAACSMzQAAIDkDA0AACA5QwMAAEjO0AAAAJIzNAAAgOQMDQAAIDlDAwAASO6k9AU+ar3exXb7EsvlLM7PZ7q6urq6E3VztnV1dXV1y3RzqmZo7HavcXPzK+7vn36f9f0ihmEVs9k3XV1dXd1M3ZxtXV1dXd0y3Sk04ziOpS/xL66ubuPh4Tne3v5ct22b6Lp53N1d6+rq6upm6tZ4Z11dXV3d8qoYGuv1Li4vfx54/uOoV0i6urq6uuXaurq6urplulOp4mPw7fbl4PPNZq+rq6urm6Gbs62rq6urW6Y7lSqGxtnZ94PPl8vjlpyurq6ubrm2rq6urm6Z7lSqGBoXF6fR94to2+bdeds20feLo18Z6erq6up+vTvr6urq6n4OVQyNiIhhWEXXzd+ddd08hmGlq6urq5uxm7Otq6urq1umO4UqPgb/2+PjPjabffL/ENbV1dXVLdfW1dXV1S3Tzam6oQEAAHx+1fx0CgAAqIehAQAAJGdoAAAAyRkaAABAcoYGAACQnKEBAAAkZ2gAAADJGRoAAEByhgYAAJCcoQEAACRnaAAAAMmdlL7AR63Xu9huX2K5nMX5+UxXV1dXd6Juzraurq6ubpluTtUMjd3uNW5ufsX9/dPvs75fxDCsYjb7pqurq6ubqZuzraurq6tbpjuFZhzHsfQl/sXV1W08PDzH29uf67ZtE103j7u7a11dXV3dTN0a76yrq6urW14VQ2O93sXl5c8Dz38c9QpJV1dXV7dcW1dXV1e3THcqVXwMvt2+HHy+2ex1dXV1dTN0c7Z1dXV1dct0p1LF0Dg7+37w+XJ53JLT1dXV1S3X1tXV1dUt051KFUPj4uI0+n4Rbdu8O2/bJvp+cfQrI11dXV3dr3dnXV1dXd3PoYqhERExDKvouvm7s66bxzCsdHV1dXUzdnO2dXV1dXXLdKdQxcfgf3t83Mdms0/+H8K6urq6uuXaurq6urplujlVNzQAAIDPr5qfTgEAAPUwNAAAgOQMDQAAIDlDAwAASM7QAAAAkjM0AACA5AwNAAAgOUMDAABIztAAAACSMzQAAIDkDA0AACA5QwMAAEjupPQFPmq93sV2+xLL5SzOz2e6urq6uhN1c7Z1dXV1dct0c6pmaOx2r3Fz8yvu759+n/X9IoZhFbPZN11dXV3dTN2cbV1dXV3dMt0pNOM4jqUv8S+urm7j4eE53t7+XLdtm+i6edzdXevq6urqZurWeGddXV1d3fKqGBrr9S4uL38eeP7jqFdIurq6urrl2rq6urq6ZbpTqeJj8O325eDzzWavq6urq5uhm7Otq6urq1umO5UqhsbZ2feDz5fL45acrq6urm65tq6urq5ume5UqhgaFxen0feLaNvm3XnbNtH3i6NfGenq6urqfr076+rq6up+DlUMjYiIYVhF183fnXXdPIZhpaurq6ubsZuzraurq6tbpjuFKj4G/9vj4z42m33y/xDW1dXV1S3X1tXV1dUt082puqEBAAB8ftX8dAoAAKiHoQEAACRnaAAAAMkZGgAAQHKGBgAAkJyhAQAAJGdoAAAAyRkaAABAcoYGAACQnKEBAAAkZ2gAAADJGRoAAEByJ6Uv8FHr9S6225dYLmdxfj7T1dXV1Z2om7Otq6urq1umm1M1Q2O3e42bm19xf//0+6zvFzEMq5jNvunq6urqZurmbOvq6urqlulOoRnHcSx9iX9xdXUbDw/P8fb257pt20TXzePu7lpXV1dXN1O3xjvr6urq6pZXxdBYr3dxefnzwPMfR71C0tXV1dUt19bV1dXVLdOdShUfg2+3LwefbzZ7XV1dXd0M3ZxtXV1dXd0y3alUMTTOzr4ffL5cHrfkdHV1dXXLtXV1dXV1y3SnUsXQuLg4jb5fRNs2787btom+Xxz9ykhXV1dX9+vdWVdXV1f3c6hiaEREDMMqum7+7qzr5jEMK11dXV3djN2cbV1dXV3dMt0pVPEx+N8eH/ex2eyT/4ewrq6urm65tq6urq5umW5O1Q0NAADg86vmp1MAAEA9DA0AACA5QwMAAEjO0AAAAJIzNAAAgOQMDQAAIDlDAwAASM7QAAAAkjM0AACA5AwNAAAgOUMDAABIztAAAACSOyl9gY9ar3ex3b7EcjmL8/OZrq6uru5E3ZxtXV1dXd0y3ZyqGRq73Wvc3PyK+/un32d9v4hhWMVs9k1XV1dXN1M3Z1tXV1dXt0x3Cs04jmPpS/yLq6vbeHh4jre3P9dt2ya6bh53d9e6urq6upm6Nd5ZV1dXV7e8KobGer2Ly8ufB57/OOoVkq6urq5uubaurq6ubpnuVKr4GHy7fTn4fLPZ6+rq6upm6OZs6+rq6uqW6U6liqFxdvb94PPl8rglp6urq6tbrq2rq6urW6Y7lSqGxsXFafT9Itq2eXfetk30/eLoV0a6urq6ul/vzrq6urq6n0MVQyMiYhhW0XXzd2ddN49hWOnq6urqZuzmbOvq6urqlulOoYqPwf/2+LiPzWaf/D+EdXV1dXXLtXV1dXV1y3Rzqm5oAAAAn181P50CAADqYWgAAADJGRoAAEByhgYAAJCcoQEAACRnaAAAAMkZGgAAQHKGBgAAkJyhAQAAJGdoAAAAyRkaAABAcoYGAACQ3EnpC3zUer2L7fYllstZnJ/PdHV1dXUn6uZs6+rq6uqW6eZUzdDY7V7j5uZX3N8//T7r+0UMwypms2+6urq6upm6Odu6urq6umW6U2jGcRxLX+JfXF3dxsPDc7y9/blu2zbRdfO4u7vW1dXV1c3UrfHOurq6urrlVTE01utdXF7+PPD8x1GvkHR1dXV1y7V1dXV1dct0p1LFx+Db7cvB55vNXldXV1c3QzdnW1dXV1e3THcqVQyNs7PvB58vl8ctOV1dXV3dcm1dXV1d3TLdqVQxNC4uTqPvF9G2zbvztm2i7xdHvzLS1dXV1f16d9bV1dXV/RyqGBoREcOwiq6bvzvrunkMw0pXV1dXN2M3Z1tXV1dXt0x3ClV8DP63x8d9bDb75P8hrKurq6tbrq2rq6urW6abU3VDAwAA+Pyq+ekUAABQD0MDAABIztAAAACSMzQAAIDkDA0AACA5QwMAAEjO0AAAAJIzNAAAgOQMDQAAIDlDAwAASM7QAAAAkjM0AACA5E5KX+Cj1utdbLcvsVzO4vx8pqurq6s7UTdnW1dXV1e3TDenaobGbvcaNze/4v7+6fdZ3y9iGFYxm33T1dXV1c3UzdnW1dXV1S3TnUIzjuNY+hL/4urqNh4enuPt7c9127aJrpvH3d21rq6urm6mbo131tXV1dUtr4qhsV7v4vLy54HnP456haSrq6urW66tq6urq1umO5UqPgbfbl8OPt9s9rq6urq6Gbo527q6urq6ZbpTqWJonJ19P/h8uTxuyenq6urqlmvr6urq6pbpTqWKoXFxcRp9v4i2bd6dt20Tfb84+pWRrq6uru7Xu7Ourq6u7udQxdCIiBiGVXTd/N1Z181jGFa6urq6uhm7Odu6urq6umW6U6jiY/C/PT7uY7PZJ/8PYV1dXV3dcm1dXV1d3TLdnKobGgAAwOdXzU+nAACAehgaAABAcoYGAACQnKEBAAAkZ2gAAADJGRoAAEByhgYAAJCcoQEAACRnaAAAAMkZGgAAQHKGBgAAkJyhAQAAJHdS+gIftV7vYrt9ieVyFufnM11dXV3dibo527q6urq6Zbo5VTM0drvXuLn5Fff3T7/P+n4Rw7CK2eybrq6urm6mbs62rq6urm6Z7hSacRzH0pf4F1dXt/Hw8Bxvb3+u27ZNdN087u6udXV1dXUzdWu8s66urq5ueVUMjfV6F5eXPw88/3HUKyRdXV1d3XJtXV1dXd0y3alU8TH4dvty8Plms9fV1dXVzdDN2dbV1dXVLdOdShVD4+zs+8Hny+VxS05XV1dXt1xbV1dXV7dMdypVDI2Li9Po+0W0bfPuvG2b6PvF0a+MdHV1dXW/3p11dXV1dT+HKoZGRMQwrKLr5u/Oum4ew7DS1dXV1c3YzdnW1dXV1S3TnUIVH4P/7fFxH5vNPvl/COvq6urqlmvr6urq6pbp5lTd0AAAAD6/an46BQAA1MPQAAAAkjM0AACA5AwNAAAgOUMDAABIztAAAACSMzQAAIDkDA0AACA5QwMAAEjO0AAAAJIzNAAAgOQMDQAAILmT0hf4qPV6F9vtSyyXszg/n+nq6urqTtTN2dbV1dXVLdPNqZqhsdu9xs3Nr7i/f/p91veLGIZVzGbfdHV1dXUzdXO2dXV1dXXLdKfQjOM4lr7Ev7i6uo2Hh+d4e/tz3bZtouvmcXd3raurq6ubqVvjnXV1dXV1y6tiaKzXu7i8/Hng+Y+jXiHp6urq6pZr6+rq6uqW6U6lio/Bt9uXg883m72urq6uboZuzraurq6ubpnuVKoYGmdn3w8+Xy6PW3K6urq6uuXaurq6urplulOpYmhcXJxG3y+ibZt3523bRN8vjn5lpKurq6v79e6sq6urq/s5VDE0IiKGYRVdN3931nXzGIaVrq6urm7Gbs62rq6urm6Z7hSq+Bj8b4+P+9hs9sn/Q1hXV1dXt1xbV1dXV7dMN6fqhgYAAPD5VfPTKQAAoB6GBgAAkJyhAQAAJGdoAAAAyRkaAABAcoYGAACQnKEBAAAkZ2gAAADJGRoAAEByhgYAAJCcoQEAACR3UvoCH7Ve72K7fYnlchbn5zNdXV1d3Ym6Odu6urq6umW6OVUzNHa717i5+RX390+/z/p+EcOwitnsm66urq5upm7Otq6urq5ume4UmnEcx9KX+BdXV7fx8PAcb29/rtu2TXTdPO7urnV1dXV1M3VrvLOurq6ubnlVDI31eheXlz8PPP9x1CskXV1dXd1ybV1dXV3dMt2pVPEx+Hb7cvD5ZrPX1dXV1c3QzdnW1dXV1S3TnUoVQ+Ps7PvB58vlcUtOV1dXV7dcW1dXV1e3THcqVQyNi4vT6PtFtG3z7rxtm+j7xdGvjHR1dXV1v96ddXV1dXU/hyqGRkTEMKyi6+bvzrpuHsOw0tXV1dXN2M3Z1tXV1dUt051CFR+D/+3xcR+bzT75fwjr6urq6pZr6+rq6uqW6eZU3dAAAAA+v2p+OgUAANTD0AAAAJIzNAAAgOQMDQAAIDlDAwAASM7QAAAAkjM0AACA5AwNAAAgOUMDAABIztAAAACSMzQAAIDkDA0AACC5k9IX+Kj1ehfb7Ussl7M4P5/p6urq6k7UzdnW1dXV1S3TzamaobHbvcbNza+4v3/6fdb3ixiGVcxm33R1dXV1M3VztnV1dXV1y3Sn0IzjOJa+xL+4urqNh4fneHv7c922baLr5nF3d62rq6urm6lb4511dXV1dcurYmis17u4vPx54PmPo14h6erq6uqWa+vq6urqlulOpYqPwbfbl4PPN5u9rq6urm6Gbs62rq6urm6Z7lSqGBpnZ98PPl8uj1tyurq6urrl2rq6urq6ZbpTqWJoXFycRt8vom2bd+dt20TfL45+ZaSrq6ur+/XurKurq6v7OVQxNCIihmEVXTd/d9Z18xiGla6urq5uxm7Otq6urq5ume4UqvgY/G+Pj/vYbPbJ/0NYV1dXV7dcW1dXV1e3TDen6oYGAADw+VXz0ykAAKAehgYAAJCcoQEAACRnaAAAAMkZGgAAQHKGBgAAkJyhAQAAJGdoAAAAyRkaAABAcoYGAACQnKEBAAAkZ2gAAADJnZS+wEet17vYbl9iuZzF+flMV1dXV3eibs62rq6urm6Zbk7VDI3d7jVubn7F/f3T77O+X8QwrGI2+6arq6urm6mbs62rq6urW6Y7hWYcx7H0Jf7F1dVtPDw8x9vbn+u2bRNdN4+7u2tdXV1d3UzdGu+sq6urq1teFUNjvd7F5eXPA89/HPUKSVdXV1e3XFtXV1dXt0x3KlV8DL7dvhx8vtnsdXV1dXUzdHO2dXV1dXXLdKdSxdA4O/t+8PlyedyS09XV1dUt19bV1dXVLdOdShVD4+LiNPp+EW3bvDtv2yb6fnH0KyNdXV1d3a93Z11dXV3dz6GKoRERMQyr6Lr5u7Oum8cwrHR1dXV1M3ZztnV1dXV1y3SnUMXH4H97fNzHZrNP/h/Curq6urrl2rq6urq6Zbo5VTc0AACAz6+an04BAAD1MDQAAIDkDA0AACA5QwMAAEjO0AAAAJIzNAAAgOQMDQAAIDlDAwAASM7QAAAAkjM0AACA5AwNAAAgOUMDAABI7qT0BT5qvd7FdvsSy+Uszs9nurq6uroTdXO2dXV1dXXLdHOqZmjsdq9xc/Mr7u+ffp/1/SKGYRWz2TddXV1d3UzdnG1dXV1d3TLdKTTjOI6lL/Evrq5u4+HhOd7e/ly3bZvounnc3V3r6urq6mbq1nhnXV1dXd3yqhga6/UuLi9/Hnj+46hXSLq6urq65dq6urq6umW6U6niY/Dt9uXg881mr6urq6uboZuzraurq6tbpjuVKobG2dn3g8+Xy+OWnK6urq5uubaurq6ubpnuVKoYGhcXp9H3i2jb5t152zbR94ujXxnp6urq6n69O+vq6urqfg5VDI2IiGFYRdfN35113TyGYaWrq6urm7Gbs62rq6urW6Y7hSo+Bv/b4+M+Npt98v8Q1tXV1dUt19bV1dXVLdPNqbqhAQAAfH7V/HQKAACoh6EBAAAkZ2gAAADJGRoAAEByhgYAAJCcoQEAACRnaAAAAMkZGgAAQHKGBgAAkJyhAQAAJGdoAAAAyRkaAABAcielL/BR6/UuttuXWC5ncX4+09XV1dWdqJuzraurq6tbpptTNUNjt3uNm5tfcX//9Pus7xcxDKuYzb7p6urq6mbq5mzr6urq6pbpTqEZx3EsfYl/cXV1Gw8Pz/H29ue6bdtE183j7u5aV1dXVzdTt8Y76+rq6uqWV8XQWK93cXn588DzH0e9QtLV1dXVLdfW1dXV1S3TnUoVH4Nvty8Hn282e11dXV3dDN2cbV1dXV3dMt2pVDE0zs6+H3y+XB635HR1dXV1y7V1dXV1dct0p1LF0Li4OI2+X0TbNu/O27aJvl8c/cpIV1dXV/fr3VlXV1dX93OoYmhERAzDKrpu/u6s6+YxDCtdXV1d3YzdnG1dXV1d3TLdKVTxMfjfHh/3sdnsk/+HsK6urq5uubaurq6ubpluTtUNDQAA4POr5qdTAABAPQwNAAAgOUMDAABIztAAAACSMzQAAIDkDA0AACA5QwMAAEjO0AAAAJIzNAAAgOQMDQAAIDlDAwAASM7QAAAAkjspfYGPWq93sd2+xHI5i/Pzma6urq7uRN2cbV1dXV3dMt2cqhkau91r3Nz8ivv7p99nfb+IYVjFbPZNV1dXVzdTN2dbV1dXV7dMdwrNOI5j6Uv8i6ur23h4eI63tz/Xbdsmum4ed3fXurq6urqZujXeWVdXV1e3vCqGxnq9i8vLnwee/zjqFZKurq6ubrm2rq6urm6Z7lSq+Bh8u305+Hyz2evq6urqZujmbOvq6urqlulOpYqhcXb2/eDz5fK4Jaerq6urW66tq6urq1umO5UqhsbFxWn0/SLatnl33rZN9P3i6FdGurq6urpf7866urq6up9DFUMjImIYVtF183dnXTePYVjp6urq6mbs5mzr6urq6pbpTqGKj8H/9vi4j81mn/w/hHV1dXV1y7V1dXV1dct0c6puaAAAAJ9fNT+dAgAA6mFoAAAAyRkaAABAcoYGAACQnKEBAAAkZ2gAAADJGRoAAEByhgYAAJCcoQEAACRnaAAAAMkZGgAAQHKGBgAAkNxJ6Qt81Hq9i+32JZbLWZyfz3R1dXV1J+rmbOvq6urqlunmVM3Q2O1e4+bmV9zfP/0+6/tFDMMqZrNvurq6urqZujnburq6urplulNoxnEcS1/iX1xd3cbDw3O8vf25bts20XXzuLu71tXV1dXN1K3xzrq6urq65VUxNNbrXVxe/jzw/MdRr5B0dXV1dcu1dXV1dXXLdKdSxcfg2+3LweebzV5XV1dXN0M3Z1tXV1dXt0x3KlUMjbOz7wefL5fHLTldXV1d3XJtXV1dXd0y3alUMTQuLk6j7xfRts2787Ztou8XR78y0tXV1dX9enfW1dXV1f0cqhgaERHDsIqum78767p5DMNKV1dXVzdjN2dbV1dXV7dMdwpVfAz+t8fHfWw2++T/Iayrq6urW66tq6urq1umm1N1QwMAAPj8qvnpFAAAUA9DAwAASM7QAAAAkjM0AACA5AwNAAAgOUMDAABIztAAAACSMzQAAIDkDA0AACA5QwMAAEjO0AAAAJIzNAAAgOROSl/go9brXWy3L7FczuL8fKarq6urO1E3Z1tXV1dXt0w3p2qGxm73Gjc3v+L+/un3Wd8vYhhWMZt909XV1dXN1M3Z1tXV1dUt051CM47jWPoS/+Lq6jYeHp7j7e3Pddu2ia6bx93dta6urq5upm6Nd9bV1dXVLa+KobFe7+Ly8ueB5z+OeoWkq6urq1uuraurq6tbpjuVKj4G325fDj7fbPa6urq6uhm6Odu6urq6umW6U6liaJydfT/4fLk8bsnp6urq6pZr6+rq6uqW6U6liqFxcXEafb+Itm3enbdtE32/OPqVka6urq7u17uzrq6uru7nUMXQiIgYhlV03fzdWdfNYxhWurq6uroZuznburq6urplulOo4mPwvz0+7mOz2Sf/D2FdXV1d3XJtXV1dXd0y3ZyqGxoAAMDnV81PpwAAgHoYGgAAQHKGBgAAkJyhAQAAJGdoAAAAyRkaAABAcoYGAACQnKEBAAAkZ2gAAADJGRoAAEByhgYAAJDcSekLfNR6vYvt9iWWy1mcn890dXV1dSfq5mzr6urq6pbp5lTN0NjtXuPm5lfc3z/9Puv7RQzDKmazb7q6urq6mbo527q6urq6ZbpTaMZxHEtf4l9cXd3Gw8NzvL39uW7bNtF187i7u9bV1dXVzdSt8c66urq6uuVVMTTW611cXv488PzHUa+QdHV1dXXLtXV1dXV1y3SnUsXH4Nvty8Hnm81eV1dXVzdDN2dbV1dXV7dMdypVDI2zs+8Hny+Xxy05XV1dXd1ybV1dXV3dMt2pVDE0Li5Oo+8X0bbNu/O2baLvF0e/MtLV1dXV/Xp31tXV1dX9HKoYGhERw7CKrpu/O+u6eQzDSldXV1c3YzdnW1dXV1e3THcKVXwM/rfHx31sNvvk/yGsq6urq1uuraurq6tbpptTdUMDAAD4/Kr56RQAAFAPQwMAAEjO0AAAAJIzNAAAgOQMDQAAIDlDAwAASM7QAAAAkjM0AACA5AwNAAAgOUMDAABIztAAAACSMzQAAIDkTkpf4KPW611sty+xXM7i/Hymq6urqztRN2dbV1dXV7dMN6dqhsZu9xo3N7/i/v7p91nfL2IYVjGbfdPV1dXVzdTN2dbV1dXVLdOdQjOO41j6Ev/i6uo2Hh6e4+3tz3Xbtomum8fd3bWurq6ubqZujXfW1dXV1S2viqGxXu/i8vLngec/jnqFpKurq6tbrq2rq6urW6Y7lSo+Bt9uXw4+32z2urq6uroZujnburq6urplulOpYmicnX0/+Hy5PG7J6erq6uqWa+vq6urqlulOpYqhcXFxGn2/iLZt3p23bRN9vzj6lZGurq6u7te7s66urq7u51DF0IiIGIZVdN383VnXzWMYVrq6urq6Gbs527q6urq6ZbpTqOJj8L89Pu5js9kn/w9hXV1dXd1ybV1dXV3dMt2cqhsaAADA51fNT6cAAIB6GBoAAEByhgYAAJCcoQEAACRnaAAAAMkZGgAAQHKGBgAAkJyhAQAAJGdoAAAAyRkaAABAcoYGAACQnKEBAAAkd1L6Ah+1Xu9iu32J5XIW5+czXV1dXd2Jujnburq6urplujlVMzR2u9e4ufkV9/dPv8/6fhHDsIrZ7Juurq6ubqZuzraurq6ubpnuFJpxHMfSl/gXV1e38fDwHG9vf67btk103Tzu7q51dXV1dTN1a7yzrq6urm55VQyN9XoXl5c/Dzz/cdQrJF1dXV3dcm1dXV1d3TLdqVTxMfh2+3Lw+Waz19XV1dXN0M3Z1tXV1dUt051KFUPj7Oz7wefL5XFLTldXV1e3XFtXV1dXt0x3KlUMjYuL0+j7RbRt8+68bZvo+8XRr4x0dXV1db/enXV1dXV1P4cqhkZExDCsouvm7866bh7DsNLV1dXVzdjN2dbV1dXVLdOdQhUfg//t8XEfm80++X8I6+rq6uqWa+vq6urqlunmVN3QAAAAPr9qfjoFAADUw9AAAACSMzQAAIDkDA0AACA5QwMAAEjO0AAAAJIzNAAAgOQMDQAAIDlDAwAASM7QAAAAkjM0AACA5AwNAAAguZPSF/io9XoX2+1LLJezOD+f6erq6upO1M3Z1tXV1dUt082pmqGx273Gzc2vuL9/+n3W94sYhlXMZt90dXV1dTN1c7Z1dXV1dct0p9CM4ziWvsS/uLq6jYeH53h7+3Pdtm2i6+Zxd3etq6urq5upW+OddXV1dXXLq2JorNe7uLz8eeD5j6NeIenq6urqlmvr6urq6pbpTqWKj8G325eDzzebva6urq5uhm7Otq6urq5ume5UqhgaZ2ffDz5fLo9bcrq6urq65dq6urq6umW6U6liaFxcnEbfL6Jtm3fnbdtE3y+OfmWkq6urq/v17qyrq6ur+zlUMTQiIoZhFV03f3fWdfMYhpWurq6ubsZuzraurq6ubpnuFKr4GPxvj4/72Gz2yf9DWFdXV1e3XFtXV1dXt0w3p+qGBgAA8PlV89MpAACgHoYGAACQnKEBAAAkZ2gAAADJGRoAAEByhgYAAJCcoQEAACRnaAAAAMkZGgAAQHKGBgAAkJyhAQAAJGdoAAAAyZ2UvsBHrde72G5fYrmcxfn5TFdXV1d3om7Otq6urq5umW5O1QyN3e41bm5+xf390++zvl/EMKxiNvumq6urq5upm7Otq6urq1umO4VmHMex9CX+xdXVbTw8PMfb25/rtm0TXTePu7trXV1dXd1M3RrvrKurq6tbXhVDY73exeXlzwPPfxz1CklXV1dXt1xbV1dXV7dMdypVfAy+3b4cfL7Z7HV1dXV1M3RztnV1dXV1y3SnUsXQODv7fvD5cnncktPV1dXVLdfW1dXV1S3TnUoVQ+Pi4jT6fhFt27w7b9sm+n5x9CsjXV1dXd2vd2ddXV1d3c+hiqERETEMq+i6+buzrpvHMKx0dXV1dTN2c7Z1dXV1dct0p1DFx+B/e3zcx2azT/4fwrq6urq65dq6urq6umW6OVU3NAAAgM+vmp9OAQAA9TA0AACA5AwNAAAgOUMDAABIztAAAACSMzQAAIDkDA0AACA5QwMAAEjO0AAAAJIzNAAAgOQMDQAAIDlDAwAASO6k9AU+ar3exXb7EsvlLM7PZ7q6urq6E3VztnV1dXV1y3RzqmZo7HavcXPzK+7vn36f9f0ihmEVs9k3XV1dXd1M3ZxtXV1dXd0y3Sk04ziOpS/xL66ubuPh4Tne3v5ct22b6Lp53N1d6+rq6upm6tZ4Z11dXV3d8qoYGuv1Li4vfx54/uOoV0i6urq6uuXaurq6urplulOp4mPw7fbl4PPNZq+rq6urm6Gbs62rq6urW6Y7lSqGxtnZ94PPl8vjlpyurq6ubrm2rq6urm6Z7lSqGBoXF6fR94to2+bdeds20feLo18Z6erq6up+vTvr6urq6n4OVQyNiIhhWEXXzd+ddd08hmGlq6urq5uxm7Otq6urq1umO4UqPgb/2+PjPjabffL/ENbV1dXVLdfW1dXV1S3Tzam6oQEAAHx+1fx0CgAAqIehAQAAJGdoAAAAyRkaAABAcoYGAACQnKEBAAAkZ2gAAADJGRoAAEByhgYAAJCcoQEAACRnaAAAAMkZGgAAQHInpS/wUev1Lrbbl1guZ3F+PtPV1dXVnaibs62rq6urW6abUzVDY7d7jZubX3F///T7rO8XMQyrmM2+6erq6upm6uZs6+rq6uqW6U6hGcdxLH2Jf3F1dRsPD8/x9vbnum3bRNfN4+7uWldXV1c3U7fGO+vq6urqllfF0Fivd3F5+fPA8x9HvULS1dXV1S3X1tXV1dUt051KFR+Db7cvB59vNntdXV1d3QzdnG1dXV1d3TLdqVQxNM7Ovh98vlwet+R0dXV1dcu1dXV1dXXLdKdSxdC4uDiNvl9E2zbvztu2ib5fHP3KSFdXV1f3691ZV1dXV/dzqGJoREQMwyq6bv7urOvmMQwrXV1dXd2M3ZxtXV1dXd0y3SlU8TH43x4f97HZ7JP/h7Curq6ubrm2rq6urm6Zbk7VDQ0AAODzq+anUwAAQD0MDQAAIDlDAwAASM7QAAAAkjM0AACA5AwNAAAgOUMDAABIztAAAACSMzQAAIDkDA0AACA5QwMAAEjO0AAAAJI7KX2Bj1qvd7HdvsRyOYvz85murq6u7kTdnG1dXV1d3TLdnKoZGrvda9zc/Ir7+6ffZ32/iGFYxWz2TVdXV1c3UzdnW1dXV1e3THcKzTiOY+lL/Iurq9t4eHiOt7c/123bJrpuHnd317q6urq6mbo13llXV1dXt7wqhsZ6vYvLy58Hnv846hWSrq6urm65tq6urq5ume5UqvgYfLt9Ofh8s9nr6urq6mbo5mzr6urq6pbpTqWKoXF29v3g8+XyuCWnq6urq1uuraurq6tbpjuVKobGxcVp9P0i2rZ5d962TfT94uhXRrq6urq6X+/Ourq6urqfQxVDIyJiGFbRdfN3Z103j2FY6erq6upm7OZs6+rq6uqW6U6hio/B//b4uI/NZp/8P4R1dXV1dcu1dXV1dXXLdHOqbmgAAACfXzU/nQIAAOphaAAAAMkZGgAAQHKGBgAAkJyhAQAAJGdoAAAAyRkaAABAcoYGAACQnKEBAAAkZ2gAAADJGRoAAEByhgYAAJDcSekLfNR6vYvt9iWWy1mcn890dXV1dSfq5mzr6urq6pbp5lTN0NjtXuPm5lfc3z/9Puv7RQzDKmazb7q6urq6mbo527q6urq6ZbpTaMZxHEtf4l9cXd3Gw8NzvL39uW7bNtF187i7u9bV1dXVzdSt8c66urq6uuVVMTTW611cXv488PzHUa+QdHV1dXXLtXV1dXV1y3SnUsXH4Nvty8Hnm81eV1dXVzdDN2dbV1dXV7dMdypVDI2zs+8Hny+Xxy05XV1dXd1ybV1dXV3dMt2pVDE0Li5Oo+8X0bbNu/O2baLvF0e/MtLV1dXV/Xp31tXV1dX9HKoYGhERw7CKrpu/O+u6eQzDSldXV1c3YzdnW1dXV1e3THcKVXwM/rfHx31sNvvk/yGsq6urq1uuraurq6tbpptTdUMDAAD4/Kr56RQAAFAPQwMAAEjO0AAAAJIzNAAAgOQMDQAAIDlDAwAASM7QAAAAkjM0AACA5AwNAAAgOUMDAABIztAAAACSOyl9gY9ar3ex3b7EcjmL8/OZrq6uru5E3ZxtXV1dXd0y3ZyqGRq73Wvc3PyK+/un32d9v4hhWMVs9k1XV1dXN1M3Z1tXV1dXt0x3Cs04jmPpS/yLq6vbeHh4jre3P9dt2ya6bh53d9e6urq6upm6Nd5ZV1dXV7e8KobGer2Ly8ufB57/OOoVkq6urq5uubaurq6ubpnuVKr4GHy7fTn4fLPZ6+rq6upm6OZs6+rq6uqW6U6liqFxdvb94PPl8rglp6urq6tbrq2rq6urW6Y7lSqGxsXFafT9Itq2eXfetk30/eLoV0a6urq6ul/vzrq6urq6n0MVQyMiYhhW0XXzd2ddN49hWOnq6urqZuzmbOvq6urqlulOoYqPwf/2+LiPzWaf/D+EdXV1dXXLtXV1dXV1y3Rzqm5oAAAAn181P50CAADqYWgAAADJGRoAAEByhgYAAJCcoQEAACRnaAAAAMkZGgAAQHKGBgAAkJyhAQAAJGdoAAAAyRkaAABAcoYGAACQ3EnpC3zUer2L7fYllstZnJ/PdHV1dXUn6uZs6+rq6uqW6eZUzdDY7V7j5uZX3N8//T7r+0UMwypms2+6urq6upm6Odu6urq6umW6U2jGcRxLX+JfXF3dxsPDc7y9/blu2zbRdfO4u7vW1dXV1c3UrfHOurq6urrlVTE01utdXF7+PPD8x1GvkHR1dXV1y7V1dXV1dct0p1LFx+Db7cvB55vNXldXV1c3QzdnW1dXV1e3THcqVQyNs7PvB58vl8ctOV1dXV3dcm1dXV1d3TLdqVQxNC4uTqPvF9G2zbvztm2i7xdHvzLS1dXV1f16d9bV1dXV/RyqGBoREcOwiq6bvzvrunkMw0pXV1dXN2M3Z1tXV1dXt0x3ClV8DP63x8d9bDb75P8hrKurq6tbrq2rq6urW6abU3VDAwAA+Pyq+ekUAABQD0MDAABIztAAAACSMzQAAIDkDA0AACA5QwMAAEjO0AAAAJIzNAAAgOQMDQAAIDlDAwAASM7QAAAAkjM0AACA5E5KX+Cj1utdbLcvsVzO4vx8pqurq6s7UTdnW1dXV1e3TDenaobGbvcaNze/4v7+6fdZ3y9iGFYxm33T1dXV1c3UzdnW1dXV1S3TnUIzjuNY+hL/4urqNh4enuPt7c9127aJrpvH3d21rq6urm6mbo131tXV1dUtr4qhsV7v4vLy54HnP456haSrq6urW66tq6urq1umO5UqPgbfbl8OPt9s9rq6urq6Gbo527q6urq6ZbpTqWJonJ19P/h8uTxuyenq6urqlmvr6urq6pbpTqWKoXFxcRp9v4i2bd6dt20Tfb84+pWRrq6uru7Xu7Ourq6u7udQxdCIiBiGVXTd/N1Z181jGFa6urq6uhm7Odu6urq6umW6U6jiY/C/PT7uY7PZJ/8PYV1dXV3dcm1dXV1d3TLdnKobGgAAwOdXzU+nAACAehgaAABAcoYGAACQnKEBAAAkZ2gAAADJGRoAAEByhgYAAJCcoQEAACRnaAAAAMkZGgAAQHKGBgAAkJyhAQAAJHdS+gIftV7vYrt9ieVyFufnM11dXV3dibo527q6urq6Zbo5VTM0drvXuLn5Fff3T7/P+n4Rw7CK2eybrq6urm6mbs62rq6urm6Z7hSacRzH0pf4F1dXt/Hw8Bxvb3+u27ZNdN087u6udXV1dXUzdWu8s66urq5ueVUMjfV6F5eXPw88/3HUKyRdXV1d3XJtXV1dXd0y3alU8TH4dvty8Plms9fV1dXVzdDN2dbV1dXVLdOdShVD4+zs+8Hny+VxS05XV1dXt1xbV1dXV7dMdypVDI2Li9Po+0W0bfPuvG2b6PvF0a+MdHV1dXW/3p11dXV1dT+HKoZGRMQwrKLr5u/Oum4ew7DS1dXV1c3YzdnW1dXV1S3TnUIVH4P/7fFxH5vNPvl/COvq6urqlmvr6urq6pbp5lTd0AAAAD6/an46BQAA1MPQAAAAkjM0AACA5AwNAAAgOUMDAABIztAAAACSMzQAAIDkDA0AACA5QwMAAEjO0AAAAJIzNAAAgOQMDQAAIDlDAwAASM7QAAAAkjM0AACA5AwNAAAgOUMDAABIztAAAACSMzQAAIDkDA0AACA5QwMAAEjO0AAAAJIzNAAAgOQMDQAAIDlDAwAASM7QAAAAkjM0AACA5AwNAAAgOUMDAABIztAAAACSMzQAAIDkDA0AACA5QwMAAEjO0AAAAJIzNAAAgOQMDQAAIDlDAwAASM7QAAAAkjM0AACA5P4H/9QSPDU64pYAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 1000x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "# for i in range(10):\n", "\n", "\n", "_, ax = plt.subplots(figsize=(10, 10))\n", "# ax.set_title(\"Sampling locations\")\n", "colors = ['#e41a1c', '#377eb8', '#4daf4a', '#984ea3', '#ff7f00', '#ffff33', '#a65628']\n", "# sampling_loc[0][0].shape[1]\n", "for i in range(grid_x.shape[1]):\n", "    x = grid_x[i].detach().cpu().numpy()\n", "    y = grid_y[i].detach().cpu().numpy()\n", "    ax.scatter(x, y , color='darkblue', s=20)\n", "plt.axis('off')\n", "plt.savefig(\"grid.png\", bbox_inches='tight', pad_inches=0, transparent=True)"]}, {"cell_type": "code", "execution_count": null, "id": "7ac73478", "metadata": {}, "outputs": [], "source": ["im_t1 = t2pil(im1)\n", "im_t2 = t2pil(im2)\n", "im_t3 = t2pil(im3)\n", "im_t4 = t2pil(im4)\n", "im_t = torch.cat((im_t1.unsqueeze(0), im_t2.unsqueeze(0), im_t3.unsqueeze(0), im_t4.unsqueeze(0)), dim=0)\n", "im_t.shape\n", "im"]}, {"cell_type": "code", "execution_count": null, "id": "0b0b82fa", "metadata": {}, "outputs": [], "source": ["xc.shape"]}, {"cell_type": "code", "execution_count": 15, "id": "15d584a1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--Return--\n", "None\n", "> \u001b[0;32m/tmp/ipykernel_1502426/1218053886.py\u001b[0m(7)\u001b[0;36m<module>\u001b[0;34m()\u001b[0m\n", "\u001b[0;32m      5 \u001b[0;31m\u001b[0my\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0my\u001b[0m\u001b[0;34m/\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;36m128\u001b[0m\u001b[0;34m//\u001b[0m\u001b[0;36m2\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0m\u001b[0;32m      6 \u001b[0;31m\u001b[0;31m# out = torch.cat((y_, x_), dim = 3)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0m\u001b[0;32m----> 7 \u001b[0;31m\u001b[0;32mimport\u001b[0m \u001b[0mpdb\u001b[0m\u001b[0;34m;\u001b[0m\u001b[0mpdb\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mset_trace\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0m\u001b[0;32m      8 \u001b[0;31m\u001b[0mout\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mtorch\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcat\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0my\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mx\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdim\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;36m3\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0m\u001b[0;32m      9 \u001b[0;31m\u001b[0;31m# out = out.cuda()\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0m\n", "ipdb> c\n"]}], "source": ["B, n_r, n_a = xc.shape\n", "x = xc.reshape(B, n_r, n_a, 1).float()\n", "x = x/(128//2)\n", "y = yc.reshape(B, n_r, n_a, 1).float()\n", "y = y/(128//2)\n", "# out = torch.cat((y_, x_), dim = 3)\n", "import pdb;pdb.set_trace()\n", "out = torch.cat((y, x), dim = 3)\n", "# out = out.cuda()\n", "# image = nn.functional.grid_sample(im_t, out, align_corners = True)\n", "# # print(image.shape)\n", "# image = nn.functional.interpolate(image, size=(128, 128), mode='bilinear', align_corners = True)"]}, {"cell_type": "code", "execution_count": 39, "id": "056fab35", "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([3, 128, 128])"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["im = Image.open('../0.8sem.png')\n", "im = im.resize((128, 128))\n", "ten = t2pil(im)\n", "ten.shape"]}, {"cell_type": "code", "execution_count": 40, "id": "b5035abe", "metadata": {}, "outputs": [], "source": ["in_im = nn.functional.grid_sample(ten.unsqueeze(0), out, align_corners = True)"]}, {"cell_type": "code", "execution_count": 42, "id": "29c7b056", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYEAAAGBCAIAAAAPO5pzAAEAAElEQVR4nOz9eZ/kuJElipoZFpLuHrlUldRqqdU9PTNvvv8Xer/33r2tUZVqy4jwhcRidv8wAATdPbOypNI2z1FZEeF0LiAJHBw7ZjAgPMr/oYWIDBERGWOstdZaY4iQiNBa6531zhFR2dlQ3YMIEcs5pP4DABARZhGRlHOIMcSYU2ZmZgZBIiKid++//O//83/99//5v37/7//xq19/9at/+dXhsBcWFskpL0sIYTmfL8/PH54/fDifTyLCzMuyfP/dd999990PP/zw/OHD8/OH15eXeZ7neV5C+Ls8vUf5mxX7967Ao/w5BRUmcFu2GxQUDJExxlhjjSUiIiREY8hZ66xFKmijUGWMoXYqANlgkIggAIsAMRIiIbJeCBG6OnDOMYQYgogYY5zzIAAg2bIxxlqDSMIZEcZxEBERWZaFiKxz0zQd9vvDYf/y9HQ6Ho+n4+VyySnnnDIziIiAgICIAJQPIuVz+fko/2TlgUH/fGVFmQo0VPiN/sC2wRQaVPDFkCIMGCJrjTVGoUwAEFE5ECEiKA8SAJQNBgkAiQgRlzMRkiB3eAgIWTEoBhGxxjrnEQEBRbjSMUMIxpqwmxRHlmVxzo3juN/t9/v94XA4HD48f/gwPA/H0zEsSwghpcgsIiy1MAsrjyofAfEBQ/985YFB/2SlEaDe1KoIQ6YBT0Mf/aU/FSUQidAQGTJIoAwCEBW9lAGh0iAo3xU8EgFgZsCCdEgMXNgPKCoBCOcUwhJDABFjrPeeEIlQRKzLPjnnrLXGe5dSUhYTQhjHcb8/PB2eDof90+Gw2+2898qbLpfz5XwOgdTuYyn/MXPOnBGAhZkR6708yj9VeWDQP1/Rvq6sp2k9VnHIGmOMrXYVlR9kCmFCKCYbFMqDINWGURbTSBDKSoIEelOHibHwIEQkQFFYBCIEgJxzXEKMUQSstc55a8gY0q8ys/feez+NY+asPCimeNgf5nk5Ho+Hw+FweBrG0RCBsLI2EDZEOefMmXNm5sycMwNkAABhQVQSVJHoUf5pygOD/jlLVXwMkTXGWavswlpjjQJSUZibDUZISBVfmvEEqqjw2m0FAAAb7BQYwqq5IAARMWKzxgCoP53knEJYUowgYq3z3ltrnDUAoIZT5qwSDzPrFVPOMcYY4+l4eno6HJ4OzlnhHMPCOQNzToEQkgpDmTIz5YyYAUrtscDoA4D++coDg/75ysYcUx7kWrEKRc46pUa2Cs2rtwsFACrEgAir1sNV3cVi0QgAFuEXAEC48SAqCESIjGt99I+cUliWFCMAWOuc895Z7w0idkIyN+VmFXeYT8fT09PT4fBEiDEsl9MpxpBiWOYzgiSTUqKkAEYIACxCTMTCTUZ/lH+28sCgf9yy8X3Vz02Etta60sW998577+yKQc0Zr1QImyttIzAr6FTnEguDUh0pP0CKZQYigghSffbF+LqqLSIKSM45hBCWJYaYYswpiStmYUOI7aHV3hMgNHqq8+n0+u759eVFMSiE2TobliWExSRKKVF3+aKOIzBTka3VewawetAe5R+1PDDoH7QU/1bn+VIhuMjLhqyxrhAe571zzqkVVpUhY4iaox0A1jCf4tte3dr6Q7dof8V+Z1i1XunPcK9fi0jOKYYwL/PpdHx5ft7td4gwDJ7a6W6PgoJBgGCM8d5P03h4evP+iy9SSjnnnNP5fDqfTohoTGx3RkjGGN0lZcuZMzdHmazydXWl/XLv51F+sfLAoH/QopZOJzQXXKl/WGuNU8zpTS9D9b/inqeiNAP05lcBEdn+tUYDiQDCaoX1h9YQHVgRCVZiIyIp5RCXZZ5Pp9PLy8vh6TAMXvigCCD3uFCL8EFEY633btrt3rx5E5ZFASjnZKwFAOYcQgkfMIaMzTlxsmWnXARrLqGTzDljRoYSXPRXfF+P8meXBwb9o5Ya51NsrmpddT/0l2lOMSph0NTEmhqyU8sVAK2o0pdmhtVjpP2qx5dDu72guPRXHnS5nI7Hl+fnN2/fPD09sfTnQcGiSrVL6LeIZK0V73e73Zu3b5k55ZRSTDmJSIoxhqDXQRA2xjFnywWlMuecU6rOs5xTKqK1EGJ+qEX/oOWBQf+gpYUgWmOcs94VvUf9Xyr7GNsokrrINPpnFZJArSqs5hMAVMOqIskWfirdacpRH39cAAt68GqEqACdCKeUAi7zfDkdj8/Pz+9e3y9fhnq6ZtY1YOzcbyJE6KxFkP1+LwBIFFNKKcWYYgiX82mZBxAGYUJQ6YeZM+eUOaWcUk4mp5xSooSoMU1MzILXEtSj/MOUBwb945bq+SJnrfdu8N57PzjvivjsVrdXFUgAOuiocxpAoMT6XEnRVwDUIZUUP/c2SHqVjJphIx1mlf1yTkGg8KAPz6fTKYZQAaioTaB864psCRAZQ2gtAaL13jqXUoopLstyOR9fn8d5GICzcCIqd8oiOXPOOaYcU4oxUyLECAgskpkw4wN//pHLA4P+sUrhLqspRmpseecG74dhUCRSQtQUIqysp4MUXv9oTiKABkPdv/ULWEFnFW56HUVWILmnryCwCOTEmZsmfToeQwjCwC0IqQIkdJ/1ZM2LZ6wbpslYtyzLsiyX8/nl+cM4TuM4AifmyLlMdWPhnDkzm5gMEWGiSno0kJooEwkyF4h+GGT/YOWBQX/ncjX5S0MJVQbyznnnhsGPwzAOg/e+hCHSxuPVU4obcWc1uKoJ9bHdNnhUi9x+6ITqe7tXysHMISyn0/F8Op1P5/P57IdS/4+hAK7mHjS8MtYOwzhNu8PT05t370RYQ65jDJxTzqlp4lhN14KRWB6vIYwp22RyzsV861H6oVX/vcsDg/7OpZ910bvArLODxv64YoX5EoLY/O5NtWlEBxvx6f9B3a054j+/ehtnVuVJlQndwSGNLUIEzjksy/l0Op9Op9PpfL6ICE3orIV70cyV/5WQbZWaEMBZN47jbr9/enpzefdehEU4pYiEYZGUUoEbnb9myIgAGJ04QoiG0BqyKdtocpnrUX1nkFlA+O59PMrfrjww6O9d6qwLa42zTXB2Gl88eOedd856nYpxHfoDxQDTM4F24XssqKa7qJh1ZY5JiwS6qd816+lm0n/qrpjzslQedD6fTidDOHj30ekUzVCStX5I5Jwbp2m/3z+9ebMsc+aUYliWWZg1FBtA57gJIRkCtBo1RCbrRBayydiUnTEx5ZRTTBkx56yx4fyYav93Lw8M+vuXOvPLWGtb0LP3fvB+UARaU5BRmXdRZl/oCZq+Uk2qjgYp/jRTbbU87iCJfOTvciisl2lIdOeQlsFjWebT8fWkttjxPHi/37POk10tv8p/ykXqmfVLInLe73a7ZTmEEHLOMYbL5TKcTjEsgNW/X5IBAKAhEiPIIpyNNTlb45KNKUVrTEwhYhPOkBgf6PMPUB4Y9PcvRQwyZK1xzg3DMA7DOIzDoFaYMyULhynJwmA1w6qgu/rBBESAV9c7dMSo7NTp0tXNviUnq2x0XdeGPh+JeK53BDmnZZ5BsJpj5/1+pxk2QDahOr2XvsVfK7waY4ZhMGRijJmZhc/n0+vryzCOl7OrRyKCACEKkrrcwACIMDMbZokp2WhMooo+wCw5Z0LkNSD8Uf5u5YFBf+dSuxwSkjXWOzt4P47jNE7j4IfBj961uEM9AADWRF4CArzSE2kAtEbx9HJO9bxXs+ezDKuutvU0P3lUznmZ5xhS4UGncwxBhPEqUmfj2C8A0cKbjLHGWOc4CwOhiLy8PI/TzvmBjFkREpEAsMUnKEhLmTpmo7GGTCTVyzKzAlA/fe5R/o7lgUF/03LrBTNE3nnvnPd+VNe7W7WflqZHj5VtVA0ArJyl/Og15AY023Ae6XaHe7/LobKeo9/8Ed2ou0k9l7AwCsewnM+nl5cP794/LcsSYipxzusEkk+E70iLVFC93jnn/OCHwRgLAICEwMJtbq1eHxFBNyFI0aq5RHvWyWRChIayMYm5T8748JT9rcsDg/52Bav8bIyxRgN/jDXGN//XMCgMOWeL7txNaWAp2XauKcwWK/oZGOXz6vKGugus4vRnkJqr8kk7bN0JUAAhxng6HV8+PJ++fL/MSwjRGENkG6sTkTWtyCbQqZwoM4NO4kXUxIzOeTJGAJFIcrnPbk7/BmcRWriDYXEaJEkI1hprk41GPWWaXy0Db0D7Uf765YFBf9OiDKhlHfPe+fLT9z+dzkAt9lchLzWJBlSEaVDUQEQ6naaYTT1itJ4pnbR8l9VckaC+Q1aNu0nKH+usBQhCWE7H48vLh/PpNC8hhggeDQki9nStoJDecBcDKZo+VkSBxDrnh9E6h0QKXresbhMWjWuiJVfwCAyhNRhjDsZYa2LMKaWIAEnjvNcH+Ch/g/LAoL9dwT6Uzlrv3TgM4zjUSRh1Mph1bX58mfYOoJ4caJLyRtOpunIpKyRtsGlriHXw9JmdrV7o4zzoxqYTEAgh6OTV0+mstpgxBry7ilrWP5qXrFpMIFCy1WuWbOvcMI7WOUQSQACSjTknAFgQrZwZkcAAwTr3Ba0hl0ywyVoTgiGM6qtjFsgPb/3fujww6G9bavp3Y4yzbhj8NI7TNA7Oe++cLVPAsE4/VSfY2rc3bqktO9EdVkog6zEb0JDOEVbNsY+U2wvAenWALQu60nUERLN/hDAfX19eX57P51NYYozJe6fJN7rzS4FoFARkboFNwFLyNyouO++HcTTGVk6J7Y5L6rUOK/V5I6AYREIjJFZypuxMStlGE4whRJWucs6YET+hTT3KX6c8MOhvV6TyIEQ0hqw1mtr9sNupHuSd0/Ee176+isqV0qxb+o9bCaOyJGnoI5s921738OfTNGC1936KLIgACy/z8kqv+5fn8/m8hBBjqgLQjR2HVXeHwkpaQUSdOef94LyKQYBE5Sa6ySptwh1U/1gn6iuuGRZOKQdrjDUAkJlTSrFEniPiJwzMR/nlywOD/rqlOIuJENHUZECDxh8OdfqFcy1DUAv/WYMLN+bSiijtYw89G5f8eshG3Kl/riFCHy+b77bYJHBNGXA9oAVP1hT3y7xczpfT8XQ+HcfRMe+IOiIkIHgnv4Zs7lxy5pTTsixhCSEEqXQJsTPGBACqyL1CUElpokhMgiJgWYQl2qLN6TQOAEmZCXNzlv0UJj/KX1oeGPRXLN1EyuIFUxfY4P00jtM4Dt5bq8ufUvMul/4oTe6R0uE73LkyZDYBOytmdWpR04Oud7vtXlcwt/1ujXD8nPuvGXxyTileLueX5w8vL292uylnbk6wztsvdYGMihwiXMOeUkrLslzOl9eX1/P5tMwX4SzqKwSErRQtUnOPrMFHW3IJoPER1hjn7SgMIIBoDcWYI6XEXGaXPXz1f+XywKC/btHl/UxxwLth8MMwjIMfh7HMhneuLXDajmp0pi8fsRB6jtMknusdtn75nkV9bql23bX11PjQfSEFhYUz5xTj5XR6/vD87v3Lu3dvmblOOaEeXFFE0YRaCBGDRu0oBp1P59Px9Xw6LfOieaOhiD5dVUUXXO3EnXLqVVrDkotarDMDOKijRTBEFJEQY1IcRuaHafZXLQ8M+isWAQ1FISKyzmoU4jSN0zhqJiC1xqyxxlBb+h1WA2Aj4tw9/ebzqv9sKQ/cGhQ/ERR0f+SXJkT1mu/trrg5hJlzijGczqfnDz++vn6xLEtmQRQ0QJrnUUQAULogn7ridJt0EmKa5/l0Oh2Px/lyCcuyxkB1+bK3z6eGS6IAaCyAtPMbo2nQLGnqbqKajqAEKGVmQl0C7VH+iuWBQX/lImtMkDrjp3Hc73bjMJRAoDIT1TQxVXoU6svWHKt/3PxrLrJ1h/L3Vku+0pLWjfduYnXJ9Xt8SrztEgkB5BjC+Xj68OOPx9fXsIScmZCs6UBMSrJHLM7DnsQIC4cQLpfL8Xg8H0/z5RLDokskYvON1ZvUU9ZFiYrStLHLdGlGqRkLNBWc1expKodzyrllxX2Uv2p5YNBft4haCoiGyFnrBz+N424ap2lSKZSQdArqJjBvjR0soIM3JtanLqrHbmTp8rsLkYZPAMhP35V26U/10Da/jXMqkYo//vijplXMOVtj4Joylc9lUm45vMygWJZwOV+Or6/n02meL80Qu6VidwFIQyKLVF1dZQYBwIqwTcbnbAiZJWeOMZeVsB8usr9+eWDQL1+wBAGRLs6juRCdc+p9Vx+Y984aoyRI1R8U4I1kfIUgzYjo4gyvdZ8+fBpgQ6W6jRV/rn357UPnruqC/9q5Sy1L90QsC85r9baPQqDECdVUHsfz+Xy5XM6nMwI4a8iYq0vjih/rRmZZluV8OZ+Ox8vlHJZQ1Gq13jardPS3tPra2pnVyENEqj4zzZpGCCkZ54zTn9F6l0FAGDDjY4Wyv155YNAvXxrDt9Y6Z6dhGMdhmsZxHDQdKxHpnqvQez9i+YbIbPCoF4yuRWzpOvDq5l87dn+ln9WvrnqhLr6K0K1itvlaj0EQkZRiWOb5cjm+vj5/+KALH+qqYdBm5K5231qYhXNe5vlyvlwu57AsKYYeVfuZYu2WsAGoaOTR7Y1g07f0aCyxo3bwTtOM6KS9mHNOnHIW6bJiP8ovVB4Y9MsXRCRCa83g3TAM0zjspnGaxmms81FLGHQxuiot6c9xiz6bv7dkZ+sU64Bpc57ry1xJS5v6VzNns2+ta0M7RBSpPKgDgqvTAQpoAtYQlsv5/Pr6+vz8PI5DfvOkFBD7+REVLmWdv8qZeZ5n5VBLmGOKxSN2e7E7d7RFo/7usZi+upUInTXsrbr8qzGGGGOQxCKcHz6yX748MOiXLwjFF+acGwc/TeNumna7aRwGP2hmd1P8Pti6NfQgApvOdAUWV6h0hTXddx+ZkFptqTtffaLUo+6oUniPZmDf4RGZOcYYluVyPr++vL48v7x986ZIOuuMuBUqlQvhaoiFy+VyPp/m+RLDklNs9UdVsq9med2Dnar4Y+VJnSaGgIi2GoaIUIIlpEjUUedxPBTqv0J5YNAvX9oAXpZOH8fdNO130zgMmilaw4Go+p7bUWu5L4Vu0KcjO7AFo54K1Qi/7eGfL29vL1/PL61nKxVqlb4pbZkd4RRjMMv5fHp9eXl9eZmLcx0INwiy2mI61UskZ17m+XK5nE+nZZ5DCDmn6sevO0JbyrHqVLAFoP7RaUwjQJ00Ii0FmjGkafANkcJfyjnEhI8pHH+18sCgX74UM4GICJ2zKgbtpmk3jaRLEq6zorbusI+2cbn7ce2266XvCNWtTj1T+JkwVNWk9fxS55TUeRH37KK2jTNHDoB4Pp1enp9fX1/DEqSQkdUD3k+OQARBVBoyL8vlfD4dj8sypxhzzhpXtKLfFXf8GP4oPStRjCuH0nU4NKeRtYaZiUh9ZEuIZXLsn4vdj/Lp8sCgX6aox0XXByND3rvRNyeYLSk5nCONm0asrmVZFSEAWLkGAIJw341uVeuN9NN5xOQKcDbnv3Zja28vAu7db69qsEKfko/iGcf6FD5ShFmAsy62cTofT5fLJSyLzl8h6tqhiIiUORwiOXNKaT5f1BZLMeSUlMr0z+anSttjU0WpyRaxvBYCEEQSwmjIWqP/nDXeWwEpqSEfPrJftDww6JcpujahKRMgrcZDqwTkrDWGyqRtuA5mUX9xrzED6E5yw/0LdboSgG64z+3+3V9N2emw7z5luDpXBbrbW7/KGXa/FBEGU4rz5Xw+n07H4+vrEQDpQNZqLg7NSVandCFkzimlGOP5cr6cz5fzOcXEnG/u8vpzo2BV7em+7Gp4Ndm1rdOhjnxDZK0ZnItjBigLlumq9plZGPgBQ79EeWDQL1WwJod241g0oN1umjp//LXrSxqDaNu3AjPKVZ9pFtx2Z7ntiP3+27/0yCtw0xjiG115I/Suf7Uadzsg3Gq2110fUSTFqBB0Oh1fX1+ttdM01rtE7OQgEFASFEJQAFrmOefEOUOVe+qBK9J2ahDIHQMRtjHYXe1gJZoigIg6ogyD4zo1HwEXjMqDMj4ss1+mPDDoFysa8GatGbyfpnG3mw4VhHRiapsY/3Hm0kqHSnInj30J+bkj/TRz7PZUKwO6W/2fvkO5c3AVSj51GFSEE5AYwvl0PJ+Ox9fT6/PLNE05s+6ywmCFoZRzjBWDLucUI6ckzJvo6it0vOcQ+4wb295GiWtXP305kwgIS8qZAPkxh+OXKw8M+mWKiCCg5jx2zo3jsJvG3W7aTaOGSNclMsrO9bhP+Frk7t9XsT9S45fvQdL27172vg4D/JwbVGzr1aDV6b3pkdUjdYUUGuIdwgKAlQa9vn33TtYzFnLTzMSUcowxLOFyvlzO55Riznn18vXm1p27vi7Yanb//qq5CEgIaMsbM0RIZVGgmBIthISQf3IUeZTPLQ8M+mWKgoC2cmPN4N00DvtpPBz2pvrC7mqZJRhwNWoKvlxnOt0KQN0XGzvurvx8T/a+6a2frQndbl4nbFWit/rk+90AhDksSwzxfDoej8fX12MIQU9Kqge1CoOIgIpB8zxfLufL+ZxTYs7C3S135tid+vaVgusP2POl8oRL2JauYysg1hjvLCDkzDGlssxkqeQDhH6Z8sCgv7ToEqmGyHuvszG8K0kRyRBpRghEJBS+kWXu/w13t3e69TWSbf7ewtCqCEGnJN30n/tJDHF74salVgpSkpB9ptmjQgoAhxDOp9Px9eV8Pi3zHIbBWWMNVZkJKg9KIYTz+XQ5n+f5knMW5hoZVFUs6bHv9h4qB2s3dL+qW1qHSIQiAIYERNcFssY4a5y1eWB9E8zq3nyA0V9UHhj0FxXVgJy1ujrYbjfupmkcBudKMLQIgxgp+mVxdX3CALubj6eVDYEp5tdKn9bdrkOGt6e4d37tnBsRumk4n1G2+yH81NEppfP5fHx90Vms3nuYRmM8aC2K/iIxpWUJ59P5cjnP8yw12f3qzCo+xI1wv9YB122yqVZX0f53/3V9siKgc4+tM8PgdnnAuuBAjClKhgcn+svKA4P+ooJYNCDv/TQOu2k67KbdNKkzzBgCqL3kIz6sHnQKAqxd9759Idff39v7FobugFVfj5vL3Isw/giA3VA6RBVWbvZWewdTDCoIHY/H8/ms/HGNM6p3EGNcluV8Ps2XyzLPLAwleWutYOWF9VL3qtfdy+1t1Wn0Nw9A1CIGAFAe5J3LAwsLCGi26ZwZMf9sae1RtuWBQX9p0dZrjRkUhnbTYb9TZ5jmqAeAzojZdNiPsh786aFVmqVR42ngBoa2u68Mqq/Cuu2eMXa94WMF17vbbP5ImsUQwun4enx90dXod7vdfs/VENOsZJJyjiHO86yGWFhm4ZKvA1av+5YENSXqhhdt7/aqOuUfrjpW5zVAtMaIiAwOAYo4nXOMCQAQHi76v7Q8MOgvKyKAwMyo0zKGYTeNh/3Oe+etNQ2DsOmn/aTzTf/cTrq8K23cYVLX81K7cGm4tRHu4Frtwnd650c1nvvIgiDy0UM21wIIy/L6+nJ8fVVbLITQ4oywSswqBs3zfL6c58slpYTA2D8juVKjtzxqo0mvl17x5hO3W7FMAMqi24REaIgyc0zZLZEQWfoU1Y/yZ5YHBv1FpfV/ETGFCo27aZrGgQiJiEWYWfk7FAc3QLHPPqKOflISalfuoaZtal/CHRgSuEWln1nuHt6Y3aeDZioMiAgvyxxSPB5fz6fT6XSq3jHEInMjAJSFNC6Xy/kyz7NIVvztBejunN3Vb5VqKdXrY7qx/GzW2LaiAoBQvAmEloWdNRRDzItLVOVzkLvI/ig/ozww6M8sdY61GQY/eF8mZRQdejtAY5nZWTrwjVL7cVpR16PZOLe6PzbG1oYPbRz496IBOhHq5jv4SAjNte7cWWD3DK5P2CkiAsw5pfP59Pzhw/sv3s+Xi3dOVxjRtVVjDIpBy3wJYRYpCx4BtJUMi9QsVzXYaEXVOKtYVWFoK1rflPpVS+eq8+yFEI0hZ633dj8NIcQQMT8cZH9BeWDQn18MGe/tOHidE19RqEtOWlRWWftqtRc6ueFO01XAQrgzZ6wKQN3HO99fc57mlL9/THfhKzi8zsuz7nets3wKvTa1a+E1klI8n07Pzx9Ox+PlMvthGLx3RBp3GUJcliYGLeoHxDLxvZ1rZUBylc2+u+iaH/Ze2FLbfzWct1WWTls3hpwx3tlp8HrPDAC3s9ge5bPLA4P+zCIiiGCNdc5O43DY76axTJUnQmER8xF7q3UHKInW4Soi8ScvfefznaMVhrr6rp/+nAH745BzDasfVZg6fiICIDHG4/H15cOH0/E4L/O4jNYYY60Gc4awzJd5vpyXeY4h6AOvGLP6DxsbqleQj1SjB6Abi+zGS99/UKuXpejTztlx8DlnFokp2ZxzuovUj/JZ5YFBf35hEc1APHi3m0bNUjZ4Z41dhYemixZJYm2seP3lpiDiGlXUFiO9Mr/u/AWNBpUP14bYJ7vKp/vRR2DoIxt+YjsIh7C8vry8vDyfTqdlXkII4zSpqM4iYVku8+VyuYRljmGBitY159C2Qlv7617V76ru92jRpu6VdCIiijUGPGRmXbhxiallg/oIYXyUny4PDPrzS84ZBHQV+XHwT/u9akPGGoDSgLtI5Y2dU3vmFZ0oW7BSqGu/1idqU73z0p25bvrp3lGEko8CxrobFKHnjoz1kaz2DTy7KGURQVjm+eX5+fXlRV1jMUYRYQARFuFlWS7n8zJrEvtYSaWuhFg6/JptAz4WA73Z2n9fMwdBW/AH7uxX0l0DgCEiR0SkUYshJmtIlyfIj0T3f0F5YNCfU7CmK0NEnQ5mjdUlDJ131hgdKltoYjUUOl0Iek0I1sQ115fqFSG5RoiPu97llgF95KDu6A176vukzobr7r+jcMU0qhLuqq1sdry+FAAIxBBYjufT6XI+H4/H/WGftTCnlOZ5vlwu8zyHsOQUibBl+JG2uvzVR6gfSx0+KgPhprLluFs9qKlLGrWkK7LqA7F1Io7GM2mq1wcT+jMK/b0r8M9XiMgQDd4f9rtpHHS1DDKoq8FIlU5rwbUnaPL3TnvAq37QJtZ32yqpkW5Dpzv/hPn055SVPW2J3I1f7CNuJYSPKkL9XogiIsw5p/Pp+OMP37++vi7LEkLIKWtk0OV8CsucQmgnvr1fWS/0MQS4q0zd//IKhfDmFjVjNYsgknPWO+udHQfvrEG83f1Rfro8MOhnF8QSvN9WDRuHwVnXAnyFV+5zrS6UP26kT7zpKdg23mUSPTu64Ufdfqv49HmlV7J7GFqZUEGfG+FkA6bd2bbn7r8XEOYcYzwejx9+/PF0PIUQQggpp2VZ5svlfD6HZYkpfKq2G6Hs/qPAbX20tohy+x4+/hEASuJE/cJa8t56Z50z3tmamuUBQj+7PDDozyk6a9E7O43jNI7jOIyjt9aADu/NU9P3UkWTNQ071n4rG9KwacZN1/5YPGNPk2QFjY90xZ+kTJ2QVOhPg6F6ga1lczXw3/1wRyOqGowwc45heX15ef7w4/l8ijGFGHPmZZkv8+V8PsWw5Ji2lazAeCXFb2en3XCSjj/eQMWGt+qbu/vAixqGxlBJkOCsd2ViID5ipv+s8tCDfnYRruFqRMPgduMwjcN+mqyzumQPbBScTcGqCq2KSpd851aE7i97Y6Lds0o2fzfB+/MirwE6CqQfOh50ZQYh4LbT41XnxnXrR2UhFsE8z/PL84fn5+fL5ZJSMjESkU4Tu5zPMYZUVhPbVBU3Hys9+XwWcmMs4uccX61p5ywALEtwzlhrsMzvQ40WepSfVR4Y9LOLRq8AAjNbY6Zp0jVUnXfaG1hEV0PHSmGwLSejknTJN1GXpFBO0NTqPiXYde+VLfrcItF271uJ++Z2Ovl5E45dBafeELsCIWgKe8PTe1DwEQQCUNl+vpw//Pjjuy++nC+XZVnIGuedYlBY5hgC54TXsLO5xV5D+9h+m4o3fLwnBkG9gbvqDhEiGWsJAZyzzlpCZGaty0OT/jPKA4N+XtH5Gd67aRytVRUAdPoiYQnw7fvcNmxEF9EBuQ6c/qnyiX023Enu7nyNUgX4cLMFN9+W466snc6J3SjWynTWvVaxaEP59KRbKwcBmHMIiy4D/eHDjyxsrDkdj+fzKcaQU8o5X9Wtj766NoBuVJ2PyTw3tuJHEPQWiUQEJKaUc9a5gMqMFLUfkzZ+bnlg0M8rxRvi/TQN+93orFW/LAuLrDwc1zUx7ky30D2KVQabHg0AsC7a1c5z//Cu3BOAPglw21mezWK66c9F2MLmnFbt6ercKxX6rBoIlJXeAQGFJeeUYjgeX3/84XtjaJym4/F4Pp1SjLqQBtGtuIMlcvp+uYaaKvPc2++nrLAehnRmWGaOMSoGIaCzNrOUOWOSHxD0s8pDk/55RUQMkbN28H43jePgraaLBsy8WW9KHbVb9bZuKLKCfvF517277SeP3crbjTIUi05qIsYm+9xcrO+6CGUlwHYv/S3hxpTBjc7b11nWMEqtE0vOOS3L8vr6/OHHH06nU4xReVCKMad05zRVA/pE2UBkQ5n7T+zO1jteedFYAs45hxBiymqCGUPeWV0emj4Oio/ysfLAoJ9Xcs6iExetHYdht5uGwWuKGRVw706Bv2qZeOev24PqDh/bp9e+N6Vpxff2aLPmZV2R4x6Juq1PKx+t7HrVO5bOWoOrP5g5p7jMl5fn5+cPHy7n8/H19Xw8ahL7ZojhnUM/8nHzgDu3Y/VFNovu7p1ub2yzj4hk5pRyjIlzVr+Es9Y6Y2rMND9ipn9meWDQzy45Z0QkQ87Z3TRO0+icc86SBt1tg2XxpqVfBeP+uePmx7rj5x67Ebc/D4lU+brOttO+rfvAXePmY/cpwjnnlC6X84cff3h5fp7n+fj6ejqddDEfzndWVf3ce8aVrNUqlKpdyc+fqOeqPTUASkmXGEJEZ41ztjGgIk4/ys8pDwz6GYUIrbWi86d10QyiwTun9hhRFUCa/2vTom+9v7VDbLds9r8VVW/LPblJ7uyxUZs/cXCpSR/QXRJnV1GozwaGV3i6Ruqtdhr2H2GdgasfdHbYfDy+ns+ny+X84ccfj6+v8+Wii/nU3Qp56/DzM9TfO3Ty+nnW17KC8F1DjJlTSjGllFLOrBikczVKqjMEUcfZwyD7OeWhSX9u0e43eDcOw+C9984ao+OeJkssMWpYRWXotGYE2Aq+xSiQ7nNbZO8KF/CutvtJwVnLSnJApM4i2TjgV0F2ExmJ5XvcVAsRkYgAWBirVC56dJcfsvGhKgZfO+42XrQS2oMozCnGZSkk6OX5QwyBcxa+WdO0PaOPzJG93oLtB6yV6qIJ4IoZrQcibGWgGBWAcl3VpwAhIVlDuhhZxJxSBvhpeHwULQ8M+tyiFogxpi4i5gbviSjnbIxhkUYpV3jounD5CFLdXgI3pKi12oZQXXSh9tXtAYV0Xdd0hacVcqQzYm6wsP6xkXIQBKQsO6g/CEl01jgIcg8uVzlJ6nk2+KO+LLjNOY0AiMw5xjBfLi/PH1LKr68vuqoqr0mq5dqxf3ueze+rvzcPT/pgpoZEuKpG0KGITqRNOSsHYq7peUGEhQiNJWONrk+fs9RIxQcIfVZ5YNBnFwEEFGZdQkOpkHeuQIsIrz14NTU0SxkCQM8c6o5y/df9cvt9C28EgG3imjvnagI0yDrHodKfUslac+hvAgD7Xk9ADEJUwBElw7bDX015uHMbXaU6rEMAyDmnFC/n04cPH8KynI7HYogJ1wes02Dukh+5PfkmPyKsVKevIlbC9gnrSRlQyjnGFJOiYglTVbg1RNYoBBkiqi/jAUCfWx4Y9LlFW13MGUCcs5oqaDeNpAJATf/cyn1g2SIU4Bon05OgZiisAdOdaYddxtKraICtAXCtW8vWKtrITdUca7fQYqelo0gaH0QiIsiIGyhp8tXqvL9b+ltqsCc5Jwh4Oh3ND9/HEC6Xc87pSuJtF/uU3iL3vr5C2/VE17vecYQVAIop5RRZoKVkQQA0ROgwZ7bGGGOk5hJ6GGKfXx4Y9LlFx1ZDlDMjoHfOWTv6AQl5XQC0a98NNbSjyRZoWpYyWLfoHleS0NVkLywJ8lu4s0Zd33PHdEetYUA3YlOfTgS3TAFlVbakQouICBKibPKQ9FZk98cGbLun0VCxAV1OkUVOp6MI5pxTTioGtfpXEtQJOVu4uQtMvXTe7VgdZis9+rgOnXNSAKoBQaXeCASIhoQkGmOtQQBmIbxjbj7KJ8rDL/ZZRT1i3rndNDpnSySISLieTtn38k4Fqr9wVWA+0k6x/MOue8Cmh2DrM7jpVwWVqscI2haolknVO+rh13E9n+o6q8qjjjGiEq/Yyc+tJtgfcP+0VzxB9GaFOaWYUuSUmLOGnt8iZ0GzDl4Ukq6eqwJVvfEus8bGLrsyzkopInQqbrAyK6PVtSspc5v+ovPp1WP68I59Znlg0GcVEdCEed773W5yzuqid8x8253KWI3Q1qJZC64t/fPb6KaLFwTZiBsdHWlZN+Baf+pOhivhaX9+CjD6qzcvvf7b6ri9ArPW79N32lFDEOYYQ04x51TI5e09bFnhFY6069U0rx3sbP6GzY+uis0LllJKMRZHmLDc2lfKLrlgHBkqeTxwM6fuUT5dHhj0WUXbn+buHAav2YKstYCYuTpBpBca7ukyXXe5EyPXmM/Wvlipy/qr7oobNiSwAk83I6I72XpcO7qesPTRO525/cZquGEDoKvRvqvmaujcAtB1tZpRCZxziiGnyJyZ87Vb//pUd/BnW/FyW9g9s55pNrhaT9N54tdoIC556aAfVKpjHlFTa6Kzmsej5nu9U69HuVMeetDnlsyCCMxsjJmmcRgGY43mLQOoaTpgK1IgVPcV3muRzXkt7QOsna0729bts4JOmTq6jrnNEKtn3YzdFWKkcwn1EYaNy3y0+2gXJiQhQBESYSzi1FX891bx2p5zvVHc1FAk5wScm+kESmfuyswr4H1UflkVn44EXdVxBUkFDp11yjnlFJOqQLn3IlxlrgYAIiIBa41ztk7ZeWjSP6M8MOhzS06Ji/gs1pjdNHrvBu+ZV6mgRL9cS8hXmwDbnG8BuJoZvwmqkfVLbDmHAGr2dcTWI1YMbISsl4OgLhDUaFVnmKxm1BUcbXz+9TtCZATlQYy3VkevOd2xUyu8VjRs1wYQYc4MUCkW3UxO6/RoWE2q9lC3+N+428p9cLN3X2koD11ZUE45pZRyccXrI6910NepjwsR0RACAHu7xFhDueUxaePzywODPqsYY5y13rvdNKprDBA0Vu2695ZyZYshwLVrrB1Re2DnCsM+NeDWlwYAgC3M595U974yZUxuAITbwIDPfgDYAUelD4gaDtPiifu9bwEIK128vuyte0sA7ocCyVVNrmuJ679GlK7utOFvH0BQIJuli0VMJRQRrm+kkVWtB4toxCKzEKJ3NuecLa+HP8ony0MP+txirTHG+MENgydCnc+diybdd4lrJah4Z+Beqg3c/LpmUNu9NoBU54JsJYqyW+tbjZNUNoD9yX5uqR27/urK7X43B9/8sd0gd/bqAgraHr241uqytQK7M6+UqcUsrbSoL6sJVhhQ4bx9PEHZETr0FwFhYQDRRELWGk1sZ+jRsz63PJ7UZ5WcMyAYY6yx4+C99whISHdmTcodb1j9vPafTb/tYaNu6slVww21JzqZFguz2qYMQVi5wKo8N+zouEB/DOLdmvRn7etXlOnqHNui6ZWh0/EngM6uuX89We3K6+336tJgaBNrgHdvpyn+/eMt3q3OBEtXjrBa4Sq0oQiWyPMSMI0AaC1ZYwhJM649SNBnlgcGfW7JqUzGsNYe9jvvLREi4Sq/XLmIrq2kYlzd9Zk0vaJDmNUZ1ves25PKKni3mqwwVP8oIvSfRYD6amotEFQSoprTbCO13D3oo6XpK1vQ2Hr2pG68d8KeBnW+wvYAoH2l9VsjBwoHE+6tsBIMJFdA2OI8m+SvAIQIBGgNDd4bUy790IM+vzz0oM8tSwhcMiWis3Y3TeqD5SpblvaM0GKdaxGsMzmqPbYdr9ssj9rFpH3X0uDfqCu32dKkIWGRTVY/mJ5Crla16QMfu63dMVd7bXcqUTGIXD6tde1uAfqHo1+3892HlKpgdb/bNTcqEdbKYQtEvKnlOnekPpz1u1JHXYMgrwCUNwjSO8Wu5wBD8cQjeHIiQefeiEjKDwz63PLgQZ9VnHO7abTWOmdFZNaVP2t07G3rx1W5vFPKGLwG7uL6f2c3Xck8nRAC0JMa7E54V4upB31ChK52x82NbE94ZUIq0ygh07C5+kc415UJ+pFy1x22imvbXTqzaqV+sD7c7quClX1IVR8PnXPW+ajryVtOypZ4shfgNmgLKbMAaELFtujYo/xkeTymzyzivbPWOmudtcIcU4bada8FkRuLqTeeeiMAVuujwkj/C2DbUa+CjLCab53xVfsh9J1wK9piDQz6yXIXgK7igPCqtJrd1PTqZrryCai+n6Wss/zqrx7yygO943/f3JGApkZsDEg1oHtKlIgArwhUtKDuNaNab4ZKajtjzP2bepSb8sCgzyoxJgBw1lprrLPW2uaVvxFCN+DRT1Jdd6s/e4zA6rn5NDzcmi+4hi83btVXZL3oZ5z+o+W+RVZ5ELbad0SuA8Q7xt7NfdwWufmjXHStEq6Yuuo8nfLTPreadBxI6rT4aoGtRrSagFj307ihopZLJbmr3x6BGYgKAikDyg9z7PPKA4M+t4QQEcEa46ydpqkNdGveVvxI57o7ksMqZ2yoTsOIe7xh43Cr3Q/WPoZdZ+vttA1N6EHoMz03N9533bhywIZCG5K0/XVzgo9fDnrOd1tHgS5c+eYiHdRUdN7eRx0dOmd8zjlzLkFXPVWqYhALcIOfJpar0oWIgES4zlYFzFdpsB/l4+WBQZ9ViGheQsoZiQjJEA3et57Xu7KqB6qTFNpZupEa1iCVjT1RTrOBpvsRduuB1ejqLBJcIekK4lpFWw1uVNt67JWBdVdvksKDqF7tHuPp76fVHUvMlPR3flOukEi2hmAzR1ca2N9ff+MrT0QVypUEcc4pM+eSF7HnsAWqoKFPX6FNlBIios4U08LCKeW7zPFRbssDgz6rOGv3+wmhkJoQ4zUKlNIhT+e/6fxF16WBSr+l+wurqbUZ3xG0DzeY6SyhG6DoHUlXRtq2R9/oOP1JtsSjYUHDMcI1mcfGMLs65+2junu5j2+8PcEGZascfYWlV0c0sKiT8zfaXeeDryHpV2wMoTt3OXnO3OqonOhTd/gotTwe008XRGRmZy0SAaIxBgRyziuVqfvBtmPc7dL9TquAceei0NCjH8DbmRGAWg264b9dvF1te6mrKmz65U8+Cqhk7qa2qz3Wn2fzgK4stU9eA+Bj03zL9erZy2MpUF4/rFs7ZndVI5GSlLUK37f1khJ+dVOTBnD12tgyK2idzJrU9VF+ojww6LNKZmYWnRiNCM65Oh++U2JqqY3/ozBUv4Nu+MXOFOhUnkaEVpazcqct0tROtqLVCmMda2no1oPUrZF1VdlrZtVdVPEHqEOgW8Ly+Ur4J2rSo0h38moFYvfE6kUbIdvWCACQqxtM+vPL+mJEELqIxBX8V/xZSZAIqETYAIgfGPR55YFBP110QJvnhVmMISScxsFZDe/86Q5TT3JnI/Q9Cq826++KI71nZzUFOtOjoVTd5bov3/iGGlR+ygRbEfX25rpbQEWiOm+j6TIrrN47/iPXvWJLlaXccJXeE7Yhease1jGz7TCBWOaa1qCf7nrbUKAajHjzTNZ/iGCMcc4ioqbviB9ZovpRbssDg366IKK11hjSOGltYVYX17zqouuIi2tTrXtcB/9vj7pGjMYpuiG3I1Z4ffS9/lzhqa/FlkZU/INtl137Lm7O1rasd7np7NBVURodWaGuA07oznD3kUDH1trO/R7tRNg4T0fF7oHe+hy7KXYImgsFr+XmTgbqbvf2CSn+EnFmKG9Z1E1/58Ye5aY8HtNPFx3cdrvJqj8eIcTYU/i1d10P4bDtDHInevoKwbbm3fbnHcbSoO5Ki14rs7kKbv/Gq7p+5P4BukuvrA2v73NDgW7Ocffn9cP4CJJuLKztM8bKIduxDZmwr9B6eAEhKJFN9dauIkqbI+yuLLUR4HUDJE1+hGjIDN7hbf6jR7lXHhj0WSXnTETDOBhjdH5QiBHq2L5lFpt+3sSX+rWsP7EaGP0BlbFsAagb39dK9QToWr1eK9JzmZWm3cGfn5YvsIpLGxTobhW3l1u/XmGn5Npu8y7w42HSHwNcgDbJtbvRWsF6Pbw+srdb9aeuyiw3T0IvALB5Js3YvSJBBSNz1oRuaAw5ZxFJ+LG+xmeVBwb9dFE9IoRAhOM4GGu8t4BQguU6BlC7zXX7X8f8VWBYx9oqdzazqGNDq8DSqNB63vUSTTG65mDNBlnxclM1QLjvgNrUf4NeHRL0F1p7+A3/6iHxCroQrgEA8d7f9/ZpeA0bWtaeFHSPZLWKG33RDBtd2vzyRtcI6O65bACI6miwfRTGGGPIO0eEKef1RI/yyfKYN//TRX0nl8scQnTWDsNQFpvP3OkF2ogFATXNIWDfiivflxaNgjd9H2tUEUrf6VAAivFQV/W6dfuWy8Jtnkb9cs0xW66LHQe73h37zllxo/a27fmxPwqxrIXaszvp/tA7xKvO+RP9dOWZjYf1F+6h+QplOwDekEoRqIli1yeN9dGJoHS336PfSoBoZVVajDGI4B3HmHRNRIGHd/6zyoMH/XRBRDK0300558u8CEsMSb+AfjysvUSaNNOxIllnWmwCp2Er97RTAnQ6SDXmrgWG9fv28cqIa3oFrDbI9ubWajbbrftq7aJt53qqlfb1gNBZh7D1Bjbba71WI31143qbjS513O4K0RFu7maFic1z63gqdtsFQGWd/haaLdw/B6ycFBCRegMZ1qcBKMYSixii3TSamkzoUT5dHhhUSjeWIpU16tb+kHMmY6wxXBNc6YxE3HbNDU5U6LkqdYytpKSCVyX5hd/j2i/XkXxrf3SkANees233ePXz2rAqPzenbA/kE4/qareVhkDt/3C1vtcNtt1c8foqW8CUntW099WzHuwvst7kapx1r1i/okqLbs7VzrA5FgmJAPu8bR24abZfY8gPjgzVFlL26trUo2zK/7G2mK6M3tiwxo+1mYTOOQBIKemeOg8+c0YEay0RlT0FyOjfGEIkQ2SIczbGsIjpEAhEzZCWO74aTFjnYq9oJKV/yqpWYM3WpVtFcyPqXsVuQylLhqoxs/bgtatXa0Kbfc2MhiU//CYgUrf3EIntQNwYdGvt691eZVXT/etHbEYnbM7domxqSLK0qm2fzqabVsiULYn6REdupHKlqNgRREBAIMCse1K5YSlPvNxeuyhgddx3K8s2TCtPhBCRjCVrDYsMgyOilBJiWZBOU00DABli5pQy1DRDKWV9UrpIlKZn0G+hm3mvd/x/qmn3fw4GadOQaoWTMS2tb0pJ024AQk68hDB457xDgBDTsoRpGq01zDzPizFmt5uIMIR4ucz7/URkQoyqB1lryRDRGoyPnQhUOj9Ikao3+R3Wmqpi0tsjZWdYe2mTrGsP2Vyi68rYzIoVxwCkYKJij8JNTeR4BTxXf9TqbBTZa3rVDroSh9bjur8KIxLcCFkFcbGKY5tXub3IzVWhQWZBGWls5OqwFTBWSEJCZC5GVX22mywrVxkp6xk6zrNyUyREXfnSWMMCIcSY0ryE/W5yzjLz5bKMg3fOsfDlshhjxsErAF149oPTeNcYU87sndXxMqWcc7DGKFAKi+rc7X3/HwNJ/4dgUM1rDK1xOueUACMAiwzeKVFGgMxcMgFZi4g5Z0AkRGctC2dma+w4eiIKIeacjTWGaAlxXsK8LIiYU/bOMTMLb9qsFNIhHTvpot7W4ezKmKmtvRdJSylo0uU5ROyYFfan7zuSIqAO7ko2ytpC3W6Nx12VtdobUoKFJTSg6wBBaduaaPW6hzTYVJq0sVTvqegdj4FeaW6IfcWXag170MGPlLazFKUfWe2xj6y8tCVBUEQ2BEQ0xhgyw+DHccjMztkQU4zp97/7F+ccM+fM5ktyzjLLEuJhv1OjTBnQ+3dPxpAIaP6Q3TQSEdTMatYWqq05hDG2MEpRDBJuT/CfGI/+WTHIGCIkzYOnHwfvyZCyHiLa7abBO0LSqV773TgMXmVCnfmlMGSMISoMgpCMUXaD1pphGJyz87xkzsaQsXaZlxh18V/OmbUd1o5V2nPthghCgGU1erWjKlEhAu6gph3QyD9sbRPt93q0JlFXUKomWeVQba1TaECHlQeBNOut8TSoJt3KLLrfn8H/sS6lenVk+VWdg+u3n+4oK8WAzpjqLtZv29a57lHvfgNH2P3dyE0n0+ScyxsrC7GhJqjWt7s9fkUfIiRjnLXOOSVBkFFCSjn/53/8bvAu55yzKEVKMS0h6rJ02gJ1rixiST6dU44pp5gAQEByzkuI87xgbZMhpgvMddIiceYlhCQZAKw1SKTZIPuH+c9ClP5pMIjq0ubCwiKGjCusVXJmARjHYRwH3ZJSds7tdtPofdFuiIZxGAdvrVVm5EzJiEiEIGDMZo0aTcc5DF509XHO6nyNMcWUECFztl2+zs4qQ6yWUddlrvSO4jxbWZB+0zOkxieaNVZEJ5B1jVZo4lDdob9K4yYA1QRrKkzdqeNXK7fo4aIiwvqxqekg/Tf1PrXfclfJwg7bI9r46/sKb269yjhN0Fm/7hGqUibs7Lkt+GysqPJ+SnRQ+XR9sygI1AwxRFiVID0NGdJQIOfLfwCASDmff/+7fzHGaLPRGRuIGEJsaRoJNXYDmDOokZVySinGrNPUUs4hhBCSCBARMy8hXuZlGsfG3Od5AYDdzlprESGlPM9zSskUCQJFdLGFfwIY+ufAIERNT0PFNhYYvPPeOWuV2hDhMHjv/TB4nTqIiM66cRymcTTG5Jx1n8Nhb61dloWzWGOmaSQijeZw1ozjkGKOKQKg9857F2MSEWOIDGm+vWIIFAIPUGTjq+XVdVxt426lQtvu19sU5XMjMyIAWDCnCi3d91ddGbERMoB+HfrW/aG/cNu13wD97+sdVxOmu/YWX+udY6F0nRaG6ynX59Ge3dXL7unNlcGFVQzGdS+stcMKRBWSimqD3Wkr/WnIpQSyUcZi6FJb5B5R3WcdrhGSMcZ7O3jvvXfOGWMEMKX05RfvWNgQkSFhYWZEctbGlGJMKhE4a5clqGitgtH5sqSYAME5l1K6XJaYEghaazPzPC8hJmZBJIAyCqaUM7MS4xjjsgRjDRlCJABR5VvZ1mfw2b9z+QfFoPbg9A8i8oNX3qGUYDeO01R4jSEqK6B6PwxewUhPYq0ZvJ+mCco6hei92+8mOOwVUIwx3jmYxpyziBgyfucBxhATAlhjrDHrV5PTo4jQGOI+A3ofl4ewEg7Baq8J9p203Wl30Mp5SqeVplgISgWg1RyrRtbKvCpUtb5+vWSybmq0p3x7NSVhrVqDodJ1S9e8pwl1pbw3rEJ4sf0qUnfSfeNxDRo2ttj6cwWQ+oCx+7F9qNiX3iiDQoMQkVMTgPqhoayGu063LcNEBR9EJOOccc4Ogx+HwVprjFWaRIgsUtsGM4qzVpVKAXHWOm8RMOfsvR0Ga4wBkSXEaRpoNyrTn+fgnEVAax0CXpZlN43MQIasdSnlmFLKZUEpFmHmJYTz+fzy8qp+El2AO8R4Op3VOtONAPCPuerZPygGGSI1jjThJhEN3o3j4KwjKoLOfpp2u3EcBu8dEarpNAzeD947p61BNR/vXGZWBk5EiAQozlnvbetO6hyFgnHgrFGxqZIeZGb1jltjWJgz9/31ZlRfdYd1jawu6WtTH+6XDTUqSCEIzSjD1QW1ErAKQLiGYnfEo8Ja6a3VwY7Vh3cPTD5euxWc7o2wWJQUaGvjFJ4hVRtvtKPVtSFNoyzNzIL6Dio2rVe5A0DQgOYOElUKJQBY5rULIoH6FjqTsdWmqdtEZKyx1jrnlCPbSsPb+GGJAERYQMAQgSYgErHW6CsvTciaGjIv1moO/KJhTdOg8/UR0RA5ZzRu2xjrh4FZUi7L3FnnEHGel+Pp9Pz84p178/RkDKml9vJ6XJYFAJw1+pZiSiE8MOjjpTSx0jfEGHLO2epfR8RpGqdpnMZRPVxE5J0bx+Gwn6Zx1JGHDHnvpt00DkOMMTM3KpTr0IE19lBA1qbfFQFZafnWh6XJGbQLsojCQm/pCHQwVKgN9pe4cvrC5siKGViDjKBxDix1Kl1I6uhc5ZZ6Fm1t25uqCFXBQMqOq1Zzw5bW13JNhXpFaWUR7aYLWYMOO9u96P8iTXdff2N3iYZAq/WEQFARqFGy6jDD/kGucNX2h36TnqDgeKkwqS3bqt2fq16SyJCijwKQ+jSoZtIuQFqwvbWIrmH00hysQaqy+gqxrJaIBioAlQQgiNa6YZz2+0PKOcSISN4Ph6cDIr28vP744cObw/7Xv/qSWWKM5/P5+flVuw7nDACZcwhJRCKm+hpBQfAfwUL7h8AgrEwXaofw3o2Dd84aMsaQMUaVnWkad9M4TRMiKM0Zvd/tpnHwhbYgWCJDaAYvAJkzALBuN6T0VZTC6ItY1ZCe1Eht/7i2lPYttwbUHd6Uhe62oDav0gvXXwVKbna/0oJXXUW7XqVC2peql71WvXjKVmtONidt5h4ovEKnYGHH0fr30urR11L6PbCYXqshWQlaB6btIqWelYB1Z8Vt768kqFpj2L2gLQBt0KqzmaD9qNRoxSAAIhIAESBELnW/vUnUhkBGBSCnHFtzRSs6dG+5vNwOavqRpo/YWH3rUA3XNgZTq78I52SInPPjNDnvjQFEGof9OE5v3r6ZdrtlXgzC4G18/04fzI8/fnj+8Dw4O3qzG1yIMaUcQpjnBUBiSnprlYRB8+7/Hcs/BAYZopoSDDILAAze76ZxHIfBO2uNIeO9azCkQjIROWu0cVhrRMSCKY2MGeobBQCRlmwcStevmgooDGEb4GEDF1ccpgJAGfdaMOHHSulz61k2Js+N9SOwwsrGZdSLJ6s+ja2TVzWjHIkr0bq+XA8QXQfpqEq95MZd1N1AfTrrkN6YRf/3+rSx8AyoVKFsrxxo7b/NrFoNKlgNqCY513pgVY9WwGpA076pllR/KgQo9Ke738IW69DUnjWRsdZ674ZhUDPflvwt25dXX9k1oayOx82S0bJSoY62QQUiEWYQEWbnPRkUYeEsmZxz4zgM42ANWUM0Dghvnp4OKUZhZs4OZTA4ODMN7jANIYRlCefL5eVoMufLPAOg1SBs5hAT898/3+PfB4NwfdggImpDaYQoIYrAOPppGg/7aTcOw+B1/oRzdhyG3TRO06jHIqExRiN9NDswFJRRu7eNvJuL1+YibZAGkWqTQNMn1y6qfRq7PlSLTqmHAh/9l9ekvqPnsu4iVzVrlg5uxKOetrW+V2lPrxA3TKhg24FZIyNKzaTjIjc16Wpz55Y2YY4d97n3N95mhO+vtmLS9QMrsNL22ao8FVe6czY2vS2VD7XTdC1E4z0Qqc1r6ZAREQ0hOXXAe+/reIhXANRVt4Zwra+j/0O/6GYLbuGn7akspQl/IAAsnHNGY4wIIwghOmOECJhzSmDJGiJAT2hQLMrOUTiMy7Icz+fnF4sAIGwNUV16aF4iM8cILbpX/k7m2d8BgxCbG6G0c+/s4N04+EZ7nLPj4Kdx3O/Gw2E3eMcsgGiNcc56ZwuX5vLS1drC4riRoumsfRqqgQXYaR9dsJ6WKpmUv7vgmoIMXRCbjp2V2EK1y1ZW3bgUbOTnClnQanPd1SvewYp6lQoVaVcNMeVBAFgDDjtxRYrYdc11qqMNGshWLeMq3nDzvmDtI5Ut3uNBlSqunGibzaOYTnKNQN3pOpEZOyqDjdPUp1EZTmU3UAGIus9b3Oq7u0BFsea9rjQGC5UkY4x1bvDODd47Z6yOkbSSsR6IPh4T2F5xZ61v63b1MEorKiOkqK8dMokYYypFyikERDQg1lnlZjlFh7L31jxNbyaLIKfz5YcPxhA6i/vJXZZdzryEeL4sRMicK/iqi01D7f7WIPR340GGqqIHOHg/qZ01ekUia42zVn1ho/fj6AHKzqSuBxEoaagQQDGoddBiKdWIwXLNxmzaAF92XOm3/m6w1MycFcm09u0EiHWKWrHv2sX666x9vp4MqnMIoHngy46NYKxVwqKhFsQphkiBoZVb4XpzW04G9cKrOYbtWnc5UP+moDtdu6/eTKvedqgI1NEcvNs3K7hKPeeGozSCoNuwbmrfrVDV1aKxHsCaXb8hEbR/gNgImj47XK3TSmMUgKxzbhiHcfTWOQ0OVFCtj7rcSF+T+rm+6nrjbeBpD7UDxxV9FAtrAyvjCjMDZBFhJu+YmTmnHGMiYwwRAIrkmAREcrbIu8EMZszZMmeQnNJEJNNg5sMYYrxcltNl8dYYQtHoEgH1tGlAEef/P8AgrGMWGV2em4bBqZG1n4ZpGqfRE5G1xjs3DG4cnLO2Te1ZB+Q2F0CK3xqpNu86SMNGEamvdkOMoCPM7XMneNQi0npMsYAKNVmBqHBZ7Lg6doc3s2kFoxXi1qdTKQ2u3xbAAcD1m8oymjXVnVTuS0IVOAsv67gLSKVqhRPce2t3P23dcIpAXW0KelaUWvF+VasbtujX6xuGFVO2rKF02wZOKwD1a5zdkiDYnhpEuBiuUHgHiJQl453zw+CHcfDeGWs0vqYCTq3y5g0X7CijRh0YpGrPvQne6tIACPtzNIQWEBAWRkERJAQVhnJKmWJEYCKVzQmFQFAEJRsUICFEBCPsASZncPI2xJhTPp4vo7eWEERySjohLTPHlOcQU2bIq/8e7zaFX7r8LTCota4afKEQhBpbaIoLkpw14zg+7Xe7adSjjCFrDBGpisYVBarGfNPNuHlx9cJw8wwrr+gJ0TpY9dOnNoZZ/1fnrm8w0RMF6brl7Uu8rk8ZfLcWGXY3JQ3ppMZdQxtKNSwOejyBTpMuMtdV/XEFwoaJCH0NsBKrWw6zgl71K28mw6u22/Gg6nOqO9XfpbuWujT4W4Fo7Z/lRNBssEZqOoShTgpqhOceACnYlMfZDDcoz1ibm3POl1ICQWolevS5LrVtdU9M6r/1vVbc7RBxbU3t4a+Nr3/+aoOlRCGAiGRDSCAEgCgGEUE4R5BsqEBctpScwckPjoQHAJkGaw0hCKEMDuclxpSXmC5zEJEQc84quJcFZvmvv07aXx2DcG1SpeEophij2XgKCAFCZkk5p5wR0TlrDYmmARJIievoAQDF9KnDIJRNlRN1ZKe8044KrR2gN11akb554XrAZtsGXtaG11EPuIKh2uhhjR+8OedmO67ZbACaHF4foUCJWEQgrMFMjdyInq3gkrQHUGlX4VNQB/76CNoD6+u3gZgtt6uM7VM8CGooU2U+3Q1Ck2Jq31Zhq3zS4yoJAuhxpD33gj/U3PEraepMnQ0w6XFVL8NGagkQ0GgMkE7CMGVKM9X6Vm61gdwObkHWJlE4cSVBnfG4QUpYn9X6GCvF3hSdt5hzSlFz0ggnRCQQQiAEi4UogXBDPkIZrHEEAE44M3OKMU4OePQGDqNdQros8XQJ1hgBzLzO7WCWzCz5tiq/cPnrYxBidbsrQiARWoPqXDQGjSWDCAIsElNeQrosoaTXMAYtCkMGbs0LGs0u5KBcpTJ8qWNsCwmGQjT0r7VqFY9Km+lNpK3psXax7duoB5YTN5dQd50WDdcNbYwbB/wtV4J2xlq90ipr9o1ygfWZVDOxxRlUrMRqD61V7YhPt1OpXE/+pL3BxlDalnK+TV4RaJdeBRaFZpW8OoCvvEPalXouUNGkQQ6uDKjeet149bMdByty3QAQdcOXCNdPxljnvR/8MAxeCThoiGy93Br5g5vHsWkSRRuUtWXWmjcTsvIdWG+vf+rXA6NO0xZETClpT8qcIpGij0E0CExkSBuWIAgiEAIBWEIGFGEhIIDRU56cQRm9iXEMMR8vi3MzkdHBXnsiQO2Pkrhm/vsrlV8eg/RdtWZJhNaSM0b9go2VKBIZi9YgkQjnFGMgmg1ZazQ5hnPWiRWjk9rLalDljVHfTgtjqVJJ61y1St2PawtrQ30ajVjVio3fbNM3u43Nb7/qPRUAS0srElPrtFfNDAEBtzyhB4x1n4Yo0johIfB1ptMrRnf1ghq8QTXZ1nNCZS1QCV09qF+KVC2am9hqaXPVqiVYcaj2w96t31hF4f5Qx5W+ozd8qXjSgxRi8YXBrU++ok9pL7j+h9xGKmEiEhCDhkyzv7yztiWabseWkPLet4A9AEkZAysGlUfYM6D66KAhUcfXr968QrgICIKw6PPGnAAlc9bJZYRgkSyhIxJDFlHRB0EMaatkFCZgAQYUMDB4KzJYY6ZhYJGY2J8uxliomR52U5kRsoR4WULKOVUIahTpfsP6c8svjEGIaKqDFABExBhyxozeemecMcbQOkTXdiIAIDnFcBFhzinlaRzGcZh4aFovEXKx33UaaLX/i2usNON1FGFAAunaTA8162jUgAVrdFA9SYO0j3Tm9YtuGG9ESLZ7dVSjXbWajxXGOvcYKGB1FK3+tXIQbW2EKM1h35291qG7CS4XQiAEbl9f4cL6VLRmNUChGiFYOw2u03A3kF4rXp9mtXf6q0DrvxsTpZxsw3mhAdAVr2kA9BH0aSjVnUHbCayaPZCxCGCts847552zVgFoy8ygq2nFku5OWmRNCxirtwIdp8bN1s3jXs+2DqGrXQ8V4TKCJM719giRCZlIiMSgkAo9YAiQgQgABIVLOAkiAHnnCGnwXilqymyMRTRk7DAM+/1uCVHlodNlZpE6Zb+0J50o+cvC0C+PQZpDVz3vgOCt2Q1u9G5wxluydRoxVNIAiIklJo45z3MKMc5LWJZxWuIyxmkcxmEYB2etJQEhKKsakBADkAAjaK8qg1RxZK8X2OTPqewGoG8I5ftVQC23s8UfvAWOetO6NzZ5RSlGM6V0L+hjrDcUByo63IsxafxnFcChkSwEQJICBj0xQYCraWBYUqdh8e6XG6wOev24JWfQAKwhT/MMdftsirR70ZCuarHBlSjUQcT6dLvXUTrvxi4rwFIeegWhO9DTH7BeBTcDj4gQGQAxxng/OOeddRp/37/Vho593bpb70AHpPoWqhsQu0u221yB9eb5rW20vrpCSqt9B0Jc70eEAMWsGMREBsUQAKEOwO3q9TGQd+iskxJshilzYgKyzg+7aZpDnJdwnpfjeW6TXTUahlmYOesckn9kDDKGvIaz12jS0bvDNOxHPw1u9HZwBkRSyppgSW8lZg4xh8Qhc8oSliWlvIQ4hRhiCiGFOAzeeWdt9ZOiusAEEICgiQ1YQ4wRURN91CGMjC4KVQas2s67Man+aLo2QO32XVNpvRXaPq1XrZpP78e/eUiIzRZq8bn1MCk4d22I6UfdrRg31enVZOnWQ1qWxtUuUlggYV4zBAp299xNqey6HDRFp3WqfodND1p1p2bTVRO0jAwbhN2gQ/9oG+KtiNPoDDR6U3XoFXbWhPN09XvFIK1d9XOVlQ6GYRiGwVpnaE1Kt1ZnRfvyuaYakfb+O/lZtse3j/W5tQdYz9c3ov4Z99SoFeYa/CYiLITARGyw8CBDjCAkQEiWBA30j4AI0VgyIABI2imYRcAY64ZhWEKMKS8hvp4u3p+NMQKgSaw1jihnnd4R8y86/f4XxyAzjINOsHDOWiJnafJmN9j96PajnwYPwktIMZacqMwSc15MtjHblEPiiCgAkvM8LzlzCCmkXMIX2bGzzhptRQQI1PtiygvSLHhYlmLRRmvJGCSjA4uIMGfh6uNfGdHGjNhCQOlghRY0s0/HvbJP3dD+gGbN9TQK72FTAaftGIMbj1mjJm0+vgAgEGnKYcRG5QAARFO+wXojgihIGj3QOcTKLSjj2VZstS5veNAKS8Ig0KEJFDSt43XpdBsaeHWR/hlsSVDFn45B9Gu9I0KdcX6/NOJUHl0RyMWQKZmnvLemNqnyUDozceV9bV5eretK66R/ymVLRzBbRbtz9sNY3ae95JLWSXoAqjgkopnMMzcMYkImyASWkA2CNYSGDRgxUB6nQSQypiw0S0RkECmzJAZA45zLzAISYhoHbysJAoRpHBR9lhARl5RyhF9Spf6LMOh2KLPWjsOwP+wP+91umobBD86MzozeTN7uBrMfLHCe52WeQ1AkyjnGZClbk2xIhIkAEgsLc65T8XLOKXNmzcyCZfETBCACEAWc+sYbGQfUHJyERH6cjPVAJjNrGC2ocZtSjFEasGiPFIZq20A34JWbBlhllirjNKuttU/pgn763lkgqOHmFnOw5dnZbIPSl7XZd/2kudtL7QsZwWIPiWjWj4KnDEQsLEgCwmWobbLT+lbLX8210+6hKkEVK7AG2/B6kpUPoSChMFQBBttc/x4y+sfa7qVhwLrXyojaMoM97tzBoE23r88RBVkYETUBqzOaz7df+mvLWbr6NSaqjUNWJ2CleGUQ5G6sahHb7UniFQg3bK1PEGoL6BkQZ9ZeoaMn55xJJCEkhESQCJxBZ0msQXDG6DtBAhWOCMkiGVIGVBaKJSTw3htrdVQjwssSADALCCARDd6f50Wnd5wuc2aeQ6SU6oMRWKMw/8zyl2JQWw5QyzRNh8Ph7du3b98cng77w343eOudGawZvJmcmbwRTvP5Ml8u82Ve5jksMRhjTLKGNG/hYtISc0h5iTlBhphCiFlz7qaUUt7JOA4OgJryXIb2rmus4x8hGTsMu3G3d35EY0Uw5ZRyAkAQBbgUQ1jmGaCN94zCmvG3Pe71qTczq/EhqM0G69hfJdsmclf7pB1WH+PVU+150i1hQoBGeOqf5WaLeUWF2iAQSMO7clJGJEZhKH7cAiNdTVbYrMZU3wO7qQqIRAQCRefCTnvS+0bExpsKVuGqlUCPD/0W7bOt09YXCogIhHRlYlWTC/AjEnU7c70rANSlmQ0i6lJNaw2a/t6jbbvl7jmV8aeyW6iajWJQvXhz5a6JPq457TUDqs+8ok9W+GHOmbWkmFKIKSYUdgiOwBlwhIMzMngitCxZgNVFBmSQAEldGAo9gohIgoQEjpyt5BQRBWm3S4kFiZyzu2k8X5bTeXb2jIgp87LEtiwaF4C8GjV/XvmLMIiI1lRyiEQ0TdPh6endu3dffvHu3bu3b988jYMziM6it2Z0ZvCGU5xPp8v5tCLRsrglLEuwJjljnDHepiVlbxWJOCxzjCnEGGJcljiHuJt0Zpnz3iKQaLwwAwAQaOvSdlkIgAhb6968+WLcHYzzIMKcY04hxBCCMOecUozLssyXSwgLgCAIMYtkzrFabcpCKjDUbr9hOxskAgERvPd6Kp1a+3nr7JU99Qpx7bulpwv0s+NB+YXuKG24blcBRGn0SVBxWwCFEeEO7Wq1k8odCs+qSgiUrk8l8TJUfMFaI8bCDbDPqqZvRdbpWtB1wLXK2HAHG0Xot6wfywKD/VfbvfoLtD8ICS0BgAbrr9dWK7ribTu+O7ara3luOsTkBj1EBEIFgTVpJyDWeWbtMu3DCo6tEgIirHO5FHp0qZ8YUoopx5hiiiGmGEjYG2JnwBl0hggNS8qSGDJDLg5TfccF2KXgOcn6EKVwo5Ky3ex3LIJExju3G8NlWrx3RKRusstlWWJU8EEWZpa/bAmPvwiDDJF3mtTSGWsI6elweHo6vHv37suvfvXVV198+cX7wTsQ1oAFZ8hbAk7DMA3jdBlOfjh5f57nxc+L94tbQgjRmRiS8TEFm5eoSMRZQDjNF9GwhSWMu2mcoh9HN3jnnTHWYDXKGQRACARBABmEc4rLPJ9PR+OG/dP79+/fW2tEWNcwuMzzMi8hppxiWJZlmefL+XQ+xRAABDhzTpxjjqHGL0vPhwA2QS+tJV2xGNk0tY7l69DX7XzLfuru2KZuSFOi9SRdeGSFIawPRIX6klSAKmxVUnanXFe1q9dq7yJp6mLOjIibdcOFRMNKKwzVY7WhFwoAlatubqMxu3qZHr8QYaM4N7lvi06wMqf+zPWmqP2+4aD1BBV8G/R0SNRRKgEGzpLBWEfk9TVwTgCggosUbaxeqYYmbeq2PjedRsoi1Q+VOcaUYowhxRBjjCnEFCOnJMzOoKBhxsyYMiKxZWEkIWKkJAgCRgDUIgMSvZHSPupdEBFhlxMSnXPjyIg0eJdSnpdgjBGBmNL5sgzejkFX4pOcOYOIIN/TNz+z/GU8yJDzbhzHcRx0gYE3T0/v3rx5OuwP+/3bN2+++vKr/WEvwjln4KxIxCmicWgdWW+9d857f569d7NzblmWYG3wMYaYQkzeppDSEnPMHBlYmAVSCGeQnHNZ/J2F2TkWa401BAYIpIo6TIACnFMI8+WMDsCCWAJ8//6Ld+/fv333VkDOp9PlcrnMyxJCCEmBaJ7P5/P5dDwej0fOSTjnuKS45BiEs3ZtpUtVBmhduqpFdVStsWY3paBEnZyDlRn1+LUFJGyGwLb/ELYwagAognxla1C3F5MMRVRBw5Zh7C4YXfc4aN2nrPpHpBkLNpWRtYMi1iavZ8KWzx4qWGyIUDlThZ/VELvxeXWfoLPG1t93AKhxKgCG4lTtb7TyvYY+DbjaduwAUm9RCJxbH4GIsDBrVhnVSlqc8TqVo7/nGtvYjC9m5sx1wZ+UQgpqAoTIKWlTBGYEAUZhZM4CFq0lP5hhQOfAWCbK6ngQRAEWYBFqz6N/J9jBEyAgWevGAZy1IiPnfDyd53nxlpyhwdHobRxszpxyjqAI9Bf5yf5iHmTNNPr9TvPLj0+Hw9s3T0+H/X43joMmOCQAWlc9ReQUyHqy3tjBem/dYIfRns/Oeecuzs7WmhhsqDAUYvQ2LTHPMYXEiXPmHAODytYp55x3LMJOWMAZAAMgIFKQSBgAc4wRZgASQc5g0BjSlVb9mzdPX335K+fsPM+XeZ7nZQlBF94NYTmfTifFoePx5fk5hjmFJcclpyA5NJ4BKxK1/wtxgVUFkPbqAaCiyzogtR3aPl3AG0Izm1RarZaRVM0Hy5Q0JdZGrSVAEqjz6wVEJKeMKQkEZobGhq4c57XGxal1YzZVRkMgILido6t9XIoHqn7VLLDNA7jagn037/Gk7nwnHmjd1EFHPe0tDJU9qonS3W9DPex3125ZqkIlKAAQNMd9swM1Hk5YdF67sCBAybOxmupNPilPpMi5vfCcc045xZRSVpsr6sqtMeYYJWfQGWE1898wjcPofVnrypnBk3NAhgGzCDAXCCchARbYPJQNmurYhohkjVHTPafEwgBCKNagszg4M3oTo00poyIuY7pLmj+7/DkY1C5oCL0zk7eHyR/2w36/Oxx2b/bTYRr30zQNGllI0voSsxAiGusHJLJusH6wfrR+cM5b76131ll7sWEJPoQQY4wxRBNi8jH5aJaY55DmmGMMOecYUwhxCeEyh9007qZhGn0Zp6TYZGRAADPE8saZmbOmojJknBudccICIuM4HvZ7JLxc5mVZQowpcc6ZWU7n0+l4/PHHH7//7ocffvj+fDpSnHOcJQfhLMKAjCKAAuoHAmjhitiJR01P6jSBqqdURrSyng0DWrdifZ5NianqtJ6BiBwZZ4wlY8lYJFP8g0AAmFOaL5fT6TWlF4T716t4VAlcxyo26LD22IZB0ACDBLmIV9DCg2rnXgOP1ruCduYtlekAR4HgI6HRbR2wDbD16KPbdV2uuotUj6p+SW1/KGDTIvIJDaExJd28tWSdriZOZIy1iJBjUl8uAEjmnGKO0aBp6JNZZQV9sDpyiSrNij7qe0kxt9A4RZ+cEqckwtYa550bnPNuGAevgXO6yAcRWassh0VSBSAiYZZMork3SQR1aKosrx/oWnvDomowChOKQbAEltAbGi0tpStRQkb8W2FQi8molQRryFvNXOvf7oc3T+Nu53cDjUYMB0gLpAWyITI63UWkJGOSkq0NjXV+3BGR1UVznbHWWGu8syEYt5gYjLcxWOOtGaxZbB6sGWJeUo5JIqfLJZ8uyziEeYlLiEsYpmmYBu+99WLBNHUIAVKSmXNKcRHOxS2/hBjC+y/eEx5KhDegZs4HACVEKeU3Twf5l385n5dvv/3uu++//+H773/8/ruXD9+lcOG8ACdhJfgMGppU2jn3k6w36u/a+7s5bNUGw06pKfRdtohUupa0M+CKZkRkrRus884NxjljnTHWGEtkEYmZjy/PIjJfTumaRetdAMAanILSKNfaatcRtHZdAO4wqNhTWO5nZXgI0uQYXPcqJ+8aGNYt1RRamU5DhRsBet3cnxZ7GIIN8xRdUhU3ANSwjNarEaIxpDOtrYK7IWs1/YyxFgQ4JbBIZJUe5hg42mysYofSG+a1+4AI5/qf0p9UoKcBUEGfnI3BYbDOW+e9G5wbvB+8G7wxRvNwIbW8gCAouvwYohBJZkYiFEFmXc+ecCXwsHltlRSJgLDGroSwhCWkGCRnA+INJmuERZhSZqODG1ZHSBkef4ZI/TMwCBHKTWIR80ZnRkejw52np8m9fxqnafDODIaNBAnncLKYA5IBMkgEZIBqsLK2BSLUReGMdd6DTAhgDEZr3ELOUrAUg86zR2fI2eytGVxSNhQShyyRJcXldOaY0hLTknJIeUxu9M4765yxBkCjqqrNtJxfOfF8mS+ny/l0Ph+Pl6++fP/Fu7fvnszgG6oP3o/eqxqXmVW7evvui/df/su333zz3Z+ePvzwp9PLjzGcNEW6CKDOOl6zsZZHV12uyoHaFNNehl6pwuqRgjKCdu/h6uU2fCqgRGSMc8M4DePOD8oxB+e8tc5Yp0Kpcz7n/Pr6IcWEa9BOB3jNdLy6jYYUFYdWmKiaNGpIEGLxytScbtVmROyBeaPUXLGhLQmiaxK01mDlZFhSbWxOu2IQITIUkwOg5dlsrE570xUAERokQ2gNWWuc1eZqrCXrCFGdFvoEkIAIDFkiYjLZ2BRMpphTEhHGXF4ot7C3gj7F5urQJ8UonInQOzKTs9ZYZ/3g3eCc98Zba60uOY01OkYtRVChr1xHsgjVwMZcvlL8qRJCeZmlMemeKaVlCSpOnE7nZZ5TiJIziThCbygbTLnyQkNGBNp/UmLCPhNYfg4PKlSUiDToEkdvRoejxdHKaHiiNFGyZDxGkxcJFM8AaUYyqDHK1CKVi/EpAEiGyBpjxVpm5ziD5I7+YSSwBNZgMGQiGUJLaBANoaFMMWPixAI5xiDq6oopxTzmEgrtBEBVolokBR0I5Jk5LMsyL8uitl9+etoPo3fO1nwjAAjeWQAYvGjq62Hw4+Cd82QcoDm/mhwvnJbWYQUIitnfjK4iz4rcTE/tkAjXCRulk6zjSkWCe+MM1plyOAzDu6/+5d37r968fe+cd36wzjur6ZB13KZvpgE4ffjhT8v5rNNJmVqFW726VCStilvSXQaSgkG1emqBdeBUVpvFEl4AHfeBijiwYg40aCtgV3lPR36g+eNhNQ83J+oBaLMFiqWO/fWLun0FQAQGyWi2PTW+6j9jERGYuYw3IDlLZkIy1lrriCimDIBEBiwKQFnBQhWBkgoohxBiCTmJMQTFIBBGEWuQnFXZ0jlr1QM9OOuscbbw2haa1xhpmy5edHeFIfVCaAIPaf5jKsMXNLFEStxuCjHMy3y5zKfz6Xw6L/McQ8gpCueaGETTEKKCc5ZVVWdFoM8mQp+LQQhgCJ0lrwMAERFO3oyOJgejlcnk0eQBowEwWTAxhJwxQXIVegwaqxjUwxCA5mrLSGidg4pBWKb8MkpGYMIyGin6bAPV0pJyyFHRZ4lxLlrSmPIoAkgV8VfBI+gUfc6Rc0RkkZRS4gwp8f4wTdPgB6erS0OFFkQcBjd4t9+N42AJRYNLUpJIJhNynIVTNWeU/JR33ENO0QIaWcLVItcOuqo9hUyU8aW9jrt0tzGMp7dvf/Pbf/vX3/6bs846Z53X9WaRjCGyhNPgY1i++eN/Pf/wParfFpihaN3tlcsqVa3XXc2YsldhEFBvCqr1BEiogUKV72E7uNxmwaON6dT/3+FDD0IrFepQpoeb9YHcYpC0KSl9XBFVgwxBw+iRoHQypIJBhqxBY7CRXWFgEU00kzICWOcZMcfIiCXAlagOPUUXFZYUUwghLCEsIS7KfaI6vIz2cIMl6Y3mMx6c8956a501GstrqL6YFVg787fMSxZEBmAQlUAE2IBK7EIVNboWJAzAzCnFEMI8z+fz+XQ8HU/n83leljnGmHMSEUJBBCJQG8ayqN9NWLIIAEteu9pPls/mQQiG0FsavRm88dY4S0879zSZnUeFoZHyQBFBkBlTEkhJohhbMKj+1CjCgkGow6SIlPhz4EQgxhKIRXENg0pDQqW80LUeDVMjShwyh5znGE+XOaqeHWIIMcRxGodh8IBo24kgKxLlFHIKMSxhnufL+Xz69Rdfffn+y/dIZIxpLXoVgQGspbdPeyJy1jnn/DD88N3Xx2dcJEvSyBiFIQYgXf3jGkGgRHbT6sMvc5kBpPCF0m/XaYrry2gf2iG1JS7L2Tv3/ssv//W3v9vtDtYq/pR8lYhkCby3z88f3r599791BCWqSrquxgOb+tY+XI2yngshACAhVNtRVxeQIrEwQlF/ywIba17pDdytHKV1qfKWtm6v+gM7Q+wWgO5iE1R7oybmJrhqQk1GQgLSHlanVanJQQRAxb5kFmbQ+Y4pc06SmUzJI6O2Hue0LlwhGhSbQghhXoKu+zWHMC85Rs5JckZmjRo1hvzg/ehV9LHeOe+st8bVvPqw3uYW2BsEVyqEiIgKCMIiIICCwizEIixC62sWEGCQGMOyLJfL5Xw6H0+n4/F0PJ2WJSwhxpiYs/JlfVTKEa0RAMzMGQWYQZDvxuV+pPwsHkTe0ejNbrDjYEdvDqN7mux+oMnBYNhRsoAgDJwRkkjkvEhFn84WowLSgOWf1IekgVmcJGfhhMCEYC2BmNoLtKuA+idZjFQKSpQhCSBDZhTgzPMcNMRLHQtTylPmYXAOwAiUsHoAEAhwZuZlmV9fX0/H4/l8upwvX3z1xfsv303TQDehbABgDE6j/+KLN8ZaP0y6MvXzjzifnzkt6pQDAJQym6S4zADaoFh8IyhUG9JaWssoMlJRZ9YOjDWIqLEHFYMAsvB8OWVmEB4HX7N1U22eIoB+GHT98mL5SEntXCOUrg2m2qvXChQiJoXGKQxB1eQVdnVirL64OkjXNVFapdcuA+0xtL6F2KMQ9AB0BU03AHRloFUUKhN62ijWH4yANVGe6kKkK/bW+D0gBBQWkQSZpYToseZIlZyBKSulzBmUJVVdP87zcpmXeV7mZZnncJnDvKQQUgiSkjCDMFXoaeij0bdkTZXBqDEd7J9X9wBrk1gfqGo/WURQ0+MTiqAoDxIu8rO2RRbhMhZfLufz+XQ6vb4ez+dLTCkm9dqVKSOKrUho1W+PQoyYWdsF8W2P+Wj5LAzSO7cGvTXTYA6T3Y9uN9rd4PajKRhk2VO2KnpJBiYRw5yQqM1S0XAV0PVIoIlnOk5AbdUCwpJZOAsnkaTTURDBmOJbBBAWwwKZIbMQi7HokAQFIAswsFhjndGp+yZlvsyhLYGIiGJEwBhQRGB9+uosA8kpxmUOYYkx5Kc3u91uGkZnrdk8E8RxcNaS99ZaQkgAEFLODDmccppZYtlVNMURCBKKMHKnWJd2oKNzbT6dR6yFjmBhP2vr60Y8aLoRCjGez68pLiI8jV5WUrEqO8M4OWedH5zzKYSi1sA6cUOrC4CAakxhoTpNu5SibJQOjgUViYABsIQAImJlvA2NKpK2AOoGRERdT0IAgCa30gaAtgTnprV3ezXNqFV8PVk7X8MtMlafgaq7qnkQUsmpKCI5q2QCmTXfspJ3YRZhQeCcQCSrb6E1ZuZlvizz5XI8z+dTuFzisqQQcoicknXGjs7rKmYr+jhd47yhz6p+rTd8/akflOpGAiSuTQ0BSJiEWJiFsjAoaDDXpVx5WRSALqfz5Xy+XOZ5XpaGPplZdW7tTAZRDCGCIUm58ARhyZX23sWTq/JRDGpdoumhlnBwtPPmMNo3e/c0+dGb0dtpoNGhIzGYDYDObBchAWKgCj2oc+RK1Lhqc8X9g9IMkToLV6RI+WqiIjKiOhrVBEVrMTEZK4YNsRgRQQICsIJJTBbNim+tdU5XcCUATCmHkIjIbR+OIh3nmNPCOehMsrjMYQ7h11/ld8w8TdNo7DUjssYc9tPgvSE0xlhjv3H++cc/LWcQFuBY0HbNQwwkpNGTZTCR4qRQTnTNYbH3WLVzXPGm1XJT+ej48uF0fIkxeOe4CkfNlhSQUefaOTeO0zHE0h1l1Y27K7QeXHsuVFBSHFG+QIgAwhoTp4BKQIJ1XmfrQPWBt1M10WkztNcx6xqAOnCBze/OslIs2+BUwaDyfcMgPa8x1jqHZAQkcxbJqO2MSoSQxpqpu1NHK8UgYFas0cuJCOfMOa8NWFhyjiGcj8fL+bSczwpAupD8MDi7H1dH++idd6Y4/ssEihYK3r+S1gqu/sS1nfQ8qBiI2gJUuGEp7woERLJkzjnlmFKK8/lyPJ2Op/PpfGkTOpX66K0XvwqCrnhNCGKABQJmlNKwaX2baz0/Bkn3MQgLFdVXoJlrUbNwTIM9TO7tzr07eG/JWeOdGSxaApQaiSOgibGlmyOHuOJRqxQW02QN2gKV8BvTZ0ZWK6aM1kRgDBITGSAGNAp14JBQkAQN4yAwjsMwlPWjATGEAAjMEGNGjDmzY+usoCtjNZQAQ2HmFMN8OV2Or6fXl9Pxw+lXv/rq17/66tdfTjRoq71qAMbQ2zcHALJu0kUZPnz/9enl+xgY+sFJugdcQseAoTXisovUvGaFLGEJqRFSvFoZUD9jrdlniHB8eX7+4bvnl+fz5TJO06auVSTf1QyVp1eoy6VVY7evaIE17a7Sbr1xEA0XNrr6FjDWddxLOqNKkHDTGvsabbb31l+lH7jFk5XdYHkX679r3NliUIs1bEeN+6dx3BlrmUs8VwhzCLMCa5l5qy2Vpdj/WYALCaxWcn23LAxJm1BOKacYVf2Z5/l8Xi5nSQlBvEEzjNYaFXr8qNG53jhrrNUkpDWbNcLaerb05+bP672qE6BrLdUpJ3ofgsyMolVNIcYYwhIu58vr8Xw6a7nMS4gpdT6S0oQIEAm4UJ+ad3idbl29QJVVMMvHcsB+BIM05zyhoZKwzhraDXY32P1o96M9TO7N5JwlXSHDGbQkCFwUD0WiAjJqZ9BK+GH9oS1fJ1+zADQw0jnwUl4t6uTzouCoJo/GEDGQBWQ0RMYNo7GMJjNmgWny+904jX4YvHNmnpdlWUIMMaZ5iTZzypwsp6wLeFhDZIQQScowITnG8+l4en05HV9Px+PpeHz3/t3habfbT85bXB8xGMLdNOi6jJaAUDhzjEkEcp4lL21u/ZUuDfpkoPJA7bCwkhYs+mblOFATRrTBbX1nNb8iCAI8f/jh+fnD8fS6waD6dhFgt9sNwzAOY3kTK1loc9GkRkCuDKj+bBREQ4SdIQFmASV3JCKIBDomVBF4W906XtcMr/pzAzFrkiDYXLMDHAUxAGjHUZfPbD2sYlrJ32WsG/f7p7fvvviVc14XUE4pzfPp9fWDTndRXlzGBNCwGZV5YQWg/n6U+uRc/CDLEpYQlnk5XzhFYLbCGr1ojbHe+nF04+iGwXpvvSVrjbFoaPOcOt3s+nX3fRXgGnug72fXz11JXVZHKENMMS4hLMuyLCpaXeZlCTHpynXKG5okCQAgWLLj6YIUGjZOS8pDzHNMS8zGJCSq+Y6EmQGY85bP1/IxHoSW0FmyhjQEwFmaBrMb7X6wh8EeRvs0WUMlLMoQGlRpszgtO9tKmweXAbw07/LeytgvAJobGgCk/EGiscYABYBWaFUpFHTWpBGyAmCGaeeGybgBjUMy0+h2k58mt5v8NPrL5TJfFIiWEMJlDjGlOcSYs7PZO67LiQOhhq+mbGLOETCzxHm+nE7nl+fXN2/fPL3ZH54U3rzVyfoCSDiNfhz9ODgi0cb+/bd4Of0okoVzVd6lNoNKSYoQo8+h4lDfnDQKelWkZW1bNYBZCSWWFTuQEI4vHz788P3zy8tXX/26XGsdJ1EAdH6fH4ZmebW47fqG+lfVEZaOhADiuNs9vXlvDaawXE6nGINA0mGEuwliV4ZY21LZH2JjaM1iWo/tL9hTnXsMaJ2wUdOSVUmFkMhY4wY/Hg5v3v/mt//2u9/9u3cupzzP8/l0fHn9kQUACYRzDkuK0sI0BZDLK7pGH13rK8UUYywOryUsM4fAMWFKhkveCGvNMI1+mtw4unFww2C9M9Zhnz9k8+o3L+C2m95uql/cdPVuPoZiEGokD4tOyYxREwpyLKKPigMrBhUuUUdHxR2jAoS1RGaJaQ7JhmRDUYFTzjVuoZhvP4cHYZmHMThSN7y39DS5w1h50GifRledIVJHSFG80Lh9EuFiXBVCvYGh3kgFEZ3XK/pHzU4mGtVaZq6oTVYosD4dQyiIAta4cfc07Q+KRNb53eim0e0mN01+Gt18Oc+Xy6Jp0+b5+eV0PF0uc4gh6myMcfCi0YxECEgM6rZPaQ7hMi+n8/nl9eX7N+/ev3v/5bt376bdaJ313g2jH6fRea93M47+qy+/QDRknffuh2/t6/O3S45XiZ62A1vh+4pSdQDT0a8tXYHqsucmpxUdZIUkWTmLnF9fXj788Pzhx7XPNLtNBBB3u904Tn4YoBErLDyojaCNCHVyylZnQXrz5t1v/+0/vHevL8/ffvO/5XRkZiHS5WgQuRy1Nq5Sj9Xs0qZRO/satFN3w5vrImpUU1nLALvkZXClBGFJ4kNEiNa4Yff0/l9+++//9vv/+N3vfv+73/7b8fX1dDyGEI7HVzLELM65GObL+bjIWXNQrgOHrPRCKXvOKaWkyTViCHGe4zynZckhSkrCGZmttcNuN0yjn8ahoo/xruoSN1Ai0qNIRx46z+g9atQFXkr3s9+3BCmxIIggi7AkZs0RHdU4yJyL7oPGGO+dNa19KtljADDGaOi2c845Z625LMku0dhEFAEJAGOilHPEDCDMgpl/Bg8iRGNwcDR5Ow1mcGby9u3ePU3uMNidN6MzgyNNxSdVJK9BKgBSJk3hOsTpQA2N9fTWq+o9NVC3TuAFAEEqMwxBBIQhM8QMiSEDChCQRWsIyPhx3L99evt2f3iz2x+GaRoHMw12Gu1u8tNoL+fT5Xy+XM7zfJkvZwDKmZcl6JzkxYZlDoN3wzCMg3fOATAKIiJzWuZjCOeXl+8//HjYf//m6c27t+++eHr7bpr2wzA677x3fhymadzt98MwTOPw7u1BRECySE45hrBAXkQiQIKqgF3LQIAAjLION2vjKcaXjtHAzQ4EXCMY1yaHCLDM59eXDz/+8MP5cp7Gqevv5Uz73W6aJu8HImLhlsgCa3stA1eBNaiQVy+BxeZ58/bdb3//H7tp9+23X58u55RSzkmYmQQ1rUeBEqyWTPtY73tldbwRcbr/tzSnCrUVFOlmrxY0pn5YJCI00/7t+1/962//7b/9+3/+z199+dVht7OGpnG0xqacjTXMGQC986+vP8ZlRh0KZZtoUkB0PjyXOaYxhjgvYb7EeY7LnJeQY5CUvfd+t/PjWNBnGv00KuupKQ2gh5GmpUhp/1K33+LNfQaEmyPajKANCcLqqGQW0IhEhR51kRBa5wYRQnLWpGCjC5yTSLWpsqYMAuesLsXmvPc6z9NEIksUsCweAYZSSOVaRHxXEIRP8SBCb2nyZj+4/Wh3g32zc0+T2492GszoyFtE6OQ5JTOre6aaHEXRlGr7XxuuWMZbXUNejxaBdQEgfZrCwhlylpQgZmAymQjIEjlrrPO7aXc4PL17/8WXb96+2x8OzuHgaBzMOJhptJdpmnfn+XK+XM6X85iyhJjnkC5zPMc5LOGCs06bncZBAxq9s9ZYYAFiYUTgy/nDPL/+8P3Xw7R7enq72x2m3WGc9sO4c370gxvGcZrGaZqm3f7Xv/pCOKZwCct5vpyWi/pUMgBvLbIeibB4yrrh7OrF4ebpVpjXLSsdQhA5vT5///2fPnz4cfyXsTCC/sU7t5t23g/eD+ly2fIS6Cy7SrSq6dR1cyJr94enX/3qV+/evn16OgzD8Mc//Nf333z94YdvRS4lbw6WqKDuDnpxsrA5NUNpK/8UkaplLWtGVUEegpoQZhtKTWUzGZXMrZ/2b96/+/I3v/23//Zvv/v9r776aj9Ng3MA6JwjMoMK0suyLHNYLs5aXeJHmo5V3pWmGOPMKecUliXMl3C5KACleQZmY8wwjc57P47DOA67nZ8mY92qN3f0p8edkm2ot6Q2H+42h22pIxNUGFu/2I5CLUZLXQfGWO/BGqNTuZNz0YcUrP7jFLtUslmYEcB77+p/znljLFEgKpGcLW5DhHPGrcp1XT6mSaMl0qjo/WCeRntQQ2ywu8FMXm00AgDN1sQMytHK4gxqP3VPDHEDRmsbXNkiNg82FhhSulCaLDMklpgkJAhZwAgggjGGPODgp924O+wOh8PT09v37969feedGQbjHQ2eBkfOeWs9GUfGGmPPc7zM8XRZjqeZzJxZcsopxAtc5osP+2m/G3kcBu/JEAkiEUgWRGRCpPkUlssrAJKxu/1hmg7DqDbgaKwbx0GRCI0ZRv/+y68u8/lk4HLKvITqKJfaulYKg+W5rXonImw/SGMq0hpcfWirfASIgJfT8Yfvv/vxh+9//evf9BymtcTdbue89344X+bK89cJGtDSxTb7phhsKwQZaw+Hw69+/evf/OpXSwhPb95bO3KS0+kYQ+jMqPaycdMqtKVUd96KOu2olRGtU6Oqs7rnRWW/ilA1M6Cxxnrjx2n/9nf//j9+/+//+dt//d2X778YvIOqLupqK6qjGWO88ymGy+kYLmfsnqoACJT0qjEuYZ7DclnmS7jMcb5AiigyOmPt4Lz3w+CnyY+TGyfrB+scGYvGEK66zzr+1GG2SU0tlGLd+Spk456qUs/XKRyfOqDOmEc0BhDIGCvMACI5p+BisMma7Ex2hrPVpNZyhUEVgJzzaAwLxJQNYpVOuEon3Yh7r3yUBxlCZ2h0ZjfYp8m9mdw02P1gRmecIQ2UacYsErJaU40GQt+eayNcu0LJXwpduC8CEJQAvm7+VHWTsaQsMcuSYIlCHqwlQw6dN3Ycp/242+/3T4enpzdv3rx999YatJa8o8GbwRskg8YBGUDKgm442+Hs/GidN8bmmDjlnCInlhhJMnICzohi2ZAxRufp1j6JGYEMInHGl3B++fCtAI3jzo8750drvTEGyHjn0GBKiXOyzjs/xOVUZi+XltGaImL3lgTalvXlNW5Unxa3ltYDejvJcrmcXp6//+7bnHPTSsqOAoAw7XbOOue9glprlfVcWHSJdixWuali0DBM+/3h6enNbr8fpknALMsinADyN3/8w8uP319Or1hNiTptvt3celMrqMAKW9fS0wp+0JxcQF0UUQkn0TQWhowl4/Zvvvjy17/93b/9t9//+3/+5jf/OnhX00t2RURYTsfXb7/54zdf/9f3331zen3OccHafXRUTzGlGGJYwjKH+RLmSw4L5OQkk0VDZK0dpp3fHfy09+Nkh8E6b5wjY9uQul6zQU+lP1DN8/aK+ireVHm7sby72gKwbxSdUdbt3joigeaiU7UVJCdCMLpEtCNxJNkq9Ahr5DAjoHHW6PRPgBBjXoK6fGIMOUXOuUQgCa8i7kdKwSBcrX4AAGvIWfLOKgYdRvdm53SJntEab8hUelXHCqln2HaY1ZCtjbhtF6yDokogUgFIx90WI1RIUGbIWWKWmGRO4owQoDXW+sEM0243TdM4TsM0jfv97unpoEshGoPOorVkXLIuuSG5zC6zG3fWT9YPxnlj3TAIAUTmOS3zMmNOlKP+s94778Q7C7YkvlGJI+dqbJbQp8tpOZ+fAchY65zXRBmAqNqBcE5xFs7CWVNBV7V5Hbmgw23oCMl2+aDyTGtzk85SWoccBExxuZyO3337zbLMRDvc+KdQBMbd5AfvnMeKaxvfW00d3caa3kZCACKapt1uf5imnWbNeXqz/4//+P04umkaRSCFZT4fsaIpVh5XhcKWEVtA88w3BKq3U5lXlcurXUYlfLkxoRrOXOiPMdYP09P+zRe//s3vf/8f/+Nff/Ovb9+8GbwzNx6oZZ6fP3z4/rtvv/7jH7755g+vzz+E5cyp5CEDTSaeNafqEsIS50taLhxmiMHkRMJEOOx2fncY9gc/7f20c8Pk/FDjvju7uvyWlQFJG2ZkCytbie+6B18h2trDZTXkm/Aq3WMuv9d+qXKX6MJvhABikFCEQCxCIvAEOWVmKXmTuTwZJBZkkZRTyrzEfLkUp3NKkXMCzlBXFMcuqOKmymBLK2viHwBgSXTqnRm8Gwc/jX43em9xcKQO+zaFquTYA2hjZoex9TqrH6zD43W4RQBR9Y9K59I5b1Kjm4BZdJ2AzBAZYkYSArJknfeDn6bdfjftxlF1HG+drjFQc04ZS8YmYxPZRDaSHYwbnR8LD3LOEjpDbM3kbU4BRSClcDpjDG4c0zC4wWuqOs0YQOutYI0d1WdMAJgYY0AQKek+kQQ06VzKnERzqtVBT3Fo+2iqP0otsUI+YHV7VAlJG1w3dva/EQDny/nbr//47bd/+td//Z12v9UGRhyn3TiO1tlyNljFj3K9liR2HUNqXRGJzG5/2O8PwzgAIhFOZhjHwTlLSC8fnl8+fP/j998CpqtMJVgT0fajs6LJ2jWqz73SoSqDl19VhqbVVtNgNrLWWu/H/dsvfv273/+P3//7f/vNv/zm3du31pi+HzPnEMLlfP7www9/+uabb7753y8fvj+fnsNy4RRqknLOmdXpFec5zKc0X/Jy5rBIigZhGCe/2w37w7Df+92T3+2t8yVr5SbDf/9XeWcdwvRI1L//9eBb62yzU+Gs1Z5uJ6ytpb3U9hIaPmFtQFjn5gCTureLhzsDZLQa883VthJIzDFxjjmlNC/xvITzHOYQQ0wpZWHGMutOM4YVLY/aBGxcn4IFgDJ+NNJbeJDRXDnj4KbR76bBEjgD1pRY+MzFkuf1aWAdPLuW3DU+bWprN+h30FDg0iWhhDqKZJbMnBiyYGZMDPrPAgFZ45wfx2m3q9EuXlPCOtdSzYBoRJZIFhAgIEfWWzcUp/q0m3YLcQaOkKJEn8PCKUrOCEyQc5iZU87R5sE6pykJyVR5ot5aaSQCa28F5AycQ0NiAAHJylXarcvaBHskKhZolT7Ks2u+8qJBF9lYoKat7lo2AkAMy7ff/PFPX//v9+/ee+c65z8CoB8nP4zW2lq58qNej+ogtrKQajEhApChw5u3+/3e2yIC6AMYx/Grr7787//z//Xt13/44//9/0sxdeduVyoJNAqcduNfMdz0YVSTq4lDLQAR2lT3QoCKDGSte3r31a9/8++//4//+Z///X++e/vWW6fJW9rjZpYwz6fj8ds/ffP1H//r22+/fv7w/eX8yjkhaIMu+VXV5gqXc7ic83LJy4VQBu/90179oH5/GPYH4wdjh7qQb50LsjqL2yvGwu37dl9AplZQrv+4C0PSw422uNIKysiEcHVQi5OF1RQrA1sZ67DVEct8XUCDJGiorKQLggCcOaYU5rDEdJ7DZV7Ol3BZliXmEHVqK6sTB2uaIVrHC2wmkNTJiRY0JLrkd9BZ1rif/O6wm/bTdFClZdjtPEkyKAZFKSZX/7LUG6+o3mNO40C1t2LrK8102wA8AQBCyhrkxsySMqcMmQ0DChIQCgGQQ+OsHfwwTNNOo3418VjNGIdEJEICkhl0GYCQ0hLivMTEAmSsG4Zx2u2CQSHIJBlzzHFJy5zmS14uHGPWTJspxRCs927wbvDWWzJd/qj1hbfbbs+gCD36kEoqGagSwIYK1Ge4NkIRgZIa7w6NLXDP3TFVwih+jxzj+fj6x//6v3/969+o0VhHWyyNtswIry6wwnsAu4YKlSb3lAQQydinN293+701ps1KA4BhGN69fbv86293+10RGuojqaGUsNK4+qHTpNcMRA3mS7+oXGijFNXtZKwdxrfvf/3b3/+P//jP//Wvv/nN+7fvnLXYpY7VVnV8ef7TN19//cc/fPP1H374/k/zfGZOOh9IW10JdJ5nRZ80X1CSI5wOO+/dME7Dfud3B7/bGz8aNxjr0FgkaiNSHVZWpRlqN1lf4wpA22M+BUPbv6VLT16vjPXrsm09i1QG1KTG1qC0CcA66hAhGKyzE9rKrrqERAixos9ynpfzZbksoUxqVd+hxve0AGZDNks2RkqiHpFmp4koBpE1poQaWWut2e/G/dNh/3a/e7vbvdtNb3bjYZA4S144h8RJJHFiRYMG99J1ux7qGwxVq2JNUNNGx/JsK5DrrTNDjZ6CxMSAggaMIUvWj36Yhmmadvv9fq/reDQMyjlK7UwqFVzm+TJrzuk0L1GXzQAAY4zzzht0FnXGCaeYlksMc5rntFzm82W+LMsyp7MY76zaZZpSUxNK9XRoaw7Vlg/dWxdFomrurxi2nZnaNRzmjTHUTnpFrZuHXtu8CACy5BiW/+v/8/8e/PT09p2zTopgUMa8H777fpkXYwyn2E5WvAQ9ZmBlKLoPIqKxxr19+26apq57lLKE8MP33788PzMnUn8Fr/Zlv3+HQSsR6niRiPIKqnPHGvDolKUKigJC1o3T0xdf/eb3//6f/+0//tubw8EaktVPCzGG4+vxxx++/9PXX3/9xz98+PDd+fyaUhTJ+sg0xXgKIcxzmC9pniUuhqO1Ysh6Z4dxGg5vhv3B7fZu3NlhIOuRzNVNNYCo6NNAZh1guq9qB1rb/rYF3AOle7vVdlCv1SpV5kWv9rzUX40QiAAIS85JcoIcQQUdzii5rF6TUir59tMSgqLP5bKc52VeQgixtMq2bCQis1iWzMYxZCNZI5J07piIMKuRXjHIWT/4YVCvvzvsp/2bN7u3b3bvDrt3T9O7/fQ0hsspzae0XGK4cJwhB9KMagAE0pZ/N4V69X2l9jMEXAO+lAD2i2JtunAWSSwxQ0gQMmQ0jBaN03ls1hdLarfbHQ77w34/ToNzzhgCkJSS3mY75evxdDwdTzoZ73IJy5xSEhBjyFnrHHlLCkMInifPecc5puVyfj2649mdLvNliSnH+RJDMIt3o/feO01t1/Jsd+2ixDr1aAGN99UmWFoGQe+60IjoNTpHROqcq/p8uP8AdbjFriWWxGmYJX339R8BzH5/sNYrWIkACArIj99/N58v62HQVQKaxdcYSn2XAEhknHvz7t007WBbBOQyz9/+6Zvj6zMzkyHUKcdrSsg+bH/DJbGb9b5WpfPAQ90XOwBSgHfW7w9vv/jiV/tpF0M4no5vnp5AE64KL5fL8fj6/Xff/fEP//d3337z+vphns8phTI5kDlHnWyxpMs5LxdZZpMCSiYQP03D4Wk8vBn3T8Pu4Ka9HYaaAaJ/7d3v1vTqe96KzCs21Sjshiaybr6zvWNLd04L/efNXqUhNfoJOo0i56zLYynJ4ZSFE2g2NWEsK2JyimUuSooppRhD1JRmmRlECBR32jCprwTZgCt5lsQZk1kAUVgyaRJcAGDOYgF0iRs3anzdOI7jcDjsnt6+Obx52r15mt6+md69GZ52Yl8T+Sx2SRA5xyWSgAEgZF3dlgAMgS3Tx9AoHhEQQa0XYjEHOkSvg4FUL5iUdD6QGCLLkiVkZENAjszgYBBx0/4w7ffKgQ5Ph8Nh55zz3hkiEU45pZjUMtX4z+Px9Ho8vr4eX4+n0+kkOacUUgwg7Cw5a5wz3llvEVE425wtJ2utRTLG6oQYWOYQY06c0pxzitmnNA5+EOuKGduJ1bUNYQ9GaxNsAWmFIZXOvQKJPiyRtTFukGzTDleLuCYA6igV8+V8+u7rPzz7wRoHdfKvsLDIMs/LfCm1aDHMPaKsJFVFlaJdGmPHYXrz5t04jQD99SBnfn19+a//6/97Pr5WYEEizCxX3VTt5XIV7DLM49qzO2MLAYupvqpSBRuLiR/m0/d/+oO3Jizz05s3IECGrDU5xa//9x++/uMf/vTN//7xh+9eX55llRyZc8oxhvkcL+c0n3m5QAwE2Q/jsHsz7J+G/ZPSH+sGozYXdheG7p6uYWDDizseVFlSBzcds7mDTd3HrcnWoU+79hX6iACvWfRL4ZxSzpxSruijK3sIM4GUJG5qjxUMiimlHKPOTcmaJZJLrl6qQ0cdVPqALmNMto5dsmU6SMoxM2CSKFB4kCHn/DAMu93usN/v9runw/7N28Ph6bA7PI37Jz8d3LgLMWNMElI2cYFwyQtkJhESIWQDQsCO0Bp0hJbIEloSnfJKhgwAEZBA9+A2bFNBiKVI0SlLyhIThASBEckAObKjMyPhOO2fdvvD/nDYPx2envaHw55IVzqwiJhzXkJJUh9TSjkfX48Fg16Px9PJkBqmCSFbA9agtWgtWmcIgYmMMWwd5whEaCw5Z7y3p9N8upxPl2UOApR9jCHEYfCjd94574xZA3FWQi7SOlgfAAHVXIey9pwSDbn6vnZGkQ7IishUc2W19t2ZtWrtEgDklF6fPwAgITUrrDTKAtECUGnKOrL3KmapT6u9IePHaX84OD+UbVL2CCF8+OHHr//wX/P5XHUcnT5fTVApI3KjV9DNJFxtrZ7taGx0FY6aTo5Yo4KQclrOxw/WoEh+fv7+V7/+bUw55xiX+fXlw9d//MN3f/o6hIU51UVWJOcUNdjnckrzmeeLAfbW+DdPwzQN+8O4f/L7JztM1msosCGia3Wu/MQeauor61JH9S2/4kzlQz2naebYBoxkxZVmv20knXYGASlJyZh1qfoOelLOOae2jFnKqZAgzcTGnEHE1Lw9qiiDSIqxLnuWUlLlOWlMIxE6S+0tVA8BEVFMHBK7lEPimDjkHGIOKYeYMCYRUT2k2GLO2YJBT4enp6enp8Obp/3hzWH3dBj3B797stNoYsKQxEY2IdESYWEB4Ay6WidnTQHtDXqD3shgiQmtARawUGIPy7JAIpX/rOO4jswKQ5xXQ2xJEBidM8Z460b0e3C73f6g/w6Hw9PTYb/fV8xFYwwhiEiM8XKZ52Wel+X19fX19fj6+np8PR5Pp3Fw3lpr0BBaMhpwoGGNFQxUSXDOAxnjBu81SGE6+Wm4HM/zZYkpLae0zMHNfhgHP3o3OGttRSKsY5fm2BNsmph2+U3zkcpfqv0mHQqsLe6aL5XOikIsrbkTlgwTjXivyCbXsCJNnKow06xmaLDWsSEEZBEk45xHJADkhk8CBPD84flPX//x+cfvU4qVLNRcQrranogIr/Pam+RdrCwEhBYH1BJOtR0KRNVJV1QdYyKZkxxfflzCfDw+z/PlT99+HcOcUwrLfD4dwzIzl6lPOenCWXO8nON8hrhY4Gmwg3fDOA67gz+88fuDG2oQmbFqJW5Y4jrZvwcYrC27DjHr9y02vr7Va7OrQU/7q/9vy5lWpKq8towpugR6XtctyzmnlHQBswY9KeWcygyMzHWJagaArDkpahCEiJSwQ84IYg0aNJbAWcqZcuKUKWemriAZIopZfMo+c0gcUg6Jl5jMElGXYExlccSqB1k7DH4cx91u9/R0OBz2027yw+CcN9ajcWg82gHdCG7Jdkk0R3QJhDUbUhbJDAwGxRE40kWIxJv/h7U/65YlWdLDMBvcPSJy2NM5Nd75dje6ARCEgEWBhNaSHvQnJP1ULT1pSRQXIZDE3OhG4w59696qOvPeOzMjwgczPph7ROQ5dZuiFrOy8kTuHTszBvfPzT4z+wyDw8DYOQoeO0V0UCtdl4tnWw1+TOetFCgFcoHKBymQsiPvQu/7HffH/f6w2+13O/Md+77v61qKQISqCqI553meTYrpfD5fztYt9XR6fi5Dp0NPffDBeecNfUy8o42puvigMqkoi+u7npA9h953vb+cx+kyz3NOuaRpshZ2Xel852uI8VoLxixvrI19apAIN9Z5HVvY2KCFr10XS+OFqnkgjTiunSIaam1+Amr8d808lxWGdDPSoY305WBRF1Nkpc/N7kJcwkspxjfff9/33e3tTd8PPnhmBwBvX7/6/ttvx/NJRJqBtGDL1sOq5FCjdWDx2AEBcckXaWpeDYQMgJoRVMc81jiIlpLLeI5xnufJuaAiJacYo6rgRuowxTleTnk6a5pdyQ6kc64/HIab2+5wE/aHsNv5fmAX1iNYFwhY0gwazuBiCq1ryUf+1+p7Qc1PrXiyms0LBn3sg62/beOgeg6ySSQo0mpKK+4UY3BiXhyojeFTmu3TFv+qCYkITAxMyMbzIAIyOnBGuggqq7UmyqUUyrnkjKUIVT6CidlUPVLRmMucisuFU6GYoVXJci6LxPmKQSFUDDocDofDvutC8J6QRCSnNE0U55qBJEVEoQBloAKcRXOWnFAymCnEBB7BEXqGahY57Fx99YyMQAjcsphghaG1AK0UywbCrJSVBRnIswuhH7r9wTBoGHZD3xuV3kxUY0SWPm3z5TI+Pz+P4ziNY5znnFIpBZpAtUW6qLZJMmptpaVQlZQUmEAZAHydP0S1Wz2eLnIaSy5Jcs5xnufQha7vuj6E4JHavFsWOrTbjLiCyxrYagN4S3IubtmKjRtFNNUVOnDdebvYtmgoNFVZlRYfXS+7ccaL79fQaYGMxr5WoCDKKb1/++Zf/Y///etX33799Y+/+Prr+4cXu90OCf/w+29effeHkpNW4hFrqbaiNcJGWoF++eSlQyKAJbZVJ2v10RoHs3hpi1D5Br1AVbGIap7OJxVZWsUDWFOdyvuk8SzzBdIYmIb9YXe4GW5u++NNd7zp9kdbQmr6xab9czvABUmXW9Lge7Xt1zegm43lD1YkqkcNzRr+FHoWF2sljVb6opo8lj6SaruymFPVBKo+VCklZym5rJVfAhuPGMHk/IkILUuHibhmwgEqogoBkSqBgEq1pSoSiYiw9Z4lbhuuAlDKHDNRsjUgF/ZczAWxC+gQkZkXDNrvDYN2jtl7JiIVTSkh6DzPKUbjekWKqmDNLSIERwSZserxqkRRyIKihBoYgsPObCKHgdEReEImcGSqtLreHlVQyNmKMyALJoEMJMBIjpy10gk+BO+tZY3pKHGbUJXeKGLd6OM4jufLJc5TjDFVLi2DKhM6dqELfR/QSDiyZlCVLVwYKlJSIlQmpw6BmNl7e7rOhz5czuM0xWmO8TyS8/2u6/q+G0II3nlrA06LNdNAYuUUNvxBdZl07Ya+Brua1WSF9bRwMNJmc/3lFQwt47oBnSXAVt8XFgNUG52zdTXsG64AqFEyqhLn8dW3f4jj+OHtm2+//f2Ll5/d3Nw6H373m18/fnivKxty5fzR9Tcs8Iy66k6372qbze2sh1F9NUvA3VwkXXYCUAARgto9wfRT4jTF8ZzGcx5PnqD33N/e7g7H3c3d7vY+7Pamash+aYOEi5jjNQbBAkXt3LbGzLqhDZFWu6b+DhswNRO04dSWDaoAtLpkJiMr1n4hxZjSHGNMca7qRSma1ZNyDWBVhMhZam5avY+EQLwKTm42LDlx86wRLyEVAnsSqDChcySFjXlSACauLbCNOGM3p+Jj4jk1vXHNmR1ZL7f1WlYM8t6H0PV9Pwy7/WG/2+0Q1RYhEUkxSSlrK9Kca00aKBEiMBGykHNOq8q3tDEuZjQmkCI6J+WsjMKoHsEzegZHm9L+NoHGjFOCKGgZ0gKkyEBs8uOmrMHOEZECWFfsxq4qgNZWqynNcZ6m6XI+m9IUqDJTF2pj+1aM0plyPtQabsFNQNoadZPZQ0pADKrKzneKhC64fhe6Xbicp3CexinGWNI851zmOXXVnfXecxWYgMoVb0SacXFS0G7UspRig4TVFm+D3OR820VbhziqCi6I07AUAMzT1wZAaON5McSbe7G9DW0+bwBgwxKrSD4/P47n0+vvvw1df/fwcHN73/f7b377m/Pz04ajbcH0xR1ryGHSv3V2NX+sZSFuMGcNk60ZQgCoiteZ9vYtBk1kn2tMSJznabzE80nixJp7gqHvdof94e5uuLnr98ew2/suWAfBpQBtUwnSeLcFiT566IJFa9hquSvbGOYnbE6T0dBmfGrlS3G5e1aTFa1hXoObunGFPinFnEy/aeWQEJSo5VKtrD9cRwCuoMiKXhiboDeCmZzWqZ1BENARLZaYXRFmh7UyiQRQFFVzyWYKWJu/lLPF4ZZICCCiMwUG733oaunCfn/YDb1YdaWK5REAyDRO42gdqOd5jnGOqrqo3jnn0AMAtIVVYLNhev5FVUELCIEmVc7KRRktt8h8gXo+c8GpUCyQFUVRW8NdU28Lnn1lfgFUKuNWausR88LGy+VyOV/Ol8vlfL5cEFRFENQx4ZLtHJw1pDfrAFQBRIwt1bXxkHX0QFXDIyQmUABmVWgKV0RWlYaX8xxTySnmmFKMsQvdEPqhM7KJlokGlXaBKhTfZLnsl6rXKhcb5tNGVimKYnEui/4rVFUYrVJwdWFe7Zz6tOZLdY0QxapxvrFYmnuzCLHAAga4JB9URgJUoGSZdHr76vWHt++R8HJ6LiVXD88Y+AXjtEJns/ja0qMKorWTFzXfa7F6qCkCbQXMNvZIRTpoRBGsrXXm8TJdztPllC7nMk9OS9gPN3d3h/uH/d397uam2+1DP1TLh5C46snXV5u8LRi33okfxCBtlE+zK1f7sy0tzcjeulwVdCrCmkJaLpaSk1Pc9OqMMc7N/JkNiUwutTXmk4VegiWPoWG2DTK6gp4NJkHzeKHpNFWoryskIVWmpa5+urHdqolWChQx6ltTLtOcxjmNMU1z7SabrJSjFKmlZ4AAzjln2l1dCF3XDcOw3++GoU+x2nWlpjAlA6BxmqdxHsdpHGdQtXNaDNYrUK05A82YFLF2cFbOj+1JG894CQZlaxYtkIRKLdEga/vtHFuzDDLvsqSUZkTJJZdsbJvknC+1NcD5fL5czmfnanvuEBx47rtQ/bnAITgpRQRFTNFp4xNYVLlCEqISkvUOrfXZtmzVPR05zz64cYzjOV4u8zROs/fjGEIXusFSQOtxLKO53WjUpW4cFibX6K0GTgDQqj0/WX4Rqva/Xep1oDcAsFFS574IKNhbbP0OBFduBRrnqqstshmSC3mjqgBSRFAwp1SXnOUVUGuLJmnotsFSbXi7Ap99/2KD6AICuBSqbkgZg//lcmA11FCMh56meTxP59N0ftYcO+92x93h9vbm7uHw8GJ/e+tDsHQKZiRCYrSmzrA21eGGdptq2u05rHfhClmgccYI7XX5SeN52g7LfTHdZYMeo3Qa4sxxQZyUUsmWbZJbHyFpzkfl/OpVNJJtRW3YSL2tMYEFd1aD10oL4KqA1OZ4+4TKGarWVjR29iVLyiWlMqccY55jmmKaY55TjjnHVFI2TmotMDWod977YI+uPfq+6zqwy1KyqlocM85xnuNs3SLHeRpnaKEoe92sH2uCUjPitS5WRCKogKIoilIstKbLfQQzSJAEUIALkBIhe2RHlfohV3tPCmgpElNi1ZxSXPzgmNLz89Pl/Hy5nMfxMo5j33kK3rFVo5ApJXpfrZPFMhb51D7doipWQCJBNXxiUEAGB4BEznPX+26cu37uBjeNKaaS03yOcZ5DN/R934Xg2FdFvWUYL/f7agmy5jLWYHjBJlw7kq3L8mLDaLU6QCphuYGeavsY1Is2pDLIAFWtg0wBxbBGtSpqAALQlsOqjAaoQqkajuvcXKVnq7adLDVtuKxJFVJ1ubSm8GWXv2nDrC7Elh5qX6S6de1UtZQiJeYU4zROp+c8XqCknqHfHXbHm8P93e7mfjge+2Hng3feVfKUmIiRazdgQ6MV9BbOux1dBZENEG3Pp52eXVxpG3bdG99ZlwYBBamGfO1Nbk3o4zxN4zSNY5rN22rQky2a3myfxfKCplhrjXBouaZblbfV/AFcwedT064ak8uihItm0zLBLfujXQgFLVo0xZTHORk3Os0x5mLpiEZatwXbDLKlexhWSeoVhfqu77rQdSKllIQJFdTSKXJz48ykRkRpBncd/livBbaIKa1Cm+32ARKBKhGhCgmTDZ0qpi1SCpQitvYgESIrOed8o7uIGR0BWSshzVJSziSS5uogzimmOcbn5+fT+TReLtM0xnn2jBAcM3bBdV2odpB3zjERKpBdTtRlAW4O2freHDFFWABIkZq/ZqYuAAMErXgUQrycp3FMMeYSZSw5zTH0XdeHrg9cSUEwxIDqS+B22BDWeH6jk5briCsJ1IYQAgIBCQJaA81mVQIu0wDa9F1m8WJMLc4bAqgiYqPAaimJGT0AiCZXBwhgO9uN3XIiCmsNbEN3qBRXOwQrlEWEGodvEwhJgRRaqS4hklLjlK4Rqc4hVbCSjDjP83iZz6c0niVOlFM/9Mfbh8P9w+7ufjje9Pt96Mwvxhr7MSaV3ApA9DEAXRkKdq2uqf+1zBFtVhpLogq1TqWCDqrRdUtWloKqFik5zfM0jpfLZRqtRPFirU5zSiWnmkYo5sLIssjTIhlQL+GKOxubZQGe9epf0XxXj4ZQ6xWud6Gah0RWYC8gpdaRSU45pWK4Yz2NzPlaSljbgqcrtNWDBCJ0lmJjBlAIoQt1q+SUIiOiKpRSLKLUJPfBVmzAhcCpVx6bPoiI+QRKiCjtYoGluTe7g+udy8KYBYtALkqi1roQGdABMqKrUphk/W+J2VoJiUouhXICQJjn1s57nqdpulzO4+U8TZc4TTHOfecWQno3dCF404u2lAZVVcJGPFd7ByrKVEIaFZAULbmu/ar9UJHYODoCcDarGImtGngeL/M85zhN43m07IKa0+gdW0ehtpTiMs2WFQvUKFYisiSmhh/N5KkwRCvTCY0SWYd/LdJYAGg1PMEAo04Mmz1YJZwETb3BkFBJ68hXQSCbYrqMbFywTlcKpHpcZgmbXdZMAYU1eWGZGNTvbo63L27vHx4/vJ3OjyVdFh+x9llrMA2AoFqylBJzjPM4TpfzfDpBScHxYb/f3xwPdw/7+4fd7b0PwXnm2q4KmJEcMTskR+RMdgPR7plVKRBcT9yPpmtFHwOSCtvVy4LlFRSs8A+rf4r2VsWcX0PnOM/W2v356Wm8nMfz5XI+X86n8XxGXDGFiLwjRCb6GIM2P1n8+YVVu7J3Guosv1jfbnzNNvRsWCAKoNm0RQoBqmjOOcdc049idRnnmMwLiynFmJvTtawe9cbVVJhF4Wu/3+9MhdCiTd4yfWvrMLM7F0XrptcFxt5Ve6zdhOU21YEtgGYzAIgokm5W+OapIhKTIwJUIFFizUXQ0jqcIREit+aTdlEsGFhEUQRKhkyiqvM8TdWAncZpulzGeZ5TSkUKNNB0TCG4oQ/eO29xc26zauF9zOFaoccMSDWTBJHtbTOSFLXNSiICi5rZ4PNIxN75zndDN45xGuM0pZzLNE4pZj+nrg+h8867lgcDm6FAiLjMVylFS7EOecy1rxMzI1G1PyrXWymhbR1GTbzSqkIljS0VWVwHRVBEu7mCTcBVlYgUlRsnX3s3W8sRW3DQlpTmqZggn1RVKVFdnX+Ahj2VtEKsoZXae46d3x8fvv7Jz3/ys19+/eOf/P733/ztr//Tu1e/y3lsZrwsAG0faHl40+UyX855HDXHQDjs9vubw/H+YX9zH/b70A8ueOeqBW0AxE3ajtghGRJVqQkblXWlvpqoG+xpk9nMHoOZZgli4xQaB9TEchowCSyms8nxzHE8Xy6n0+V8jtOYYlQphOAdL+izOhar7bO4R1d2y7oiLHDzEePT/m041Ebe8rrmfwMoFiOtwJYiQFApEmMyXcl5jnGa5zlu0oUsJiHNdl9ZKCjqmDxTNmJXtIi6/W6/2w1DlSDszEtmCw9UoG9pUJX6qmt1JVYXi7IpEkElAhoRao4GAkoD2OWaGBghihWIib2CKLRACCM5IGbHyARVzkTVnLYCTQ9HRWWe52maxnG8XMZLDd7NKSUpdf1hJt86gjmufb3p2l41ssc8M6UlJL+8RajeF2tDIrOikJrbRERaa3iVlFnVs3nAhh3jGGNM85znOM9zCF0IfQi+ItHGkql5vVXHNyfNabumIaH5EjYk2S09gmuvAXPC7Saoas5Fqyy73SML4XOLUAiRLXjWg6V660JABISiVEnJSlASIaDlmMhmxVVEUJSKfNIo6dLAqCYISEtqEhE7GaYwHG7+9C/+0T/+J//0L/7i7724P755/+f/+l9/9m/+1f/3+9//CmSuCg2IgGCdLVKM8+Uyns/pfM7j6Aj2x/3h7u5wf7+/uRsOh67fuxBa0hw6RnPBHBMxITsynrECUFt36xillSVf5+c1D1TX3Y9et/7x5oFkKZqgCGL8GljnKmTy3vsudLFrAsxZi9PCNm4r6KzMxobk+YTl2eLJxrFaDCRcmMUr3LmCWWzOOiho7UduJVStw0+KKceUU5KSCaVzWEPXCJnq6Gm00+JIA4C6hj5Z1DE5Ztd1oTPBDu+d45pRt5DKix0ksnBgjav5CINWak6bi1Bf2/on6w8qP2nXQwGKaFHTbNUiyk7BQauAY8fOsas8LoJRVKU0alVFpMzNEBrHaRzHGFPJWVUQoZp8jp3jEFzXhRpkM/IRdTEVYWF/EKmBEZCqoioB2TpWw/Qm4q+oRBUbG1dNqIRKxKqgDKAAHhCsdtfROJHZRDGm83n0wfdDzfauBmi9ekigqAKqjlEFS4xahIist1+23p8FipmGm8EoFqkXA2iQYqRbqY4SKgDZbayUEAIb+Ua1VAupKQa0rOFGlOCyKjdHrM1XwyDApV32ikQg1aAzhas6B2xBAUK9f/HZP/ln/8d/9l//1z/6+quhD4h4cxx+8qOv37352Ye338dJABJaoznRnGIcL/PlnMaxpNh5vj2+3N8eD3d3+9u7MOx8CBa9YKObCWwVs2WAnEP2xB45LLl4jSG9ms1tvm6m6CpM20DH7EM1WeatP7D+jS3ExqQYc4goqoQK9SoHH0JX+gRaQIqK5TTHTSHo1ur56HFN2C+DGQA3sfgl39uOb1ubYyfTEq6XOoxa4y4iYIxyVfNQVJWSoRRQQRFHir4CECMwASMxyZLf2i4hItYJXpizq9vOdMtq7gpWSYlNTfXVUrZYm1d2UKPiGvboQtmtJMfCWGqLZ1R0UzAAMsVo27DbSsCKiOR4UXlkWrGx/jliyVlLKTHGGOM0zdM4TeNkJXqqQgSWkWgCHd67JWOQ6nK+4TubMdRKvSsmGWcEYLfA1BkVavKiKiqiWDJjDeZXGLLOsUqEzAjASESOXXAh+BDiPKcYS47xUiTFFLqu67xzjrieIBM4BIe6PwxDf4cIIlJSTjHllhAroI5AgUSaw6U2bAyDzAoFJmR2C0BApYRUBQVUJMc5N1/b9mfnHDnXrhWz82Sn4Zz5gnViEFrhdK2dgArJoGbgmk8oLRlykS1Z+1erwnh6fvv6+1ffv7q9uQs+nE+Xd+/evXv1Kp7PNvRFRCTlOKd5TOO5jBeJMxMOQ3+4f9jf3g/HY7fb+dCx8zV9nizeBdwsoAZAoVpA7AhtUFV13mXpxh8wKqDxb4utWn+KLWdCG13d4kXNetruvnjbAIDUEm991wUpPahVYOZSUikOryLiuBmiuDGGFnCizbFXTALExYAFQLsftVVYjQKJSXfkLDmXlEuub2s1hogQKCMw1gFZt1EdgiNw3BR7iDijI8ykudAy9uyrFZBrQSgUhSxaWDOr80uAmqiuMyoquGg7LUvZwmHiUtRP1IIgqzEK25UAP75jtlpUHqkh0YJOFswUacFeJKM+ajP4RaWnAbqqiKCqmrxbtNTRGOc5mvmGAEwEnr1n59cSi7q6Lbx/1Y5YGqSv91PNIEJVkyVt9WRtBi8w1MJoKGtk1zhsRWarkEBSICZ2HDrzaQggTuOc5jnOcbxMXd+FZhBRdRcoOO6H4XBzdLUGzVLDS44pGx6lbEK+JUspRYtVBFVNkBYRNdBlYjaWyMhoFSiSp3E8fXiW3EQfVAoWkOJQAQkKFE3QSUmY5khkpUTVPDIbs/K7zK3mvQ4haehjfDQ0yxfMTQcCRBW9nD785j/9uzSdz6enX/ziT6bL/Kv/9De//dV/evPqm6wX1ZzTnKZLni+aJtbSDyE83A2H43Bz0x+Ood87k5BYpSOs9Kk+LT5A7KlaQJ7I1ZQzqtVpcDVv8dpq0C2ftRnQFUONDbMhvnUBPgYraCZI07QlIuec90G6bCXgpl+ek8uJbbo14wwa0CwxRIsdN4K6WvXQ6D9YFAsbG6gfo0+RlErOJaaSUkmpmCh9srB6zjkXAGhVVhQcBkdWcYUO0aFjDJ4CYy5YimTWUjBnzUUArvN8Abn15jBhsyzqRN3+cOiHoQudc5UdWZ2vj2FodaDMR7J+sbj4jVuwgWWpvQKiarxe/ai6VwtzUW+yEUJMlkZp5loTvbaZaCCkCFBzu1Z+PrbbZqsfBe+CY+O6HLcqh9Uard7zlT1kkXH7mhZcggodbCdMKkasUHPQ1PIYtdrbzYQSYlIFUmFGVW4GuVkbfo5xnlNKqZQyzzGEEDqr8Qje+d3gh2E37I8uhEbQFVdEe8v6lNXSKFKylFylY0w2zKARkImoQS+sd0whpTlN+cXDF8PuEOd5Op/Fgu6kQoUdIoDOmRxPl/lxHHOcxSwaRLRJ7pw1jyRTfWO2zINGO+hiACweeaWlVOwC5TS/ffXN+fTh9evvv/7xL9KYL08f5vEp50vKY5xOaTrrfAkMu84dbm6G27uwvwu7nQs9O0eboDThFfpYCQEzk/PIHtkjeeOAtgCE1+zpht2t1yrNlp08iwgROedD6Kx902rcXE2B7WP99Sb0gAhqyQHOOfFBi+ScnJsrg0WMdTWGtmpWZsjSVgw3BVAEtYCoSJUr2zx1WVfUdD2qskf1uSTnYjCUUomp1IKDIlmqO4YAxaF4EkeipCZWiOiVECsG9Y5KkVywFM1FCmvJFYAUUIEASRFzAcBNHhYzMbvbm9vD4TgMO4MhRBQx33DhgFqXslZRRRZEISIAS4Kr4N7yIrCZ2rpZJupcbt5bK5hsmK1LkccCQYZB7Jw5jBWAXBs4Lb4CqlWQoeqkpJRS4qafYTG1ppZtxBJV/mo9qE1M4MroRajEiolzLBp0osYvGjtGsBZyGDdNRCpQI2s2zUSZzIcjEiUkRucJwBFZdJinOaaYY4wxpRB9F3xw5I673W437Pb9sGfnaxpyK31eZBtW1s63MWdvQVtNtuEBAUCDcAAAFUHVH/3iFz/95Z//6T/8J99987fvXr9yPrDjKZ7fPH6jPJMml3Se8/tXj6+619P5knM2+dOcc0lljkka00HM5j+z49oKqZmvm7RMXP41TqiAAHKK4/OH777JE2Sap0ueR82TlpGx7ALtbl/sb272t7fd7uC7HftAxG1h0MWLIlJrLee4xeAtAGFGEPlaVLkA0JUdVCXKimUrxylO4zxd5nlMsYpgMLt+2O8PRzreOY+4KS6BDWoBwtXKe7VRAWgd585xcS54l7w1hmZ2RATNUm/0jgEQm2qAAJq1WwqYZWELT5WXz1rFgYqIlqbzUayQU1WsgKmkUswCqtaQcVGWt62GQeJJhERYlS0LwTMr2HWm4KgLXAp50VLA6CNRACA1D71tZNFuzmNMYc4+Zj8nH5O7ub1d1IKccxYvXRZXWR0xK4XUZtpX1qQGIQWUNnVKNrAqD1Q5gIYtulgVBj31dUk312ooUY2hkndkoXRnyR1MVikmbSGtNSBVi7LKbmMNKKDhjkmWNa/Tmie1EvvtWtVuOC7OJqgsaazGvxQppaiqNdc26xfrYDLMIjIjqQodUCVfpflzhMpIVvHFSIrOcb1QiBpTjGnKeR6nzjt9uOmrPklHzq2OjSx+vQgvoCNNi2oxZdvqWwNhNRKw2LOSM5T805//8h/+43/6F//4n/Z9+NFPfvLisy9u7u4v0+Wb73777/76v43x+XZ/dNz/dMJ0znmaERFUH9++fnz37vH9+8f3H56fni/n8zjNcZ5U1WxOIBLVhXatd3GR3272g2pbzUoez6f5ckHFkrKkGXPsnR7uD3cv7m5fvuxvbrvdvnZtM49SLFJLNq2NmCJTYuFtGN63KNiKiESEaAYblMUyyCnGOc3jPI3TeBovp/HynObZPEkA7Ie9gvoQUsnITFVxnxbC6ONHc802ULRuNqeC2TkVWYcpM3P1xVaSmQiq+cOApAJZJcUyxzzPeY4ppZIWZyply04Gy70ARVSTiCZUE88BUEklp5JiiVZmkSQXMUepiJZiIXYmBEvqKYSl2MSh6mZwrfZV8+6X0k8084eggianoiGmMCdvz5D8nJz3lijjHVuE0uyLZgRVO8iIGt3M0JoTowQqJCQgALR0zsSVoF7+qSBUfbum11EnZ/PCVnrIvsIxGYnTbk0LHCFg46wWpdwqW5lLKcUJg1peH9e0J18zrbGW/DYnUVf0QRMZQ6sQQjPpSi5xttLr8XIZx3Gep1lVQ+drF4DOOV8DR1AzMBW0JhOqjQCxKD6KABHYK5kUAgExsrIHICb2HIIHFRDtOheC74chdJ0PjtiZ5KYq1uwLUWpWEYqgiIqiKMlCAizzgho1CABKLf+hKNzevXj5xdd3Dy9TTCWVrt/1u533fs+Hz198cX/75YdHjBlevvgqXtIFL1/99EeH47HfHwd/kKxxnsfz+cPbN6/+8Iff/+ZvxnGcp+n1978rIOgopWTKUynN4zSJKBDXmhnvnPPWm0RVJeWSCwJplmkcqeTDEF483H7+9Ze3n788PNwN+x07Z/AnKiXnkiSlaDONnfNd55iQkWoIjIg9mgtGoXJAzAsCqoKISS6UknNMMcV5Gk/j+XkaT9N4msdLnMc0z6VkqBmMTkR81w27Qym5iLPopWWPVSO/pT98/Grocw1VaP39mFhYnat5Xy0LrMFU/ceKnIqApV7EWMYpjmO8jPP5Ml3GedEpM1IZVKkGqoDJQAS8I8/oLSeh5nsJoRAqIzhe/WdVEFq6xaKasJf1cN7acOy8c/a2ccVIrcxerSMrYFGkXLJozGXhUlTEYbPzKutWqdyFABJs8XgDN1r+UTJEEQKqYIRbldFlFVjQRys7IVYdXOPGeGWi2rJtC1rFWOese4VjdtWCbrxmzRxoBfNWsSpiOUGWUuoc18zLmnpJSFV/VKHGM2A9CrR0b4vuGUlvpSpxni/ny4cPTx/ePz09PqeUQxe6zttr14fQOd/Zt7Q0XmPVLZ5vjJGl/akQodT0cRCqelxmoNXsbQAC3Q39MAyGQew8MgMKCqoiCpKgkIogoqIQipDVj1o+IZEKtOZLBqmkACaGgESmUwGl3D28fPHZF8NuN17O0zjt9gdijjnmUnIpQ3dMfVQVIp9kiprOKe69P9zcDv0xuJ7Jg8Ll+en9T1998eWXxI6Y/uX/6//+5u33qUZ8TWHLSqJSjCnnnNM0TaAArutCCORIS4lzlJw98eGwv79/+fBwf//i/vbF3e6463e9D53ljZhtCpa5X1QVVQtxXeqb7cNETByIA1cvzLXsdYsvg5Q8TedxPJu9M9lzPMfpEq2/U4olZ0up8GFwvgc2754VwIxvW5U3jb62xo+RRZYeXWeD/XQJ8Df/kZhZVXlNQmVqTXKWDAERKKI5S8qasl6meDqNz6fx6fny9HQ+ncdSmlqFqKpa03bvrG3MmgBlYoeWMi5QiZFmAQjWUWPFhvU8GtW98E3NtUdGZCCPLfhlulFFoRQtmkQgCxTVIjqncpniOKf6OqdxTq4Fphe0qOSMHZdZyVjzaGtp92ILCZECoIoCiYq2zpzX1BxuYMgwCBVFANAyqRvZ2wiaCgVGv1UGx29MVCKbxlB5pQZCraDGXuyLmEx7sUX/uFUhYYta4CcHi4hL+MayfqXK0LSS2BxjuozTOM2WSeUch9q3NXRDCMH54LxjYvM2LA0PiEhFdTWCVMSiOGpJ4FKVpRAYPJMn2u+GYTd0w8401WHNGwUkEJNpQWzxOINvRFQhtbKuStMYriuKyunxPF7G24f70HtAUJfv7l/sj7fk/PPT4ziegF5M8zif0zSPz+cnRQKEUuLT89vH0+M0z/G5hF3f913K5fbwMOz3xDXBMs3TsNvf3j98+9v/OMfxnDISGVFYtY2zFYTP8zTHKT4/ny/Pz8/GzyOE4Ia+2x9293cPLz/77P7li5v722Hfhc774J0PZmqqFlFUAtrt+mNX5jGePgBIbUfgnFFFxI7IVwwiJ6rRJL7iPE/jNJ6ny3nxtqbxnObR8Kx5SOjDEHquOaCuY+eJg/OeiAGg1mJKy1OpZs3ib5lbsOGEqldRoCUnVLO0BmLJAo1LGgozNTK/ruhFVYrkoilLSppiibGYwkXOpRRduzEb3dFKG1b6cymVYHQOmbABUM2oQMBqJYBFEAS09mu2vAMBKIoFTOCUsnJSl9S1EAc22NAskq3BebFQn1hR6xjzNKcp5jGmac5utxu6zvr1VcfYSOhKA+nqTJIhJDYrpZa9kCgoCKr5Y+sF39hBZj43uwOqcg7gAldY8QCrZYJg1Bs3Tnr1kc2LF6m225I0XZasB6Niocb2nWOrkV7yi6B6WctRtvJK3CSgGTZiTVUopem5VHHwknNRzW2ZQr4QO3LeheBC57ve933oetcFDoGZWwYfWTkmiNYawApDqNT05YQQATxTxxRYnEYqExYCzYjBBitI491QoKYQgK0KluiMglV/AFa4VUDNkubZ4uvMTlGAUFXOz0/k+fV3vz+f3n542r17fvt8OT+dnnKZFcvj83cpjefL8Hh5zAIHwOfL03DqgcJB6jKk1RYnEO363X5/04U+QjSPb/XGVXZa9Yin8Tx8ePrw/vn5NKqq9243dDd3Nw8vX9w/vDze3u0OhxBCcM774EzanAgAtGTTBSX2NOxUEqJYcikRMwd2neUrFYFc5lLGnFOKcZ7GaRzHy3keT3G6pDiVKq0pAOpcMM+oeUds8h2W1rDWtSKpNRCXUqSQVOGm5mctE7GoFJCiJasWqLxqacaPmb5sRjgSkjIwLDKyZsat4Zw2UYwZIiJiXXKaFKqFsjWCVLVqt1e/CRAtiwcdo5lIrk0HQrRU8pQlZuQshAAoCigKXAulAMwZBCyKRSkrLxhk7NxCrYioRffb07bLHPPcUgGkCIK6m5tjCCEE33XBMiyMLG/P9VHpoTZDAU1pBwhQiUTMc/zIrjAQauhTb5RpQ2ySuBcAWtzmGpevvpgxB9aywlBIW56jiIosVFAFIKlTAhZvtdHR1a61Q9LV9lroIXPGNgYcNKIy5VKKqhCDcxRCXTdEtZQyzSmfS86CCN47H9gH1/W+63zXua5zfedCcM5VU7LmQa4wZMO+nhYBeMbgwMus57fpzZRdiK5DF5A9sAf2SK4tT9WHpNYzyyIEm3MDWIhzUNRCUFBimVQl53l8881v8njJOZ6f3yvr6w+/vuT4dBmfns4++H7np/iu6/rbmy+fTo+5ZEbH9OObwxdfvfyJc35K0bFXBSIHiLkUReqGnfPsla22cBkJtnCISM7JYNGqp3POx8Pu5u54OByH3cF5z8zs0AVstFEg5wnZ5nBOWSUrsZaoadKSrDGD63fkAiCLghTJOU3jxUyeeTzP05jmKcWYc5ScRYpW29GqXZqrv0kKX2P2FYOoJVSbTWAmQ7UBVAXN0pGiJUFOkpPmaBZMdXoQgR2QB/aAtAGXGkRC+/6KQZXGgBpthnbBkAnW1IIlMFdXxe08quBS/TJG78gzeUfBYLbmr6Ir6B2mTJxMcL6yM0XU5EyaqAcogAAWhSKaisRcOKY696qyvki1fYp59BZSTtkIb1FRBGACcORubm6cI5vgVciipUm3yEsTDWga/o11a6nnUGscAaiBTHNy6mS2xOeNS7CkYCx208YCMSuoBgsqBrmWK10j7k3ZBmqYURoDu3iTjTFzjhdPzMqKP0oxa3O1JmI0GKq/0VofY/laWVWJwAcCcOxqVFdBUyxNQVyKaCk5nfPlMhmr5T3v993h0O/3XQhNrZjWp9qyZvYwAAN4hkDgNfGc9ekixIAM5IEcsAcOQB7ZAbGtVPZqSWhFyhoEUBWpHQ1BVEp283xgLI/fz+yIqOT8NF2eX/8hTufx9IiB3O9C6mACOZ0jIpNj7sT14e27b+M8o1J+fHJP0/k3v/vVfp8hR0hhvzvs7+52D5f3j8ENbjfkkgmUmRSQa1q5gREqgIhQnd3ADn3fzXP0jkMXajGxwY5n55m9Z1f9qTr7CSWU4Hmans+Pr8s8akm0u3fdDtBVyYTxPJ5P03SZjdyZpxxnKaUlCzhkZudxIYnW1JsaoW+RM0t2pCrV7jh0wzD4ENgxMFXBzYpD1fbJWpKkuUR7TgujAQhAjE7JVV2+1RKn+sv2JGJuJk3VpbNQrXXrUwCL4C+zgpgW2UwLiNWqSyJmdA69Q2f9hB3ZdlsTxWLAhGA3R0RFySyOorrIS9eUPUYCUS25pJh0wqKFF0PAEq9Nkq1s8yGr7lGltBHMvCK3hKtxzforC3K1AhKRUmCNXcnWZTHcaABk8LRBp6XKz0yNjXG5RYFFamZJX9SWY6uyUQBsBB6p4ELeb1Kx2roBdcfKarebhLSuER8xV835WtKampkjLXE151JUBS12ENh7cp6cJyLMqYrIWYwzJUnJ/HNNRVIUJnacHBMCeA/kiahK7xKBFeoz20EoATiCQOBAKBeYSj0mJCAPHOqTvJKrpBig1ala6nSpMlelltlUTLaIJiN7kYzOKbKIzPNURACplCyXFOMJb7w4BS4xgRRwwCwzxhEEMUGMJ3x/fg6/cT2LE8ECmTveHfpbTep9/4ff/NXzu+/mp7c2rR2BliLI0u3RtfQctDpKu8Z2VamGHthZUpj35od7cp5N+Q0JERTYq+huDwgAQrvO+eD7wYX+fHp+/f3vP7z9brqc5ulsQQ8D/NDt1uqwa6mgmnFcs//qg6055/LGBPSYfOi7rgudd772nUCEKkspWUvSkiTHPE15HvM0pmmCGg8nIER2XICEHDg26fIam13qUdt3EgmA1LJetWwXO1wGVEJObJmhC0KYNi9oLRTyjjpHnafec++5D9w5CkyOqpO+JP/pUqagWIM5lgmEaLwBL04qoiOLSueSNWIhTSVjbduRa5VZyRvVhpYyry38U4tNABXQFSkAokCqokoihZlKTq1PSPXmTFBxYakbHjULdLV8LA6wGhrbQGSr2Ps0erCwQnVHVTVJ1hjTNE3MtHQvifM8T6OoWPPr2sokprSKrC1EHi7e3JpevXxJRTxtUAawMcbaObbAW5HaOtpMaSZE671Kxn4WLz5Tzpw7yVlylpRKjCXOJcaSklgKRorFV8GgZgQxkqAy8JIpA8AIjtQTeARe6kkq/5Uhq5YCmJScmtNRlz7JUruuiJpfWpZKCWw0BJEjF0QECrMLgDTcvtjdf4mO333zN/P5PfrC3qsrrBJBRCGDlKwkwhgCdX3fBSJ0JWNCVFS5vD0/n9KrDAToiHzoGIE0e+8dk0cVkaSQ44V3N353g+yFatPkZYgSkXOOnffeWd8Ub16Y75i9AZMx3DlnBXb9vmd2IZBj5wIgq0nbxBinKce5xEjOORecD0yO2LcamtXeaTmKi/O/TR2qo4ct5ZIdVXz0znfswpqRYZFrKUbCSkmSYkpzmqc0TWm8iOm8AAkisncd+Y6DFo+ApGseIi6GVwUhm1hWDIANpwgQkEDIVQiykDF7Zmj8s1WtBYdDx31Yn57J+uoQIAgIalNX1hUhLJgDgKBMKCYm3arTLFGISQmrAkBjtARM+B7RIQtRy5ypr6UixpI8DaZu7qQUq4NVIiMmSqGSTQY/przCEDRn3syl5ty3Cbtu1B2wwdMCW9omdrN/VqzChhoGYypQiqSU4xxH5xAxp5RinGc3zb4bvarmxWDLZa5iu7nkddFvi9vi5ePKhP+AKVSJ6XYiLdOjJiK3nnC1mwggVvHPmlVPxEzerznypeg852kq85TnuQw73/feSmK41nBbghUqb+BaURUItHZns/IcX0t5pTJZoppN70SBTO3EsJQUkIA9q1KhUgpKyQVqTT1VrRNVKZqTaHGEHIaXX/3kF//lP+uG4T/8v+Pb71KhiQZ/vpznOUUCJWKAPM8kuO+7L378i1/+8h+9uLvxDs+nU0zjNJ1e998+v34/ny7l/KHMz5xOjskH5ohIZMseqc7nZ31+F/oddXsMB3AdIjp24jspERHN/GmSniF0wbq5ppwvY5zmeHo+PT09nc/nvu9/+tMfHY673e2BLJVURETM8XA+iGQRcT640DkXCJtIKyEgbkJPtEigVMoXEMggA12ouRchBHZmb9TmWYiLGWWrq7ZBowtLmHNOKc5xEqUiWJSKILKEHffikYScMBNUXtQ4X4O/6oxpi3W3OVUZcgACoiqAtEEiBG0iJ4qgnaeGPtQH7qoug9k1YDEQrrXnNf6PVWlLi5CoFlniaauJwISe0TN6Z6/gGRlJSYVQRZXbLBAj7+vTSANDIoG67UopqiiKJLLQEzmlnBr4tOawsCzTjYFrM6cS5m1i6wpGzUxqvk11cGDpdgQ1PmWhzHamWERiSjiiiKacp3FyjrwzcscFb6mi9RwENOcyWZZoKQtvbgtelV+sKxtujlEXwNlC0bJdvU5twf+8SNlWhQ+bXMsftPFXP4IInTNUYlEIoTYNdp6cQ+OmiIGkjikbfyYzxoCO1TN4h95h8Jwt90fbdyFK7aACoFpU6xSqB0cAlZEtGRKKKNiEE5Gilk0MiupQuqH/yS//9L/4r/4bUP3tv/3vzqeBhqH7/HM9nfD8fC7TnKaURgJ0SE7zvoPbA90chuDDEFwpx5xfHPuXpy9P49Pz87e/Pr/+hq2lN1HMRUpxiI7Uo0KOeRrl8ozOgz9Qd4AwgPOgwsRIhkGevVOAaY5Z5Pk8F6FxSufL9Px8en4+nc/nGOPLly8eXjz0u743Sk4VCZnYd/3ucJPTPI4OiZ333nfOeZunljfEzNhIOQv2W6hrnvM8pxhjaprxu/1wPO5vbrzzjtQjWp63q1QWtmTZGhpcqo0qGi0FW6oAQIjCRMTiWZmFUHANJdfZYH10sDmA9nEosgxdywdHJFRmLgtTU8M1qGRpNICE6iobvTyBjYVCZGpdkAqQIAkUgiJUWmm7Lq8AK8SCigJh/UBrz2UwVOl4wsbRgjRRsCKai0koWDpSC+QpCKIrpZg8aVXKU1TFqlhW9TqkSnNv7J5G425oe1jdl4Y0hq7LVN/M0xaXglWMGZYSAgAsqjFmEUkpT9PknAE8VIearATRtTw0VoUaCEzZkvbMyG7pFhtXDDfH/zH6rJ5hHUi6qYgweRE1XrDGa9fIXnM6PwK2OriwKk+6GqSriXLmi7HWsdwkCpVBHYE1y3aMROoAQNcgiqUDWV8AUCUAE0MCgNoMCqCQMGpGICxSdWrYyD1TVFAER+AtZc8q4rTwEIYXx+OXP5vefj861QT5aZqnQuQYCKgwZ6aZMDl2GFiKKwhlhyDIKHq5wfk27G+IfUppPJ/i6axQCDORMGgsOSXROQLMQs/qOnCdOu92IQy9VQulXJ7P5+nddDqPT8/j82m8XOZpnnOOrmZq8LDfTdOUc4YlsIoIAM67buj7uAdUQrC2Vd4HHwLWxEJNWeO0dqyYY5pNuWCK8xRTslJfBMDPv3j51VefE/X9wMQO0BEHS9u5Gj+rFWSHU/txNT6SW4IqKiixOhYmsWpTS0FcDXRcnTIisogzrDbIOk0+Cmu0eB0QIqMSKBnFdg1D1sPZcqaZANGgB4pgKVAESkvwUViRSJYGyFobHiyab1UPrkqhWszMUsa1COQCWSAXJKx4VLU7rO8TgCq4kssi0F6fgkWacGLjeRe/CTdTbEMIw+L1fVSwCwtF3JDIONQipi4jtboAsAEkqpLVAJeCEZO521XDTSvVyrbCheB9530gx429sbIso1oMgVrggFZriGhbM0GqKvWi6Bqv0ysgqjBusQm25KNar7idBdiigtosZxs/FiBrufhIbCYxMKlSy5pERJCigGriLMLGPXkmZAdtcIiaiU5WrFoEAAir7iJsxCMUwDMZocDMjhxpNtYSEdCRjUVCKCU9Pr17hMdTSD74frjD8BZ8cZQlxzxJHzpylDhmzDGdY3pGLKBQZkyjnN+/PT1/mC+PWuZhv7//6Z+Hm4fT41t69V3Mf+AysShp7pg0cM6Qs0iOKjHmUxJM6o5ffNb1g3PhdJ5+9ze/u0xxHKcQ+v3xGPr+bn9QKdPlWbWYp02sWXIpxeL7tUgIgdmFrt/t946xC87K5UXwNMbn02kc4zilcYomdDdeLtMUa+JFtgy/WhNvfHi8O1o3h5wLu6JVkG7x2TfB33q9rUackRz7oDIgovNOoeXCAxE77jr2HftAPrThpk0UtKUAExGRVBKmQR02RtWcN7pm2BmpemFQP7b15m39LK1o0OqJmiJqTfoDsFGttQjPShia8m+zDjZGAza1TZtai9JrFdMEKAVTaT0EEahARjRWoZ6PACo4KdKkbWpdGxC2BFDZwtCWzF0maXX2ijRBEMk1LCdZLI9eK3I1I6fCUoUMaBMK1ahxQAUx4WCqF8jw0oDNGippUREoRXMWTEWYXTssBAQGLrXZWu1+aEaHCKpWns8AaFlgiEy0pC5LdVxsbOuF60YEK5Lnpn+FNQvPbvxC/y5Xqp4LU6t6I5PTBiJVJlao/TuKZkUVtUYfDsURkWP0DvoD9wdgZ0dVcsEU0/OzXM7WAYhQ0aGyrzF7BSiFUtI4kxYAYCIOgfyBia20L50/IBQmIMSU5neP35/kNEMqqtXvzDpdJhXpOkck7KjrB+52Gf1YipTsqAPfIbAv2jNz5xJqimeVKNZvj8w0A0RFVcfQATIyAiiKU2QCiJpzQQAmZufKmD48ndH3l7mAw/vD7f3LB+99ihO+VSkRUBE5dJ0umWA1twMVgIj7rifNfedK7ox6uIzx+TR9++2702nMOVlNfOvelaYpjVNCgBq0ZnbGPnMhLDU6UrIXNjYQFtawet4ri4hIxKiWou2CHw4gxUZVXaualQONfqpzcREINEKiVWXWOdAQpVp7WEfVEtZbsghaOaZitQFw+5mA6LnlcWPVkLPDAah53VZQX8RUvkwGoxZA4IK2YCR0fVrSo0Eb1ZpYINAs4LKmApmVC+SiWaAI5CpYCFm0KDgRIajJtqDWcLBZX2o5WPV5TefWC78YNblYYLikLDGXVJ81HtfMzPUTNpBm2G50hgBSrdTCxhCuGFQDKCpCiqKlKBaFXJS5NA7aLq7Oc7yMk1VMqILzrgqYOfbe5HtgczjrplYCumpzWzSs5l6rWrcFK5Ne1JyRUEFaEgygQF1ubBS1lDOjDZ1jbK54RTarL5Maf1UxgRYgBHbEIfDQTW438S4KxySIuOud74rOGS6XKmYAgo5xdxDXFw6goCKYZv3wFnOuwY6uL4cHpSACnrVMT5iTTQVVSClizhyzTPEynkFUC0yXTEDOgUgk5r7ru25Hri+gUVIBJHDAnro+mCL1dFZGkGQGS3ORjMJSK9d2pDbQYlbzKRxVESJ2FiyjF19+ST4M++OLL7/+7OuvBfT09OH59OSVvQMkH7rechFsgGgtl1ZkJEcuOC5FuS6EpeSnp+dvv311OV88S+cBtLBmoFIoM2bGzEx9x31A562iVdaRUHPjvTRuc31cERSwdCSC1jWmLbYmaFBauQaAyOYj1ie2CrI1WWBFpHUW2eK8pExiS5wU86Rqbl8RXWwmWwnrCWBNdGyZw1VQDRDrVNel7r3azJbip6ooqmhiwVWRw1rgMCE4Qq7l9cCIhjK5mEem5pdZRX4WzbZRwIkIIJElNbWr2VTLrsBoNcCWi6fVdhAFE6VPRWM2BYD6KiLLBF/ImIXHXgNiy6Wqi0NbB+oWrEQfqKqSaClKRTgX5rxxsogImdm5iaiqu6dcTDxoEXVtGbAt3lgRqX5RlSMsuZRyvszTFGPK1tiIa40FMSvR2hm4UnEKWqUi26nQkm259HGoWAmgxEuc0c5euYCSsiqTOFNBdkw+ZHSX4p4mfTplAPrss/393ql7tHEKIAjAztHx/n3kN6eSsxyG/uXxCM9PCFPVatgd5uHlh5lFy4vBIf6arA2WaIxJRIMgczgcbr7+4qcxXd6+eZVHR6RIcyqXgXwIYT8c9/1eMZYyxzyyJoKMwMwduK6wJ0QoGSoR21b16mJYv0YFhih6yTAmACUi9EYZu+pgW50yEgmg77oMgKHHbhfQDx4BnffBUuDaTLc4r9U/EDtWU7coqgDMGAId9n4I3a6DfQ81h6GUcUqXSzqPjAjBc3CERLlgypBLrQJKOedUSmipH+2WrUvxikPYcAlFpa2YoiVrySJZSwaVVXCtZR7DFl+Wwb99LDE7852qAbMwQfUBoGXD+BaBLJAKUFZz7ESwNqRhLIKuVcW3WQcAIFXmQ5dfGCQtaICAoiRACqzAikZwIjNZvMwxeUJRMMPHiCGDpFQ0F01W9VY0F6m2vblDliLUKP3KPNSGL7qEgFYTZvEzG/Utlrg9pzLHNMU8xdSguMZscPMPXr+F7W+aw4tXv93YLShIhdpisYJJW0Aul/Hx6bkLPnSh7zqDAMe86va3eNkiENuSNc3ErqlVl/Plcrpcxnk2trvKIGCT/rdePzUUUUtItPllSwo0QStHdMyu0T8KUqVgQU3WFGoTaVVGqhq9TMQsWWOJp1N6++akCrsOb9zAmhxJIVWFWvhKPM359dvTOKXP7vf3h7sWbDD+m6dY3r27aMkP+wcWJRFQiXG+nC9QICir7/a748PtZ69ef0PgpsvMLivGkjP26sDU1hwSKVS5fZOWImblwC4QE2quw7h1bcSaXwcEKqCMmrN+d4I3M3nCg9NbJLNYax4BWImvWJW9EAkAEjOD94zsiTmlfD6d37394DvPTC447x22rIzGTKAJiu93/vam06xD0KGzhF7MBUPQELTvFdRuK4riHEFEUgYppaSSY0mx5CAlaynVhoXNoNwEYMxfB9GmaVmJiaQlaklaMqqws7FAyFwpRdpQiZuB3M6lSkLX0pbWYX2Nn1mGAbOaLgUQWNV7nf/KBJS05UCDsBSHRdD65zTSA6gpUuji8VGb6rVEA4oCApYFhnCBoTofqsnP1LAQoABQVTI3iRkCQhBSQQVnKosbR8tMMfMFtQWoWwgLr4FaTULS4ntVDkEAGypVdcPm+MC6sQGXFXW2t3W5yc2fvdobrgfB5gua42sHCM00WpcdaumeC/SsGWFXkvnVWLMEpJxTTskxDD1Db9lqSCaQYl5VOxDzT41ebDAEbJVrVKnxSg6AALGaFhCsdB+ubotWiT5iZIfg2UEIQUQIFTQTKBMAg5jvBqqWpjDO0xjnwUvODGKStJZzV4qWLLZ6sGnFl/K3f/vrv/z+ncJcynTs+169d30IO0Q6XZ4dC1FBLJg1IAfnvAvsHACrsN1iMWyx2gri2vXIjp/aLDOaAAFao2B0gXEgxzGeEUkUL2N8fL5cLuNNnLVkQUzzmOZR2WnOqMrEznt2HSHOc3z1/Zu3r98cDrvbu5vj7fHm7kA1OES1CxoBQY0GeM9ZUaDElCxnMDAQone065yI2ghOGVJWUKMb16pLM7sWwYZliNbIcM2Ewzb5dU2fjQkkQylQMkhBVCtGsWgnAAFhrdRYHAJs49UqOjYO2eKnAVLjgho3bXG0QgBFFazvTC5KaM3jQBXEgQgWQS9YHDkLzxPUil9UWKLmawxLmzOlpUAu1oRPyGqwqYoeFMUskAQSqyvq2UJIIKJWWWZNK6xkrJlCkrO4rbJVLWxUXELyuvpAVutO0IYVIoEBEMPSAM3IfVkkNUopRbZQY3Rt/aKWqI6LqiV88jBDF2sbuaoSi6sHu+JXNYmX0bA6brBYz8173wIVXr1viNYeNY2EEImGngG989DZFbCsUFJAoA0AqbQemotIfkM5C9RV+1ZRtKSiKZaYJSUpRazPsQNFqLJSNb8PrLSVQxdQNThu0FZbETFakjB47w/H/TDsD4fOLo6JXDtCQHDO9bs9alEooEKgKuW77/7wt0+/vrs9fH6bfOgpS85p6A/H/W3wLKKg5GjX+W4XwnF3vx9eIDopmqGgFpSSURQTEqGdHQBUGAKukeoFhhCqnW9tb/vQhfx+QqRc9Oly/ub3r7///s3Nw8N0OfsQJJ5lviA5jBNpIQrO+9B1JeXLND1/+PD8+OHzL17+WH8UOqeyxxrjIbSSarT6eVSAIpCySi4ZM5MSKbGpcKoCiGAukDPGpOME4yzTLHPKVa/WPCktG3Z0O0Z1k6gCqiCl5Dy3+H8EKSgCYmphxhEKmV9kh7kGZFeYQWSk0oaavS523tZcWsbxGibKRXM2dQn7eR2ZRVWEipAYl0qVBKi3rMWLrjVOG51cNAtkAURFYiJFVmtPi0xZIBE4AVfUkThGqUTNkidtgrMVgxa5WOdMNw3BEnAQAUGoSZe1Y7er2yLelmTKrFQEMwMXzagZFSGLGQZatY6kVbusc7zalhXvYJE9w02Wx6dIVOPdbaVoP1r2/iRjY0kYa4mFa4aB6kJJr1bYavwsPj2ACRs2Qyk7KIWKsIhu/C37DG0IKEKAFn5s50smttAEYuyuW85oEY1ZxjGPU45zGTz3ntABgqAKgiACEqZULpfpfCnzGB0TqMlQA4ECgVi0T0FFx3F+fHzuwkDYB2au4cd6DNNlenqcGIrc3jRxT4nT9PThtOuC64vzRXOa06Xvd4f9befDPBcQZOcDuc45z8FRB+CAhCQpWOuRLBtyzepSLcZR03arb23tagEBGAFEYklFga0WQdWktWJMr7/77nw6v3z54tB1t7shPj2m5/ddngN655wP3jvGsru73R12Pz8cD8ebw7AbyLJpauTKbPZqS5aic9RpEtZCkB2rc+pdyyAREIGcISadZ52jxCSxViBLy+8FuxeIBNejrQ2tlRRCQmJ2wSsgEBsbDaWoFABQdkKcgVXMPwVCQIFGdxjh/HHmD5qw3ZZ3qE5nI53b0Vjpci5SP7CxKyJalESwqBZVL8AMbUq0jOJl6W65fiK60DrG6RASszArJ2VWZiDWgpBJuVilETAutHIt1LAYe64xdMnFXCV13nfGN21eARAIlVSpikULiJIj5prx5XzwPqRcXEwuJY72zEC+AKUCnApSAgARQWyzfuNRLeN1g0u2oe0VthiztX20NbavxtFmPQLVZWMBoKaqu4Gh9pnrPwsa6hWcUSGpKY5cCpbCUlpRGrQhSbiuhFhTdZYMki0tbbowFeQEVi+Q1TlA4IfD/sVh/2LX3Xt58OIDSedLCDcu5EPoZx3HRCrHow9eiRBRFZTMv0FlptAFa5273/fBswV+ocIBhOAPR3Io3vMietd1/d1duDkcfT6DKLBjCA6jZz/4Ls9TUQEURvSKknPOkQiXymW7YBVcsAZVWsaeLmsbtc5R5rFYggIUWMrZQAsqImkRmcZxOp/L3a33rvchK8gcJc4lBsnFMZNz0KXb4/DFFw9dFywBGtHqy+2mom6GmwIWgZgBikJRi46pt+7ACIBQTHTLuGMpSSTXct/1Ntek0trSypa6ZlyvRQ1234mZLQmE2VgJlSKlACgQK5GY2Htt3WKiJHXILDOkFrHiUuuIS1k/0haNlgW1siraUpxFIVuioSX/FRWQ0uhqLlizDVq6AbSUg+3CbehT2eE10FdEuPUng2yqJQgEmlAZtFg6X9VysAImWes22oYLoQMV1IJaQBFN5A2UW0NFNA1kFQJ07ELoQjeEfuj6IaXiYnIxckwcI81JkVPRmArFhDQ3/P1oyn8CRJ86RvXfZiI1iAeApohWs3AWGNL2NY0gbHlIdbDAx1HV5aHLjfshEwzWaVYKxCiXSwFIcba8+8oqWnxQa7aUdYmT6vYt1nNlstmSDRGUGk+hlYKFh7vjV/f3XxyPN55uPQHKCPkC5eh37He3SimLltxBDOmiZCE4+zQhBO78y89u6HDvvd979DhmbMISIAB6d3/s7gfU0sMpk0IRIry7u+eXtze9829eIyA5T+gchc51gx9O8pTnnAFhLhwBihSx6l0C49M3TFy9d7rkvIhhX6NcDYMArWcecaCOvc/jMwCAZAQhLIjgQ/DBE7MAKZBQSBgu0wfl0Xfd3f198E4cd313OO6d4/Xmt5ikLvHVikEkSilDmjXNxbN2XjsHYNnAADkbE4Qx0ZQgFkgCRdEKC5YhfD1ElpFm07M6+rZiEgpYVxUW6zMnJuxkbWnXmHpz9jeMaQWghQZoZObikVm57w896oEul0NUUdGyAK1falEopFmVrYM3LI5YfdX15Op8sti3vVYMaoWUIlgKllwLYBG02u9aGeEs2rIFDYmgtt81UBNwVB2xduKWncBUmIoj9ayZNTjSUiXcu67r+24YQj8kV4isdItMbdultHRthdVF3s71asVsb+ZizSycz3YHXFggs6eqiEG1g8xA0s1H1dctoK/B1B+EoT+KTh89StE5CmjOGZyTFs1ok6u5f6UJXBNRqAVuVM1rZq5qOgJAzlmWNIdAMggjv7jfffZw8/J4t2d3cC6VGONJ49kF3u/CznlA1pzL5UmezzaqtMUxiJQDf3Zz/2K4AwWZTuX9WGpAQ1UKot7dHe9295JifoonVABhpp/87Kc3P/8v8+XDt//D36Ajdg6BvO+Hbn/o9q8F45gwZRkSTwpFiyTRAuAJem1hlFrtV6MD1fa3H5tqReVNVBHUEgq6LjwcXuyPx29P720VD176UEJwX/746/F83++GJDmXiJ6p93MpMs1+nEGB2UaYaMlKtegQNlNwY4g26wAxFTyPejoVRgkOvaucqxXhiLICFQGThU8KWUmqpvJi2X06eGoItX2zNh9QkYRUANSa0ljapjZjZuGVl0V3e/CrIUQr8QzwKQDRgmW4/NsArk4YqDVfFuG2ZOCiwmQp+lA5ugrYP3CKrVi6Hd6SbGH2tblQtWCsqrBCS1EqRWszRcOgls2zqPI4zdF4AUYlFEJgRA9M4p12AaVn3XlM0fuu96F3wfvAPqB3MIkIScbCmlHW6GP1frdezyYw9gNW0DJ08PrUm3Zcg6EfYIt0Y73o9RBZyJ//fx5Xf2ZZz8qknrVzMHgIHlr9GqAp7AqKmKUPJtREbK2lXfDOdTvyA1BQcgBGNgsCOkJhtnFMiEwwpvj+fJqJZuKSxsv0fpoe5bkroVPnEFlVyjTpdOb5VNsUARAo5Jg/vMFcsH+WImm8pMd3UJKlLiFIOj3mV7/T7kPJuZzfa7ZsdAidPxx2U7mQI1TRnEvJiBBc2O0OTA5EOaLLwAVQCZQtU100pWjNfKTkOJ0/5A+vII7cDVybzWtNCDLaV/Q06yWKKniAOaYPj4+XaS45o0XuEK0X47DfEznnPQKmOc7TJaXJee6Hvh8GJFJdYwAAy/zHpm1okuttda3TmBUwFhhnQdCZwdrRiKIoINVWZGAFek5YJHS984HZNb9nGRuL57J5bsdM9UMFCBQQkYDQOvJagL0eVoPt66GPRpU2lmeJkzXZSVwjvtbD2jl2nnxghSLKqgJas7qxGlnN7FoIzHbEqx/2R90AbPwMmuFNRK6JMXrH3lHnrc9XM2IUUTEXTERM0lItsRQtlofdSCIUcJJmqpW64CzTkRGIWV3A0DtNHnPHJQdyoSraOcce2SkULVQSlggZJUNJWpJKVilNGWm9qhsW+IfNx48wAK5CYSsVfcUAQQWeZbc/jimb+/t3o9LHn1NHGyIQgSfoPewC9B5Mf8MsyVzQavOy6ewCFyUk9q2JDblOwMfCmm2VUkRAYEbvmAAqS5KLPl2mOeqeaGKieMrnd3J5k0ESlooZdmVUGQo3VUhC0Djld9/Kh7dKrmQpKZcUO0lK1oNL8tO7eJ6i+lIUJQ1ZwARnpEiJKgkslBDnnCbr37wbDt57B9QpewEqClkl1zJelTKdn6fTkyLkHJ9ffyvPr3bzRLu8hFZb4q+iiog+jvLts6QiP77hHOfz+PpZ8ZYzEyCSgCviFJZaP0fEJZV5uqQ4+sDeO0K6XC4psqSkWuraW4cIGgABaBUtNwqFyAc37MIwdOOUfJesEFSwLY0KNYfdeWJuKKO7/a7rexNbtxyDFW7WJU4RauvJFZu0iZQDABm/uh1cTTZioaw+GX0NfdqEWf9d83mWZBxnWOBZK/QIgAMwuavNH68mAGCtbtku7T88FWHha7WuxkyWateadngKjhAQVRCA2g3PhThLasYcomQUEhVUUi2oKFpAHcQzNO1IBnSITglBmKQ4EYCCVBxLUSQiRmQlLmaGZY1OZioT5gnypGmWNGuu1hBoqTCEcPX6dzz+7t/WUFi7V5sr9v8rAF2/X/7ohwyo5r5Z0gQBqnqEvYf7Hh8OeBwwOOw8BIdEeIl6iXCOeolyjiXFNM6atRnTSP6988H5sOgHITMxgiN0VUsBveP93u33O+9vwvF4PNx08Unf9YIyx8cpnYskrZERtrEoluNISggImeOz9RGTmgivZCIxNs3S6OWCBVJBRURnVEcpOaY0pjynGLnkMp3nyyOyy3H0zPt+pzfHe3aHndcyvvrtf4bXb0QLhY773YfX3394873vByQc337v41MfiuZs5rBhUDXXVUqRt+fyqzfpnPRh4Je9DpRTxsGxJwXgnF1MpKJSqtgzEXEI7DtkV/L5/dt3796877/tDvvh7mZ3ewyNBlUFBCXVKwPcJnLo/Oef34TgvvzifhzjZUzSCPVt5nPN9QLz7kVVb28OtzeH29vj8TgMg/eBWvIJrHDTnJyPDaLqH0KlGZbNOq42y2IbyBsAguZcNgCpRiI1hpoRKXg39B2oMlHwbrfrTHQnm/ROyrqi8+Yz1y9dsQcrzqzzo75oc2kBauQAkBGHwEPgwbNtdN7UsOypAIIqXJRYiJWKEisXSaUG7FAEzVkQcTB9QCZ0VOW4HDkhQmUpokUxCxcBUVLAYuEF0AxCCMg5Up4pT5BmTZPESdIsadI8a0nVGjI/ABeu52N8+GHs/ePG0Ypk1zD0xx5/h93zMY29ITaXV7SSXlEgcAh7j/cDfHHAhwMOAYcOh4CI8GGED6N8uCiCxCRaLLukSbFIc/ytyLgKE1PV4WRiQkb0jm9u4eaOIwy7mw7vXrg8aLqU8TnneIlPqTTGFRRJgkPPUFVNAGojGFUAEFp4ds11aghpARBRoFpXiBZIyinFaU5xTnOSEuPldH58A86Pl2dV7UMPh+NNcOT9ZZ6e/vY3UT2AhsNhuL9//YdvXv/hm25/dN6X0/sjJbkP2noLgAjWIL2gSsp6meVpKuekonjf0X1HU0JAN8rNKf74cZan6T1oIxNymuP8/PR+Gk85zaXkaZrinC5nKmm/61FLhhr8sfEArRmnAtSujogQPN/eHoZhyFlLgVJa7HkJQl89YEnsqORn33Vd571znhGhUT82vJqyzQJDjadeB3KFnzWS8r+41sJCCa1+Ay2Z0UtFEiLRDg2JjoddSk08PluZW2lD+ppZ2P7b7LKPuPZrQrXt2DRHCbFj7BiDw46xc+gJoYr5S1USEsmiXIALuAyuaCr2WqgUyiVZg+pcHEwf0BEVJkckzELOceskr4CGPkaHF124LUUtwCVSnjDNkCaIs6RJYtRsplAGaXZQw6Hrk16p+x9Eko9tJm33zq5KW1x+KFDRrt46tuwHul7ejzhr+ORNW6fq7Ve1HvV1jVuIzvU7m/q91F5DkkvOVVCxFKk4oLUiscZRNyugKhDR/cP54cXllHT38PB533VA+fkm9XfPl/FduczJC4Co5pxRysMO73pQUVJdao+q/W4JuwBQhcoVtDbbsOG8fLdIiXMcL2OerFVwHC/P6d0f1A2XeR6nmV3ohhsM/imlx6fx/ePjOBcpcny4fRGn5w8fzufp6XkGgIGlPzAigkLJIrmAFLB0M1BUTVlT0aZMirvAnh0zTYXfphdvzp89Pb9/ekoimlNO85xjdkwMRUoqOaoKMbCzUytobhcaAWTII9ZlzPQjKv1I6JBC6PDASA7Roam+trsu24DCIkpuhUu05Ic1wUXc8N0tDNaKu7WNzusHLj9uFmllF1qE5Ycxqel0LDi0FmdQE4FExxxCkKHK4DRhiaULzmZowzI3Vu5K1/OAZd9lurQJ1E6jTWACcKgO1aO69lQpsIiOSVERFuCCLJgLcEEnmIpQypQz5gIpa86C2UE6gzIAIzCaGAgy6fYqKyz+cc1UN34dqEQsCSSh5JqNXoFQ258vxt8nXPIVNDe+evmjZrqulBBazpAu3TzWu3tlQFYU0pqrbU9pF3Nd65qduVibm+jZOrrApIZIiLdWflukNqe11CfK2u+19gTQbBi03M/Ggm3+VwQkplKsH1yMcRzHJ3aUnE+HFxP2ufvicjqdnh6fHx+fHj/k8fxnL8nfo0fyhKaZADUjuVZ/iKoikqj1S0BUUrAIVU1rB5Ecx9M5v3uGdAqoAGW8nM7ffbu//6IgF+DzJU9TLD1ijOl8efPm6XSai8ALkf6wO5+n5/N8uUQp5e4QbsJgV7lUNeuCUmIul1jGUb4/l+dZAOHYORV8jCyuO4sbM/7+/OJRyundq+ndGyc6nsbL5VJyTuMZ8hw6pwSIuD/s2DkA2PUhdMFWSwBpzZmwZSqYTbQmzKxPQiI2PvgT32MzJrWpBK23HLarxjqQcUlQ/CEA2u6Km2lxBT2byVI/CBdjaBlta7hrU6jK4Hwr86lfr9ogpG20E1pGuW5H/mIobCaF3cTrFXtzpKoMwiCsxTZMAFRNU6eqmItTzEJOsQhlwayUsvqUYkou5ZgSp8wpOdAMljegAkIqAtriuavVtm7gcpFAUTJqRqkGGJgY8vaaIsD1TIXNpy4/rqzz9gY1mnl5rSZEHVoVelA393VzpLokaFYYulrqVkNzczzLZVaAra3cxpe2z6w9orRGmdug0c1L/dZVT349140BsiYftGtFCEzqSAMJyQTxVNAXgtz3HHaHOw4xHafp5unR/f53p7d/gO485lFYjXVmJCKfm9WqiOqwlFxUil1JogQ4ZxQOu+PNsAtMWcJR+oOVHfrhpoCOubx/fHL7F77viLvHx+n9+8fdvus16ji9fTo/PScBDMeShcYkj+f4/HyRUhD15dFXfYpSu0yBlDmX81yeR/kwyVTUMb88dsDuMfNcYCo5x3KK30cSyQUwAHJMKc6JCVuiEEpbArCNLW4icpuBdbXcNQqWFsRvFmEdyltBnI8fP2SafEQfr/82D3lrUHy61//S44d3qTzNkpB4VeHoattmctae6Afo5cXMX7Z1hRYbsasP0GBnNYLgGrHah6FkEqvUyaQFLZtT6kJsG6JUgAW4ABVlAUpFY0ox5pjSXCXrkwNJoAwqIAWEQQsIr4TL9uJs7QRVAkDJKAXtUIyLavGCdX6tF+OTu7pZCfCTjT+2r0KFnuV1g2XLaxsQ2gy51lR7yXHeHpOum1vabvvl9cNqkLmJGOj2wFacE1VtoaPV/KkHploTSBZHsn0lIw6e7gZ+2OGty3u9BPGKKXvd9/tDf4PsCXk8P/f7/t0fuJ++n+ciCFmRhYg77I7niM9jEYCuC/uhK+cPUeYsQkTcDafII9Nuf3fz81/s9z1KFApleJGHu5K6+cUv5el7SrGn3e5wHPb70POv9/s3bz88PZ59BwdHnXNMRQQen8+//u234+UyG+8FOMUypyIiOefWvzQXzQi5owJevgNFpH3vfvSwp+CeCo05l5zjPF/kD7l3KlmBRd3p6TxN07AbiN3SwUs3ps4yGRu26HrLFztoDb3WEgdowaG6NH6MJ+0ztvd/S+hs+J32h63UeGkr+SkMbWbS8leb0ffxAz9+V6PyuiZMU9OL2GQHkcWMrh4/8LkfWTRX9v81R7GcjC6b7QciUDKUBMUcoIwl2+iHNv5BRJAVnaITdIos6LJojLWKrjMYisnVHHO1y2u1lvrR0bZzWOoYNlbE1Sk12ZyP5/j1xdhYxsuV2u6xwaNPDN/rZWhxtJdDW9p56Ca99VP2cflwbU7Vp2Q5LH/bjButRXDWs4vkGm/tw2t1TLGE6ZqRtXGqtfrVaMds310DE4x46NznB/+T2/DjA37dZyR9A9NlnrNASoXD0HW9C/zyq5f7vcyvdH4z51zOuQAAcId8+zrrt6cZAG5w93K/lzimWaSI99zt797KcMH+R1//2Y/+m//T0IU4neM8J9GCFFN69/Z9colDeji+CP0BEAix7wdm//z8rByOt/1hyJdJMebL6fLr86XyK0QAkIrmUkAll5JTlJxKSaIZoXRO+x7VBGUATlH6Th0kLmVO5cPpcpFXxA8iBQALhHfvPogUJJ5TSbkgUlvdKgoQomMmYgAyqYotkLSwPKGlVDTBP4snIla6CLDZ+1tkufb11wG5LqYGh+11ExDbWkPazK7t8N28sz2vUHBhgPGjWWKQi1YeutaP1ZzpRd9jC6/r8wchdT1lXLsM183KnwM0XbjNmt98FdXaXBRyC4PqpsufiKp1M6qaIMBkImfCpI4VfDPq2MECBWBJU9skJlgvSTXD2lGuAFlvSoWeZlEsF+MKXhYzesFphE8xqA6hP/74aKFqN2/z5+sx1JEo7XX55O2G+XmffM764Su+qLbOgaa1VK3zax8Q2r1YsW/z4Zv6f1yYdkRAIjp27vNj96Pb8OWePu/0kuJ3l+e3rx+fy7vnRALM7EBkHp/j5Wl6/yo+j0Z9MyE6wU6fZ3i6FES6nC+P7x9hfC4pqygx++cPuHfU79++ffPf/bf/zxCCnb9z3ncdh92rp6iRH252P/nxn5wvzx/ev3776htCurm9SzGV4FK3x067IADgVYixiEoGrA1hKDh2hCmXOI2QopYsWhi19zgEFoBUdFZ5P+aU4zzP57lMGS4xZ37y/hVyh+xF+vfv/gBQ5nkWSX34mo97Jmx8W+0jW8WYgHDpYYobsaplSK92UJUdbHxcnZ81/rAJmSxMBC5+MlyNuWoTLPMZ2mK13U83uyvAwtfUH10P4dUqgRXSdMldRkKrDb6uWF2SrD+27wxkPzGFlgOsGq+6UA+6LOnYBqntZVZ7tUHX1b7yDVkkZUlJc2rVAbUiQ6QgOSRn4XZkh+QFMJdSsgk/FxQhLW7NDKiAWp+6si5Xju/2yn40YbFlpa6mwXKrljUBN/cELIFXrxyTj+72/8rHYg/ZmyurFBEBrLT6o3uDm0zI1WJpsLKSSLpwj6iwsbPa/vV5jT4bG/bveGyopJrMjgp0meZvv3v/l3/9zbvn8d3TWIoyMyGiFpUscdYcQcTKr5Ai4JQKpKKgkBAmRCgWoEXH3PX5lz/7B4eXP3r88P6v/qd/IVJiTtM8xZSQuOsPUnLv9XYfvv/2D0g4z+PTh7dKvtN09BCgQImM6jyjGwCBWKcxTjm1ZrboHTHqmHOEicsMkmu2LgAC5CJTTOeUs9B/fnw3zlGRvA/OeXZnoG+5fyDuAaCYEqMIIQYfGMmmBYIR3kVFrO3p1YK/qJZjy3mx0iwkJAZkRGcb63q4uTkbPwrrLIR19K9jZnVQtFEoa48e3ZDNG7NmNZlXJEIAWHNr109vo8d0lpYymOt6+et61XVNhxZwvQKgj2dUndW4ZCmu5oOuO9isacvlBkIVVUykreRUSkwlRWuxauq3tlGzTNuTnTOhCCtYBVEsQqpOm6sm6ApyJkfoxPqy10tSi3HWibhQHzmKY2USh8rQCu3F+sO1WdoMvXW6WeZoO3ut6Zf/28FQXcEWuxIRCUDsFYCseGnFmk83YLncH+OLNu/synjfnNrKd6+IteyzLq1boqz9ceO/UZAVWIByLnEc4/NzfjqXp1NKOSlAI53sQAlqKN7grx6FKhGjc8SuSiSodoA//8lPf/Ln/+jX//Hf/+r/8//oQjcEF7RkyDlreT6jlEzwdIbnN98qQEp5mmbHEBhvGAfUcPrgMzI7v78l50FTLI8yzt578g6QiKmoxpRSGREy5lSRGxFASymqsDsc//4/+i/+8t/86/j6dSlCiEQseYznbztyvOtAs5kejrkLIXhv5Mew86F3zHA5j6Y2QMxNgo9qxQYCIgH4pstlVg8jMpKD5o4tyKW6EknNWNne/3XxvPKa2ihZFpiFrlru89US3WI50DzxjwfsQr3oZhBBO6GFld76YZvHUr+x2D7XEY8felzBzUdT9NMdmtWoWzdHAQShIBTQrCWZ2F+VXErZikkaa15v1tKzbCFVXcEuo0NyQE7RKzohR8gbR6oaeHVKLRKLoJJn8SyOGgAVxaLIikWBlhvzyUNxkySuHxdf/N1+2P+6x/L5CrWTRsWCFnFb6KH14D464I0N1ADIBL2X5bfdq4ZNy6BcjuIHtqBi7rIEA9iCx4peySuxKJRcSowyTxAnSpFyazy/WTvtbc1uaYwVIO6H/vbhYej7eZrOT0/j6STEw+H2i69+Mp1Pw83t+cM7ztw57Ah6UlUhthp3a1gGSqIeHKoj8ax9wKGjpPhUiJwXdnnORaCozqlkAQLUgqre9NsBi2oBAQUUASEtosR8//nn//v/+p9dnp7maX7/4YO5ubV+vNrG2WaT967rgqlVdEN/e39/OB6c4w/v3ntH/dAzu+ZwazM31thQdRERiSwzaOGDNg3Cqj3Tlg1YY9Fbs2hz4wwyxCLDoFsGQq8/dv2TLZ41V2/zm2Whg43nvsQwFn/sE+BZfrIc4cevH63qC9ZtTmU7JOGjx2oNrTBkANukfgShgBbQrJLEErRjTDHFmACwdjRqMmyAi2nSXhVcoQ7RAzpAr+SFfCHXquENfRrjBYpWilKjpFKYCmlhKVSEimBWKmoKbWia+4szvZ4UNrSuV9HSvtrv/7cFIGjOV00jISLTrLqyY9odUDAL3Abg9gYZ+oiCgLUuIAFqfV7Wc9t6ocuf1HHdftNAcInvtGPFyg+JSs55GsfTiS7nU5onLIUROu8I0XoHGNAQIbPbD8Nht98Nw24YQi0GcX3o7l++/OLrr/rOv3v77i//8j/+9//yX6LvumHYHw7Hu/vhePvu9fckgkq7zjFbK7Pa14ygeBYmpZo5pp61C7Ab6N2McFKRUoBykawggFoUUJnRcZ3NCIpaVFQEEVBIFRy4++54/OyLv/f3//zvn1+9fnz39sPjo4qUnGqESzPIrDIDKBGFrjveHl9+/vnheDPsdrv93gcPIClmh9qFzjnXrjo2RqOJKRRAqWaQECAuSvJwNXOXmbrBn2vKU9vgWCajSiVpl8m+bH+65GK76Vfmcttq/EqLpSyOm25NJzPlSVay5FMQamAM16+fHM0VVl7ZOjVKssyFT3eHZYKgwRBWGMJaRhhzinGOcZ7nOZqcnC3bzZ2yOkmqG4CK6KbCjOSIEhIjOgCuXaGkXVbR5etBofr2igjnhJdMU+EoLoEXykKiJEoZLHcDqabLXV17XIj+zTX8gRv0xx4f4dSVlVw10Orgqe1IEE3KDCxpgWTxqj66uKtxjotp3YY4NBhSyKpZrA+YrnvgmhJnglFS+SDVqqCw3HdYaNM2gCqFqlULPeUUJTuU7FE7xuK4OIYmB5WLZoXg/XC8+dFXX/34yy9fPtzfHg99F6z9bAjhsN8fbw6B4Onudj6P/+O/+8vd7cP+cAx913X97nArAsfDHjSlXABMDQOZ0BM6RM+FqSAiI3giZg3WvNwraEnzWNC4HgR2SOwDHwa/69jWFUYlkSyqQgJUazbd7XD8+rMv/uTlw/2f/f2/+Ktf/edvvv32/HwqWJxzzAE1ab5AmSwRrx/6h4cXn3/51f5wDCE47wgx5+Sc96QhBMfcCBNqBN3GJrWBjwoiC1dLpqNq0uKIjbqu96RtLQNx46M1HT5QqUoXTbUNq+u/HZQ2hlYn5uNfLl/RotBr4ELbLrockZ0dXKUnfmQSLWTQJ47Yp+bNNv1vRZ/Nnthgue2ugFfmnA14Rq3NNchyFlEcqAMoqBkLqnU0NBnB1qHHplKzgxDd45SdA87gHLis7NQ5rdoIbf3Xtm43z7MWP02xYZByBlcoCIlgAcpAedXiXuzUjY14NfuvwQCXBWDdwS7KlSf9SdRte7cRG99mOICiApbLryIVoogWX2zluRa7rY3aejRaO8FqUTBVXbu+YmrFLa+1Hc6GyV5PYb3BS0BjE9kArihUVAqjBNKeoXc0eMrZeQ+FAjHvnCcfQr+7fbj/7LPP/vwXP//lj3/04uawHwKiZpCsgMiQkk4Xytnv+69efnZ/+/LzL396/9kXfd+z97d3Lz6/efnzX/zk9YdX7969yTEF79ARIQgqEAASkRIioSgKM3tPXfD3N+7z7J4TRyXPdDx0pRyJXXDYMdwcIjGx9yiKRaVgERYgVX2eO6WH3fFnL7/45X7X/ehnP/vpz3/xn//mV+N5/OyLzx9evDyfnqepSHwkna0+4nDYf/bF55998YX33hJPpFbEFEB0zsS5cWuhmDEJzZNagwKgVtJRJy1tpBFb/vHyk48mMC6GMQACWCO4TVL/xrf6yPjAzV5XftrGxll9+IWIWj5wsZ1rIhy28PyqKvSRg7auaKtVtJkzy+BsGInLoruyIst0/IiVb8dr2pi6hp8JkFVYxYEWBIdQEDxZj/kikAURobSEOtHqaVYNs6cxGe44p5zUeWWnhFgz+hRq2pEC1+IZE/FHIpqjTEljllTMQbCuA1XBCJone331QWuzNFNBtiDV9U6fkNOfOmhXAITL3F99Vm33vcYYCRCsVlMrESQbdt10SfXau0KLoy2GmqpCEUhFY5aYJQpmwaLA0ERwF4OpsffLowpMYWsIsKxdiK3Fk/U1rMnXhOSd895757zzHBjFd77vbm66w7Ef9sPh+NlnL7/84uXPPn/44uYQSIkEmR11gEGJ1EUCgnkm724++/yXf/YXL7/6keT0/R9+9/j+7f3dw3j7+d/72Z+6jt89fXj37oTIgWnXuSHQ3Y6d8ScgdkoU9mG/c50e2b3ErkuYlJ33UnKR7EPHqFTSvj8Fj8ehKymn4s6qSYupFT7P/VM8POwP93cDMR1vbv/k53/yn776q8t4+ef//J//8k//Xs758cPj7/72N3/9V3/54Wk+7Pefff75V199PXQ9IAgUkbXrlek1EzOsZQ2LdblMFl2WP2h4ZPk8pSFHc2hao7hlg1budyvhDNaCSQixVCRpH//RCN3+uw7HdSLgBomauaVXhtfV6EdE1VVeenHJWs8Yw8/VRlkxaDmIzXfjdrLUi7OM8mXdXS2lLTm2DmkAbB0LCDyhMigjeGvIgEyQBXNRFMBS8RukMsqmHicK7jRF59R79aJeQQA8IhItPaerorcCibBYFMM6g0KMMcU5xbmkWfKsJZqEEEgBE3aBpqLSiHdjCowYr0HyjS+2udrQ7Kdqf7XtZXfcANBmwdhc9bomtvQqsi5pq8Gz4ZoVREWFWqwDl//q7W73yUS5Y9Y5S8qYCxbBQrUFCS7lGxuDuhrT1m+e0ZqYWS+nxS60Y7BRD4BLgzpBxm4f7l8eeR/80e1uhv2h3+27btjvd199dvvF3e4uYEeKJS/6vZgzC0CKOk0+5tTRrt99/YtfDl3//e9/99vf/Gocx/u7u3z/xXH/woXvC7i5MPMuZ0nAk3LoqEfsSACK2oT1ez58QXTpvd5S8MIJvHM+51RK8t2AUiCOg5sHr3svEfVc3MUrKLAWBHmO7jl2/+DLu4f7Hom8Y1V39+KL//PPvv6//V//L59//dPzZfrmt3/zr/4Hfvv6m/PlYilOr777HhEO+10IntgBESExKKM6RqoRrqvpso6B5tAs3RC0uc64mZE1xFhKu1ursdQK1XnLvWitTV4KcXSDRB8fw9VjNeWvLJ0WE/s4QmJe0rLaKkItnTVNYFqPEJskbPu7awxaXCoArQlpNhFBr3PxtgCEJncBHyFoDd5WBEfTEUcglKqmRiokQioMJFAAUKEsf2Ptd1Wr8L5mUTfPk4oACBIwk2ptEG7Bs6UrkAi0jlfKps4JkmJKcc4xljRLjpqjSgTJtQZtwx/9UY7nGlG23kr7JV6/vd5zA0DXtlJN6mwbTToEFEzg0qgiADAoUkABQVFZwa5+fjPSVLWIpFzmRNOcRw+Dw+gxZ3IAxhJX069ZqbZiOUbH5BzZK9M6rMwCEwHruaCqlzm/ebz0IfSOj0M3Z9bhtnu597v7w+7ehd6z887tgj8O/oZlly+UNYMygIrEkvWc6SQDOlTIucBccp+kc7dfvsTT9Nf/5n/61e9+d/vw8p//7/6r026Ypni5zHMShW63/2o37LrgAAH5lOBSMAEmEGQUoE79vSBhmT2BR0IkdsjsRBmAVIWZAuOA8z69EtdNez/4YZxTnCaY9Dn63XH3858//OjrAyARwBzL4XD4R//gZwOVMj3f7A5/8vOvzu+++A//4f7xNDHz5Xz629/+5nx+vru7ubm52R8OLgSVwpocuADFYw16tjzfjxbrZbq36bdYHno1XNY/qrkkqgBoQmd4Rf4SkbGKVZ+kFSP+sQH+yYBvX90Asn65blI+tJnkFS+sKxtYFuv6WDyyTVrfetZXdtB6iggb+G0wozVvth2Ubi9cg/g1f2AN2RkAmb4fEbka1CAVAiGgOhmqyJsJ4Yga3ZBFUtZY1M3TBKpIyozFkapbEkcNtLJqFjAMYmOhbAZD7ffWgKk1k8KaPL8WdFZUWBihNTCGV+hzZXgCLIiD+EMI9ccBqO5jIwOXsntY4dzeLdamCAogCcomZ3ozOOpSkAvGLHMsc+JUjJNu3a6l5jygVA+UiByA99R5CsG0L9FZ74elpr9AqfYviIIWOI3x1fuLKoJKSglc96xDd3ePww0NR2JvrciypJQBxe1y6ERdljTN5+nydn6Cx8vwmMLtj2h3nJimp9NrTb/f0Xg79MCP796++/5bs/Rd1z2fTu/evTufLt4NL+4ffvInv3jxxYt3b959+O43H94+spZDB51zhBILvBrxvhtAYJzOp0KXrNP4lGJKKc1RGODYwRdfZD2qAmi6OMB7prsOZoQnCC+/ePF/+POv/+FffP5wNwBwKuV0PgVPnz8c0+n1c744ptOHb85v//b25ubP/myX4pxLfv361Xfffeud3+139w8PDy9eHPcdP383hCj8rR7vcXeH3QF9D+yBlnShhih1iDQraQWLP4oamzh9nbaIi1282hfVg1+SNv7Yx22G5CfbWG2zhZHclEU3x6zOAJsNq4pITb1ZyaxW3oDbKQJLuPDqCCrHhQ2Nq5I9AmgL6UAjja4RfSFPm51vGqxMyMyiIuJcs+ZEM1pPAK3zQ9E6qIKISC4Si8QsLqXoHIs4AEAkYmLnyTlkJAUSZAUvWBQZxMhvgkIgDKVqykmRwsJUTPij8qxQCw1tXbn2Phfv64eMoK1fhtdv10v70S394XvecjtX43Pj5AIu9a621IFc5w3We87MzOydc/649/eHcH/w9wd3u6ddr74TwFKkWFFYHTz2gYyOoAvYd9h36BidI8eIgKVoypDsRtWeTVAEFPTpEud8Oo3xMk6n03l/cwuDaqByGTO+yVlSjFpKAL3x7sXnL/oX9z0Qi46XaUxjlgQCUwhwc+sP9wExCrz+8N1vH59HGG7n3fn0PJ6exsNxnCJ6fr6cQIQJzuP5Ml+y6M1nD0/nSyb/eJFDBzc99l6ICDwU553nnt3P3PD753z+MD+f8+l0mcapZDgeDzf7/VM8P8XSB/LB9SFw6J3vlfoE/Rf+p/Tw88++vAtdSKl8eP/0/t3bt99/8/bbfogDdzvSOJ3epPE5dN0OPO56ZmTGlNP5PMaU3r59d76M+97dlfehn0Zhd9r73R3vH+jwEoYjhB04XzvNV3LO0MTWoR8YJB+5Pj84uMxnX8PD6yr2UXxp81E/HBHD6436Vis/sqoXtTShGtir63VT7VjcsaumG2uJ1dW3mBn/EUQ2EFrewEaVov0MK+bgAt12si10Z5KegA6wAAVkYnWAnigz5+ySy5JySeurEBedc1FMkrEYwSquTjfj+NixDy507D0rFkVRcoqWFENQSJdnJusCJCIla2HJVKhS9h9d9cUpWowWhG1sCK//Yvm7BX2uCKAfCId9RK9ds0KbffSTXy+fSUrIqmoMFbXGzI6d96ELoQtd3x92/nbHtzu62/PtDo++7Dg7SJqTuiKUC8SiGUARiUmJIATc9bwbiJrEWC6SiqSsMUvJmgvkbA1PABTmJIDFu5JynlOE01N8Pk0FC7pZOKaSctYiAemh6/6eIFEfvCfmrAS+PxAXLtgj7wfqPBfpQ+9CHy9Pp9MTTSlOo6QYx/Hd46NHejo9l5wG754vl/dPb3/3h2+Gl8fHy+lcVPqbHJSG0rlnQSrM3mPw/oZ117EUep4xAwN5H9Lx9v725ubu0A3uabjR473f7boQeg6dCwO7Ht2Qhi9T/zKHwM59+PD029/8+q//6t9zOZ+fPpcbSuOTxifJc0f+/vamPF0QMQQO3olK33fTHItoTOnxw1nyk++mGxGYPgz9W99/x8ORdre4u8PhFocjhj24HjhATftphMv1INnaRbAZVuvoaukXFXQ2ey+B4+asfzSGV6t9/cbtn7dojWFkK/Q0kkl0WbwX64sI0fo5b6PzWxhCq+z9obG/4cy0wRIAVMn2ZX1erwm2c2z7fhxshArxDOAUBQgIGNFGiogrXkrO2eeScknWpySLixkpASYFEs1ZIIm6ep624DvvfHChcyGIEgMpkLVYKgpYtcpSjVGLClBRzIKxwFxgyjBnjQXMd9N67h/ZN7CYtKuDff1o3s/HfcO2n9IsmJW12/iu12MByNSgcfG6dV1bcPmO9kQidszOOe/6PhyOw/EwHI/D8djfHsLtnm/3fNzx4JVyhDTnaRzPk8YoPFvuZgjujulww85z8OgcEFpST4kpl6IimEVThlKwFCgWJmwY3Wg4KaIpxinmy5SeJ30/lnFOCBAcH4fheHeXSxmT9MxDN7y46W96B5DnaZrOIxGU+aK57IA/391/hvnD5bukRVQJsaT4/sOHB98/np9TSt5x14Uxnv7w6vf6N845n4jCw+d4dHgjnH6Ti57Fh1KikgDgdOqB7o/Dw1dfzgUA/Rc//hNCJJm+2OUvbujlkTu3cu6lYIZOu3sMR+89IL998+av/urf/+bXf/OnP345nkdNXvAP8AAA0/5JREFUO00jlrTvwvH+5c2X9//iL38VS2Ym59lx2O87YuLgL5fp+fH58ip+P5ZzjLeP5bY/3+6m4/A4DK+7fnDDgXe3uL+j/ha6I/peXafktNWzbgmCq+KKZVx9ZI9f/f56UbvaWZdJq7odijY6Px6ZuPkzmwdqzSiQcHGyzLfCZqnTSj8bEtWOY5uURVut/xg9tRRIQa3RXOme1U24Mo9so1pBqgK1ZDtLTpKT5iQ5ak5QMiK29rDKDKDqfCm5+FJyztYuybmkyAtupKIuq8u55Kz2tPU5ZRE0TbQi1RoCUUApILlKBYmg6px1LjpnnbPOyd5CstbUCmIyOR8ZLIuH3m6a5bltk8i1NQMwFGpsmb3qNmRm/hZcYVqzW5f/oLG/zXdvf1DvGyEFZs8UmANz37l+1w37MOz9cAjHY39z7G+Ow+2xP+7dfuBdz8GB5jyezpfT5XLyETylCScSpxT4ZvAvQtcPg3OEIATAgCnJOOfnMZ6mdNI0xzmVWARshav9LRSQWCvCoyKKiObEkjzxrgvOhZwyEe4Ou/3NPjE/qwYmCj4Fn4iFOFEUje9ff1uE0A+9dgHUF4rned935NgTS84fHt8fv/zJRfJpHL2j3W4g7NQ7VQz90O0OIYQhUPYRp/c+jSo8TfNTdyzDi//89nKaUun05w83P/rR18fbF98945xyz4cfP+BDnwfOtTBRBQAUUahTP/jQA4Upld/+9nd//df/6fvXr/7k6xclzhIjaWHQvhuO9w93/YsP5/PvXr/PokPfWQgFQR3ioQ8dHVOQ+XmIl/PbeXz/OO/P+a7P97t4s5uG4dKdn9zpLfdHGm5pd4fDHfU3yk6JpVUybqbWFiCuGMy2D17/EOFq72bSXLGZf5xuat+lKwyhtsEIpIuUBREz+za0V59rwwctyUG0ZH/XQ8SGKx99uV6xGdUL2/plALAQqW27ptpVl7GoZJPWzXEuKUqaS4qSU8vbWW0zACJGj8TsvBcpwi4VpSQQs3ISS7R05zEqzUoeyAN7oAgUXAZRLDVI3DCoCpUVa6KIADHDnGHOMGWYsk5ZY9ZkdpA2O6j5o9WXNA9TpEahwdqBbBeimmi2+F+Ls7a2Sfqh12sEQmzOeLMvdXWvmDvve+d75wfnB+c9c3AUmDvHXe+Gox+Ovr9xw43f9W7oXd9z17F3yKRsvbQZSu5SUa/kxNHE4kQwMsNh39/dHF7c3+xC8Og6dD2E9+fpzfP45vny3M2nXTzE6ZJTKRqTzLGUojkXUfCeCYkdIXMRcIzDEI47H5LnyKdLPMWYYjmdL6+Z3xxu30HmgNrzLDnNRVTzNM8pvnr8wNS9vOt/8/jmVZKneEmnNMEFEbx3UvL56cP0+dc87KYUD4SDZwVMrI+Pb8+XZ7RxwxhIjl/uXr54oO7uKR8vGB4v+Xevp/PzU/Dn0A8Pd8fe0b/7F/++oPvs5d1B7/ef7XZ7p5KbH66IgIyEsQt0mcu3f/j2X/yLf/kf/+qvhy4QCJcocXROHEM/7A/HO97tf/7Vy3PMT1NkZpGCpsMN6pm6XecPL+XhMF/Op8fn09PpeZ5Pl/h2KvtTuu3z7XA59h8GTy546m9ouKHhFodbHG6w26PfgeutHK9CzgoZ2CyCT4ihq33+GCht5vZH+1wD0PUf2YpJVlFNLc9ci1eXq9dQfYclIai1Z63Kve0TrntG4EYEAjao19byBkeLxwEtcIQrRq+sabWDTKo1Wyp/mqc8TwZGznnHjpmZXO2OXj1IO18FAHYxFp2SMGdEFNWUiztdZiVfYYgDUESOrkBFH6PFqmcqtZtilSiXaNSGSLaOrqJZWyNXg82NqasNgupPWn+njRUDsM0Gu85FX2uGqXnEWAFou6Nz5kWxc8TW8NVz9a3sV95773fsdy7s2R8o7NkHx46AERyAC+B3yDt0e6AddB2HjnxAF5AJuKbwQFQRwDnD00XeP8UPT9P5MqeUGPU4+C8e9j/96n7ne8rshJ243nfHYff1w/0lp8c4vZvHD/M0RZmTNTyBXCRlKUVSSiTCjq2ixJE6Rk6FRDTHklPJJc7zHMOljE/lvC+7LveXLHPMZUxZ41Tk+5x2fb/3/tvx+dXj85uYJMvby+sYR0coquPja4R899nL3/2KVYW0aB6fL6OS975j50RUVRzo5zgMxy/vf/wVJnn3+Pz+u+/ef3jz/HgZumGaJtRy6PTn9/Lbb1+9+ebtZ8NPv7r7MVIPUBYNVUbNSKCE6D+8e/sf/s2//ct/+68f37+5GfyuC7d7H1hB0nA47m4e/LAnx1883P7+8Rw/nEspBArA1ZZFJGTf9dT3oR+6Yb+7uYzny3i+XM6X9+fp94/pGOR+J3c7uhlk6N5352cf3rhu4O5A+zsc7rA/Ytix65WCEisQIG6jQJ8m5G1ZlCuiZ2tBLJ7MClgbNnixgD7y62qhCdZsNBVFUmQgUXHNHKlTZEkQ2Ky30HhN/AiDtHllK6OM+hEubqDo6k1LI6obVcuszuMGArVBfNGSpCQBqBlWIAs/xYxMxFyDZ8RuTOLnzOwAqQikXNw4RXIRXSCX2Ef2wcXUemybFYPVvdUaUwawqj2t9GqRZAAkkC2faAkQwXINzWSt5W5Lraqh+WK2rLbkBvdtDeAaiyQmCsE574Lj4Dk4Cp47x86xtyezc0yOyTM5h57BV+jxPnjnmd0OeKe8V9r/z7T9WXskx5IlCMqiqmbmC4BYSF7eLbeurJ6lZqpnnub/P83DPFTPN1WdWVl1F15uwYjA4u5mpouIzIOoORAkb3beymxjEIEAHA53M7WjIkeOHFGeFANCQAiIEYEHoB3Q3nBnuDeKAGzIAKGXsaqoiC5LfTjlDw/r+w/zx4/z+Wm5nPOaW3LlrmFpOpARAjMNIbwJh1tkAT61usvLVJa7VqqCAArgstYlt1JkXdb5MktexyiozRsDCHkMFHZxRAzSLrnEgEwqUM71stTDrkzC0VI0MVbYxfDbX44QxxLS+yE94qI2H4fdw2KBQgyhlgxl0bq+evvZdDxYPmNrASCfL4tYjGm3m1KMQwzHaRimKY37YTrc6SJr/vjuh/r4tFwyGjDoEPnuZvoP//7L/UinRV4dxiElAwLywMV6VUpEcl2y/O53f/j//qf/9M2f/lBqGW4+O+6Pv/31r3/xZhQpu5u7YX/HIRFzSHEYRg65ibokZ7PmQCBCCkQASFOI4253+6pIXZfLfD5dlstSl/VDqbPoU7Hbsd6OdUol5iXGMy9PPN17ZETTLY03xoNxAAhuOAP2rDaCl4TRy5oGXh/xU0Lp0+DHXtzRPzk+ZcgRsA8lQDDoXUS60cjX57uyS1ckfA78n0Fpw0bbJNfm7PrVjww2PMFnth431v3HAAQv3qX10T1bPuQVKqVraOKjJYlcHQfEwJumkokVKHStqQGqWhUNpbZYW6zbUWotBQA6Nr+gWWArIEK3r+kAJNrfKREFDhYNADYSn3vVesvCXHRAzwFlB8yXlwagD5P16ewUtnpdCinFNMQ0xCGFIdAYeQw8RRoDB6YYODnyMENgZVJ20QB5dd0/hhD3ygehg+Cx0U4wIkRCBgNTC9qiCGlDUVUQAM+HFRWsNa1VlqXdPyzfvjt9/+70/bvT/cdLWYuUCk32E+cKudpSlNCGEHDcxd2tQb8gB23QVl5PQ1mzWDUQo91BnYQ7PZ4+frxfzkBtybkKyn6MQ4zHMTGmCFjykFKcDtPtzT4kutRlXuddmOpEQhECkTIqEqu0bE0+/+LzucwPaxEMqhxCGMdBy2o15/Pj57/67evPPn/4LmvJifh2CqPqMNDd7XBzONwd91989urXX37+9he/4OH1oa4BT49Lia1pq60WMA2Bdrvd4Ve/3O2Pa5ZxOkxDejF+xR0ezLSqruv58r/9l//yj//wX3Ke1SCEuN8ffvub3766HcSU0kRxpBAAYVnXKurq9g0LeuJxLQwhoCEjxwBjGPY8HNJ+Xc6X89P5fLp8f15/mOs+2mGwm0GPQz6mvEvnIX3gNPF4pOlI4w2ONzgecThQ2lscr1bf3nX8HPpcb9drLPSps8L2+fMXfhSS/LkH2vW/F0izgQv9tBT3EjquKerLTOIlEfoC4WhDny2gcd1uFwRtXElnTl68yM1rcfvNaqAIHXfIoyEQ6n1im9FIb6dgYEZm93IFZmADChstDWIgoqG2doWhWFpMNZYKcO3g27QIfjo+2SSAAlLiCDEhVIIauMZQSig11BJzqCFUaZvJ9QtFKSEycwo8hJBCiMxML7MqoMDILjEmCEyR0xCHMQ5jGsY0TmmMYRdpF2kgnAgTgY8qDYgRMRAKkxA3osbUiEKI/qtCTCGkMeuw6LhqEAWzZqpiBOadTeozPhtAQwAoInmVVdplLedLOZ/z6VLuH+Yffji/e39+//58Oi2RIDIGhGaSLhWfSgvl5mY8HpOON/HVW2vSSq05n8t6KfPSWjGEEBJHQJQmrWktlRmHIUkb2yWvTQOiNSqLJpaBmxoc99ObIe2OhzgOQPI0L/d8AogKTAMNgQMQSMttWS8XXcvtF7+5Gw7f4+GUqUhMPAxjKhfKdT19/B7k71+/fbs8fbw8fQxp+O3nx8NxvDlO0/7muN+/Pu7/+m/+av/F39jxy2o8vap7tvX7//V353SqxdzFBQjDqBjuPjtQN1tt1ppZsxfCZWIFKafHD//4D//562++6ilF4JD4cNgNuxFCMI6ADAallvvHx9qUQzSRJtXjH0Qj4q7NYyJEnxgCYOg13Wm3P97c3F3mXi6Yy5ofSrlfa0S5Gex2hONo+2Edl5oupxh/4DTgeKDxBscjDUdMe4gThAEpGsVr+6g9/39Vlb1Elx/zR5/ywT8KgvCTh3ya+P1sSes5ANuEk55iPQdFWzIG28frX5/W936UTV7fx9YTa1v55+XLeFYRqJn1YaqgjNYdPPug5uuMDyZmcnEXMRA/gxGbUfDKgAI6geNxUK3Vzc9qa02kqTKDITOjOQkS2N+vT0YHN2ENkaJxBWsEjanFVmvIIZQac6ixxlbFvXtwEy4SYmQaYhhCHGMYYxxjDNeuTeiDuyGQBbZAFtkCQQrDmIYxpiGmMcYUhoATwUj944AQCRNhREyEkagRVaJKNIstamakaiXXZWlNlrHYWG2qMDUYFRgsAkQEARDQXHQ1yUXWi6zS1lqXWi9rOc/5dM6nc74s5TLn0ylfLnleRZGqWa1qIosApBL2sMfdcfeGb1/D4WalsEpd8no+P65lRWaM4zCSGYi0VrM2NdFWS2tVwYCocaxpX1WL0IjD58PuV4fxOEQaAoxDpvjUsNQFqRWyS7lEexXjro6jUsN2ulzqx4cP6/uHuzC8ETrC7X99WFrB2wA3gWPg1vL8+PH88f3Nzc3p7tW7P/2OiBLgQDTX3fc/LPChvbm13/wvf7v/4u8gTiFGWe+DHv6n/8e/u+zC4ffvfji1X392vNsPBCjAqqYABABIQABiYNLvKjMCu1yWf/zdD7/7/X87nR5jStO0Px6Pu904pgjEGNxymC6PDz/88O7dh3vl2zSOiCaLgoJrSAOHEAOageh2a6HpJlRGROQw7Y8xTfvdzd26zstyXtZlbaU8tbLOeip2k/Rm1GmQMZVYcsgrz0807Hg4sCPRsIM4mRNGGAyDJ0ovehiuN/aVBPoELv6Zw/65f/7Yz/Bnn+4FDm1fueLRCzZj+/tlTPQSDfHT578i19ZW8FNKvTddCJoQmKEF2lIenxocvSkpUAwcYhcZEAGRIQmgGLrtRFWook20Ngml1JRqrbUDUGsioiK0Ue1MEBliIJ8kB5v1EAAIkEBUIg3RooKYNL1qAVqTJgpm28tAYmSmFHiMcYpxF+MuxV1MRKhbC4moNDWNpIE1skaWxJY4eciUOETiRI4+E4J/HMmdj5TVghmaLaK5SBbNzdamTbEKFMHcoDSbBHaCO6AJaEAk8IZ1BbAmslZZUVbQ1WTO5bLkeVnnJc9LWXPLpZUqpciaW6mmSMCBmYcUp3F8/erVL3/5i198+YvPv/ji5vZ2GAckWrStapU47m/T8ZX76wBAXi7LfKq1rmu5XNbzeV5LbWIUQ9rf0HAkDJGngaffHuLfH2GYuE7DwsP7HLHwYSzDkOXjw1rzAC0FbYkxBFb4/vy7P318XJ8ur/7wVUrHv7o9/PoXX37/fX2Yy1JzCtgIZDm9/+b3b37zt3F/fMwiZf32vOwf7f/yv/yHf/er3969er3f7cL4al0bycKy0On7tHyY7qb/5//1t3//t58vDb748tev7nbqM+muGUWvSQAAed8TgYHR+x8e/tP/5z9/fP+emffHm7dffPn2i7c3d3cQI8WERLXm+x+++/a77989LQ+8d37GHToUxG2sEH2smPVf9kJW7DmGISBHCmkYdnGqaVrH3brO8/k8n8/zx/PyuMp71H20wyA3QzuO5ZCWKSHHJHHitKNhT8OePFkbjhAmo2QUDNmA+vL/s/iAP/nOn8GcTxinT6njn7n9P/3hawi1ZWfPv3hjjLYA6McNTR4wARBebVl/+h62eAsRwNC9OcCVP92bkKwPzEBkJEY2okAbCcsUI8XoZTtANCQFNINmVsWqaGmSa1tKXXIJpZRSUqq11daDIGmmwZthCYEJAmPgrUOtXwTvbTUgwwCkwAZB0dS6rbV0m31AY34eZs8RE/MYwhjCFIJ/AuBeR+aukAogkTVySywptIE1hRCJInJAYADUoDqYRFVUFZXSTEyDGXv/u1ppmqvmqmuVpWgRzApFMAtUQUFsQBnpghgQ4dkjQIq2tbWl1bW1pbZ5yf3PnHNptamowSZdHcbdIaVpHHa73fF4uLu9e/v2zdvPPjve3Ox2EyGItFbWWosZ8LAb08BMoCLSal5FoTbNpV3mdV5zMxh3+zCMHFITaA1Bg1Qoa1sCPe3S4TjhuDtf9H7Wp2x2EdB2KPY6WcIyhKIkYdgRDouGk1hReTh9/HxvX96FX93u3oXD17N9ncdH0DUflZhkGVhvjvtxP344rY+XNWX62wqff/bZ3/3ql7va6HIGBrw55B++G775r+P8PX85Ta+Pb9/eKHLY3fAQVNtGVYrL2Py+6Im1EZJB0cvT+f7D6e7ui8PhZrffv/78169f3dzc3EHgJm1+fLz/+P67dz98mOsFksZEAF5iRYKAjIZm6oXRnhogABh1wwIzFfMwutvBEHJMu5DGaX9zPCzzMs+X8zyf1/myfljzu0uJqHcT3I54M8J+yLtYx3hOgSgmHByMjjgcaTzicMS0x7Q3DAYEeKWj7dP06dPCk233/6f51qcwtH3qQGE/qV39+eMFWX1FI70+4SeUkH+tF34QvHHrRQ6HnyAUbgwRGrrxDQExYABsSAFIgBXVMLiDJigRhe6fR4EpEDEZkm2AoYAA2BSqaGmaq6ylrbnOuQb3n5bmsOHHVcp3felOAdrVPZwAGIAVA2MESkYJaETGLvD1WcdiqoBA25hvCoQBY+TRYYh5DDwyI/rMcFBEQzRCSaFGWgNmhpWxEhiq1/21qaG43WDVPkVEVIOZmAXVJmpNz0UvVedmS7NFoAi4mLuIVQElLEiBkBFAVVqV1lqttdVcPflqa2lrafOSl6W42BwA+/hxJuYwjMNuNx0Oh1ev7t68ef3q1eu7u7vj8TiOiZnBpNVSazVTZk7DkIYhxgHBai15XTNkUagCVQnCkKaYdrjb72+Ph2kca8F50fOlPjyeAS8Ww32t3373+O6+vXs0oWmYDq3UtuYvjmH/+a6m0YJFLByP1aLyQHGEeDmX5a0NR97dyMf0Zth98eVRw/2rL4pYQ1vn969ud3EafvPb31x+983TKqL69PAey/mO8k0+LU00Njry/M1/p3/4z8NyD+Nv8c0tppFCwhANwG0S0MUjJmbmTU6AAOZlT0OrI9uXr29udv9B2onJxsMXr252r4+7Wtbl8vju26+/fffDfYE63sJ0CCEAkqmYCiE4u2Bm23DnF7zuVWiv6FvSNrTBwEXExIiUdhSGYdzvjrfrOi+X07LMa1nLSepyaferHqLejHAYYYowxBxzZT5xeE8caTjQeKTxhsYjpgOkHcYRwmCcAGmDoU+Tsk/rWT8DQM/Ys7E91ykPWw8pbO3sP+a7f+b4UVa41be22OqTHg7HHYejlzAEGxTBy0c/VxcQ6RmA3KnGDN08qxGGiBwo8DZ3EQTUDETdjAwVIJeaa8u1ldpKbbm2XGrYMOfasPICgF5sZn6lXfLLAN4UGIkGowFoMBqBJiAiULeiYPNYCQkxIkbqxadEwTGIaWAemROhmjWzBl1eJACZQM2CWkNkX9egimIggOLmR01VHRpqgyYkbimmKGpii9gqkBWLYTUsYkWsKTQDUSgK1aqJSK21llZrKTXXmktdS61Nm6ioiWhr6mGdiHVCC2gcpjdvXn/22du3n7198+b169evbm5vj8ebw+GQUip5yetc1hkCxxiYOaYYY3IDdi8/aveXg6qInG5uD/tpuDvsbg7728NuiMPH++XDw/kcH7+YlJuZnB/O8+MJ3j/okmnaR83t47vL+bTe/P3n4fbL3Rdf7l8dH8/nP/7hj3/449frUqZpp2V+Wk5l4Po6/rf0jg9fxLvf/Pb1v/+b6aCIeX06ffNfwv71Y4GLxfv6/9bwDsGePnz7wzf/9PFN4OMRxBTO5X4pP3xT3r8/P37YaTn+x78ffr2HMAEGAANrzzefL2NDNPcjVzBVsBj13//14bPDX9e1mi5ggjSlcQwsH/70T1/94Q8/LO3MOzjcYRi8dIAURAy6J4EZIIfAISBRnxu5/Vbsezu4iZdqM5WuCbhuonEIcaJQQ8wx7dKwjJflcp4v81pKya2uoudm+2zHQY8j7pKlaKEps3CtvF4oPvCwo+GAo8dEO4yTcQKOhmzI+NIq/wUcXG+lLXb7+cOuj+nFLXt2O4SrdtlB6/lhL4KmH5XpPnkF+BK5zQB6tuxV+Gf0ea7tX5HRzBRag9ZMBJpAV98gACMFZjcGJOSAzMgBfeg0GQqo+bzP7gK05pxL8RHP1z6y0AtWL4aybzDUqXDooijyPNiJLDctcI46AA6AO6A9hgBIbMzA0EtUFBASQSKJ2CLUAMAQABgB0cRaFfOKn58pBkBAMQgAPveXDVi1gZg1NWkgYlKbQG1Wm5UmpVltII5RCmoq1gCFWCkokRKr++ujkQGjSa21lLyuyzLnnFWsidYqpdTSvKWrnxMRU7UQ481hf3N7e3t3+/r169dvXr998+b1m9evX9/dvbobx7FPAQDQ1q71RGL3QXTHeF9VvTwoBgoU0nhzy4R4e5g+u9u/muIQeGBSUdgZ1vYq6gBDojdPl3KYloGXVs6HIRDL0+keaokp7l69vvnl3x5/8VdAuNx/9fDwx8v99zevPgs0zk/6kHPOlXNTtGYppOPw6vPheKOgPI9F6rg/jhBod/f1uweK43y5XB7e/W//9HtR/dWvfzUOw/542O8n2N3CtNf7D+PHs331zoaJ/ur2Re3Fl+6LSvo2Ht5VGURws+eJUlnRGqlIU7ucfnj/cP/u/uF+bjUOODAzujc6mzIIgTGCmtZWCZECAaK3s2yjuw3QtO/m3uEA6ATE1vxp4A8BQEUiTiPHYdztpv26Py7LvM6X5XJZlnl9OOeIOkU7DHAY4DjoPsE+tjEY80o883qh9ERpx8O+B0fDvgdEnByJANmzhRdp1kuk+eSvTwHoxeP9xttoGdh0Oi7twX8Ohn7mwJ9nmF6GbM+hkkJvnHRJEYCZiknb5qkKNAFVNCMDAEIMQLC1s/WmdTcO8nKziFaxKtbE1lLWUnKpHgc5ZRw2OzjtWCR6ZXPs2YmCCJk8Y9WNBFRDAEaIgAPSBHwATkRXw1tGYkRjaAyVTNAqWIVNJ+RvewPn/ns2w49mWsQy2gq2ghUwARHzPyqmWpuU1nJtuZa1oKiPsKOu9CJl9inXRsGY3ZIU0cgMCEXMUIETRwgaMrTWylrbvNRSqpr2s4q0P0yHw/Hu1au3n33+9u3bN2/fvP3s7au7u/1hn1KKMYYYmdivoqoaKhOHEGEYEYG9Wtld7p08FTNVACAept0RYCR6s0+vx3i7i4kQwbKVFls8YMDDNNyNKb3/+Hizmz+7Lf/uN5pX+f0fPsr5FN4eaf/mt3/zq9s3v3g4T5e5nZ52IsPd7e7t27sf3n8ouZTWUCqVQnFsMgHtMI08DmTKInT4LO73+zQMu5svf/nrh8fTmvNS9NvvPrYm3z+cwzi9ffP6N19+/nbc8zgCoSrp+0d7c8K/5mskD1vbXs8g+t1yvQ2RADlZIo6kdWlLLcv56fvvv//mh4fvT6vtXwEh57IbCjCqttYyKNMmSDETMVATNWEIzMGITUWliNtnI3jm1dkMYuq5gvVcwpwsRx8mDMhhGEMM0266vTus83q5rMu8LEsua/6wtg+zjhGOAxwH3EfcRR1DHnhNDCEESs/sNaY9DXscDjTsIYyGbBigO053p/EXCmbs9aUXsHONlJ5J6itM2DXDeukFiJtg6ZP86xOE+TEtRT+t4f3sY7vRshlubseq2yTVVq1Vk4YqhOYsEXkjjnHPEro1sYGpiUhttcpapVQtTc/zuiw9GHL6WUQCXMe2qkoHIHF0si7V6vs6qj2LJcy0VyLQrS4JGfoIpxcd8YhKYKSKKqgNrYAC6HVoMqvmrbVRPBEzUIMVbEXLABmsANStXVdM3ZbNmmkFaEjKCWMv1AEodGrJYBvfaKSCor51MDEHDgbRIBomhSJWQGbRJgpqgIiR3c+WmPnu7tXnX/zi8y++ePPm7c3N8XA8HA/Hm9vbm5tjCJEoILGZqYipIAqAmQ1IyMyuGXVRQqetrJqI1RpqPYgMiBOHQxxuY9gBhJKbVoOmWnZcp33o+iyAVze7Nze7JiYKl6Xu9+OvfnOZ8Sjp1S9+/RnS4b/+d3r3Hg5TeP3qN3/3d68i4/c/fDydluOAY2iqzeit6s40+JA1RGTmEAIxh8AwDPv9wVSfHh+rGDQ9/fDxmz989bDWz3/xi//7f/g//7/+4/9pUFWwevNKpNhawRB8Dl2PhmjT1272D86VAHY/b0MKEKItT48P77/9wx+/+u/vTh8yynh7M95c5uXp3btf/jpMt5HRWr4Ybo0x6LuZG8yJSPGMDJHMCMVd4+iZT3FnP2cWu9rYtvEwrrD128cl18wcJo5pHPeHKee8zOuy5JqrtPZY5dJsYNtF3AfbBR2DjUFSvsSwRL7nECjtcNhT2uOwd94a0w7SDsOIHJ08v2KH/aT4bi+sjp+Tpp8BCXyZVL34Z0e1T7DlxwD08niRptmPUclPEV7jBHOLz2atSs1Ss9astZgKb57OTMDO1fQWEt+RDFWsNam15JpzW0pbSzvPeb7My7LmUmqtImJm4Zp/qVeyRKX1OMgnpnuwE4gAFBWADG0j4TYSHYideQYPhXFzcEdEAuXWyAraam2VKiJsiipkiiqgV0jqPvtiVgAKWENyqaESGSJiCL2YiEqiJEakJEqkpgAgalWlNCnNySGyLpEKSEwcQggxDDENolSrmTURba36yHYAYCIMwTk1U3XZ1Lqu8+Wy3+1208RI0zjeHI77/dE2ww8AIPKZjtg9solDoqvOHExlubT1XOZzWRbKdSoSFUeKk8IeIFqWUBtljEAJMSJz7OyTmZmlFAEpcgxp3IkN+90Xyyw0UNyVYfr9B/rjn6Aq/e1fx1+83Y9Bqenb2/3/7e9//Vn67Fc3r2C8m+m28T5xMjQiNPdrCYGxGxu9fX3zas+3qe7e7HchgGpUGPWUSEjXlDC+fWOo6+G15A82po1S6IvgubVSFQGRXpK1iogmLS+Xx3fff/31t7/747v/9s39h7k1TofYpBURuczlsko6WHBjPLwa7Wq3nueAROqLhwyRiNk0GShQ3x+3TMY5IkQDAga4rnLnP2XrRkBAr+MkTsJpiGNNYx6WdZ7XeV5qrqu0UmSpemabGPYR9xHGaEPQFCyyhXrmdWF+oBAojh2Jhj3GPaQdxBHChJ02ok8g5hlzrjD0XFf7JFrBfwaG/DP8GeR6EWv9TJL2c+0j2H/oBQB5NtWK1Nzy0kqWulpr2xAS5J4lYQgcfPy2zyMnNFVprZaS17Ks5bKWy5zneV7XteRSa+sYBADXiyO9OFRaZQlkEtCEUF0i5OUNkG3AWL/m2MU/zBaCURBCQ1TChoCAjWRBXUlWq4uU1V2+VEAFREBFtWkPUhCQvIFQiIVYiZUZKPjEgGfhFYCpKDafMmsGKlKarLWttc65LqV26OGIIYYYU0wxIgAhFBXJy1LWteS15FxLkdZUBTa6xvNUPxcxxGEY9rudthaZxiGmGBGpt/IAAIC0WmuVVkWqqhgYMYUQUZvWrHWVspblXJbzejmdny622qg80BCDTYmnUU2rQoPR5ViEAX2KfB/WjgYcKUw87DntGUMaj229l1bWYv/4rnz1daMYf/1F+Pu/pxSsLISL/vLz29/c/k+f7zHy7nGN5cKr0p7Y3HLJDBEjByLf9uTt69tfvh6XzyYNY2m05mbERu12H48TjRPFLz6rh0OhSU8ZhmAemHRG89pws+1M1jsATA1MVGpdLvP9+6+/+v0//f7bf/jq/nf3rShNkw3TqnlJREOMgYMH8x6GArK04iUZRCIKSKQqTRsakAc8bg7n0XG/NxHBtrHX29pCJIpGhiKK8lx32VRNiMRpCCmNu924y9Mh75d1mdd5XpbL+jCvpBrJBrIp2j7iPsFhwH2SxBKYQqjMTKVQXig8UhgoTc9hUZgwDBAG4AQUwFU2n4BIB4brX9bZ6E9h6MdhEv7o+3/u+An/9OcfusVW0LuyFM3v1mZSta4tL1KLn9FrqMFEKcUUo8XQg2xEMBSxVjWX5s7ll3ld1pxzLrW21kTVAIJfBwcgaa3VUgvXQBLZJIAlAg1okbFvagaABEYQY+QUw8CcMCThVCnq5qPk3f9oUAFWsMVktbZoXTWLNoTN5hUQkI3B29NsG8CoxEbBiIEDEBMygCEYQddGKWhTs6YNRLSWWpZSL2s5r+U057W2kFJMKSaLiMhkUhVNTEyYkMq6rsu8zOuacynFqfpWq4hs42qLiRDCGOl2P765O7y+O9wep900phjBrJQsXVtea9dViUitJSNIYtoNkVG1rFKWy/l8f3//4eP9ux8+trne0vDleDjsjzqqIOiA5kZmTvurWkPsYwgMzBCB4kDjDcUJiE2KyQWtkOWS47fv6Jt3dHid37yap8M5l8UgB6l/9fnr4/iL/ThYU/324+P56bFiLymKIqipMAEAtFak5bu7m//wP//1rw7l8fT0cdY/vTs9PuZS7bCbvnx7nPYJpmjTCJccnipqNWmwJT7dTd1XL+F2+yiiAZCILg/vH777+rs//vH/97v3//Td5ZuHujRQrdrqRHIc6fMvPvu73/zt7vZNJb6/XGLaxWEX4rAu51oWk0aBO9YBuYNwM0FEwtCrzR6F9Q+dclFXoKvC9gAAc3Ob3sQIql3j2OMiBAhp5Binabq5qcu8LsuyXNZlyeuyPi3l41wT6z7CLuEhwT7ZPukuwhQhMnqbAoVIYaB4pjhS3NGww7SnOEEYLSSjaBSA2JAMt57wZ3z4cYjyY9j5eQT58yW3nxyfxmI//t51PBWaQTcLUzRBFTQha6rtKn/wVUtEImrq5AwxBQAyAxWrTUqp61rmZZ2XdV1zLv2e8X2ix0GmoiKt1VZDLdQCyxBVBcCIKMQUx9H3Y4DnNtaRY6LIHICCUCjEhNc3ZmBqas0wG2ajYkHMwAhNQM3Mi09iKmbgFwMA/RNEdn9y7KtZHHoYIRBExgYADQQVrOVSLutyXsplLXOpubYm6qGUCYM2UDKxXItsnNe65FyKk2KqVkqp1eVBVVoj0N0QIscxhc9e7d++2r+52786DLsErGtbHi9WkEJtrda6rOvpdFqXueRFakaQ3RBvdmO6PZjJ+Xy6f3p69/Hxh/cfv3/38fHhMmHA483xoHtQxjbtSJg1GLABgLt1o6J3rYCPDGACANMiVc3UJFubVdulDh+W458+7u4fy69+efniTcOgUCSh7BK8nnb76RA5SS0c7yGo5frw8KECHp7Obw67UstSC49TCEho0373q1//+tWojx+/vzlXgXC+ZAA9Hsb9FFNERTYQLgvR1QCUfPO4Sk4MscvkCEBNWmvreX58//Hbr7/96k+/+/23//in8x8/1sdCxBRQSVteoS5n1ps3xxQnmhUfAHb7m8Pt22k6PD19OD19XC9PxNwjLEJTUFOVCgaI4quROTIHckYS0K6FOfMXA32GybX4fS36Inq41P8AIAU0RlRAng6cxrQ/7Mpa5nld57yuuWavYLSPs05BDwPuE+wjTtHGqAPXxGvkE4WIYaAwYJwoTRQ7c0TDjtJkFI0DIPv443579Z7Mn0DPJww2/gQ88JNPfiZdAwC4Tpb+34uZDHoerGifApCKU7ugptvEHVewq7guUJoMbVDVtOaaS8255lwXxyDPw3Kttbke8YpBbnMvrbVSChJyCEObmqEhYxjCMKXdAWGz8dkqz4k4EgdiAKg+SkitGw50yyEU4GKhGYixWUQQ0i5j9Bds6rPiCDYVvENSX9NmAILWnQACIZrWIpfLcj5dzpf5fFku8zIveS0tNylNmqgBsFCroCJ5XQHAi31NpG3OkqbmtDMCXl0DaqlgmgKNKdzshze3068/P37xenizsz0vQQhyK5bnOVzW8vR0enp6enh6PJ9OUrNKHQPe7YdwczDZG5zWWj/eP3z/8fzd/eXp8XJ5Wi6XxZCXwHi3G+/C9CrhEcqQIQIS8rZ1bHUQo+0eB2vaZjMFbaYVrKnRUsLTTDp/syvlbz7bv7k5EIQBcWLYDUAsqrUZiLYaEKJBm//pv/5n492b27f/8W/++nGdLybx7ubV3e1uGoljePVljGE6HIbTYjwSYvxTOOxGAIvYqkGAwrFyQogRKQB6NbLvFuiohJumTFvL6+X+3fuv/ulPf/zmv//p4z9+t371oT5cGhIZBGZAQlOpOZd5nh8fQm0rRoRwON5++cu/Oh5u33/YEWFdF6+3fNKgAQigpq3X38Gc3iEKCGiGquIpnCqAM+ba01vtpZ/tju0361XS528iEBKwa/wjhxTSME4lL3ldOhJpa8XkIeul6kA2RdglmCKMAYcAiWukFvhCiMgR40RxwrSjXlPbYdpRmihNECIAGVIHgK0zticL177WZyT6MQP9qfQIP4Ghn2LO1aYDf/yT23N6xck/KoH5WB31j6Ciqk1b06bmAY10DJEqUlurTU7zepqX07ye5vW0rOc5X9a85NohSERti4PUXIwnpTYfSMAhliaiYMQYE49Tmg6dqd26cwQgq1VpBA0BXvr6A1pfioSKXvBiNTVPLFVABVUoSBfXAphbDnnwpLrZlwD0kpMWcx7ACasyz+vlsnh0N685lyqbvlLUzKzWioA+BqptUnDV7g7L7M4iTKIAUGttpdZaCG0/hdc349vb3ed3uy9e71+/Pt7cpN1epZ3PD8sqOBe9rO3pcnl8fHx8eLwsF1DdDfE48jQQYUSekQ9Ad1Ksnks+l+VxmU8r1HY3xc/vDn/z5dtf/fLN68/vQmJDqShExBgAqE9v8GmrPcEAAFVZQBTMNmoEQSk3Ri2/ff30mzf0N78ceIdkEqFNoIHMoEHNRlJEBSyyoq7ff//ustiH3Ts+P31Y5nCz/5L+6vWrW2YGMIg7nO4YaT/WX4Rj2L89/uL8eqxvX/EA2ZqwtRSVbvZwOABHJAbsV7rfyuj2dqaS18fvTx++/fj9n77+03dffff41cf8/twuWUvTGIDBYozH/RBZVTUvy9P9h7CuhUc4vCXi4/Hu7uYWyWpZnx7eqxTYGPotXumA5PFskBajWnItYwT1WajOFEGv8pOBoQ/U8yfBF/cz9hhkK/72N8TESBRC0jjIMLZxKuNa1iWv8zovuaylSasiWWQRvVQbA0zRdhE3JHL3u0blQjgj3RMHTDsaHIx2PHQkcvk1hcGI7SXNvO3Gm0pow4kfJ23P/3ymsF9+x57RZ/sEN/1j/zlnADZq39eheQribAgQGKMqCgEhYKeSrQB4KFNFapPS5LTk02U9z8tpXs/zel7yeSmltFxb6WzQi1xMVFsTpGaAasAxuaR6LW1Z8+k8EwV/8x7oXllxuhqSbW5AsHnJuh+UaZ8IZ6AAgMjgZT3j63w0V2ijmoiiKLmFX08PW6ut1Y2kyTkveZmXZc29zb/32qp63t87RVRVxWMiVfWSOxJspLYZEBmJEishBJBhxLQfpkR3h8HR55dvDtM4VMDzvHw457XZXPSy1tO8zmspVTqPVAuaxEYqOMbdMfBNCntOsXFemy0aq70K/PrVnngfEr19c/j8i7vbVxPt1FAVDAwTJdxQtwtmfW5Kr3R7kNmZjq3oQzfTOr6pR3ia7o43RygRSXOAlqwZgolCaZam2kxNp2DW1o8f779790BA3/zpj9n013/3t3e/+DylgZlbqw2Chr3uhnSgtzdfvv61/Q2EePnjUL6Pcq9NESANwK9ucX+DMW22l9TVib0SharS1svTD1/98NU/ffPVn373vnx9396d8ZStbVsNgcVANzf7AAIgNefz/T1dLjnux+FGzYZx2u32IfC6zu/f/el8ukfAq4LWTK81MFXbWq09xLaYGlEgJET24c6exKEBGAEaaAMFNbFtTfhtStd5X/2mx40jMjBEpsghDsNuLyXXsuZlWZd5+7jmc64MGkkHtingLuEu4j7BFCkQBDImIwSURu1MeUF+pBA5jTTsedhR2mHsSIQhASfkABR6tvuijHZ9cc/FwCv0/LnjGY9+BoauIVZ/+mvlyZEHzQj4Gg4RCgFjhyECEDBVrbWKWhPNtYVcz0s+XdbTvJzn9TTn05znNbs8ulS/N69xkJqIVmwGIKKtNURMKTFHRFKFWnVZSvduvs694dAHz3aFaleI4caYU1cIYRcH9uqZywg2whUUEEAVTAEVvdfETFozk6aaa81rzuvqMOR9JqKCXbING8qpp1ob49Ohx7bgCAB6M7ebq6EFMiYlMkJIAafEhzEcd+lmPxz3I4X4cRZcc5ayNliKrFVzs1y1VClNrslbRDuM4bPj+MXd9Pnt9OZmfzPuJh6i0mD0KsZhGt4w8Mh4jOGQDnfT8TgMI6MpKrGxTx4jRNyoALx2H5ib3npkdC0noxkj4pSEQfl1GI4BrYRyZp25ZC1rhkCK0BBRl1JQyn6kEGg/AOiCGJrwkMLb2+n1zX5IybZAVLtM1ACBU4pxF2AXKYYMFomQQmAaCYc9Uuxu6s/ZF6jWOj8uj+9OH77++g+///qbd9+/X3842ylDVahNRBQRUuRXN9MvPr/97M0OVdq6IoKKkgqHYNLOp/sPH753I5raCgdWbYS8aQAQEc0QibQWUd99zGUlrUmSGkJijni97tsuv73aaC4R6g58L7MXB6ArObTtW66DN0NUIIwDxRin3VgO9YpEnqOtOZ/mQiZTgCniFB2MaIo4RhyDMXrzrRA14ia1Uc4czxQHjiOmHaYJ44hhgJCA3eg9ID13hFyL8S9e9E/L8/azn35ybMySXR/Xm0I8xPSBNEheZY4JTQgEzRSqWhNVn6eBBiqi3n9UqltknJdy6uizXpZ1Xtc5VxFtos3poE/iIBEAUFUiKgXNwO0/1KA1zbldLiszd/Tpk/82x7GXbmdXrsjFgP1Hri4iyK5hdJ9zNFcW9AtMSD0XBjW15lsPhEAwRiYg9Ci/teaeeltD0LYzekZnaiaq14Fxar1S0ecRAPUpzH6LSwx83Kf9NOynOI4xpiQU5ganKkWa+/b7OLBSuzwbwVKk/T4NPEyBbsbw+e309nZ6fbs7TLuJpmQpNgZT3A27mygj8SHGQxoOEU2wKYmyERkzXWnU/t6f0ee6wV0lCR2GbDszyBymca8AUi9RlkAo67rmsoixUcNApMu6BmiHfZoGvt1R4jaO8binu8P4m1+8fnN3E0JoJfcJnyaiTaWqKEVIQV1vGQJzQKBAnJATxB1S9M6IHg0Rg5lKW59+ePzun9798R//++/f//77+UOJuWERADCP1dEgRr67Gf/2t29vjsm0PT2wrRlNCSylARAv54cf3n1NHI7H28vl3KSJNAQjDsiI1oWRSGwIaoKozGSAtXm6nlMcYhwoBO9ppd5H8uJmIyRjQ8N+LzxDlNPqW/Ve/ZZ/JmWgz4xAVEKKyMQ8DMNuP65LXrz0s+Zaaqv1Ibf7RRLBPuEu0RRxijQGHAMm1sTAVInLVkqLFBLFC8WR4siDU0UDhgQhAj9zcD1O9hGIPycOspfJ2D8XHeGGO89iLnP3Crta0AEhIQeyyGCKwERIxSeFATbACtBKc12LiJoYGOAlVw9/zku+zPmy5LU0jxg87nnBB6n6R+yd+6BqzAGRVK3VtuZ8OZ+JP/nvecAIbZjEPUDyI4QeK/HmHMSBQmAXVDIhMwYmC4wIqv0seHbmlzgwYQrMEBsUNAQxJVVSZcdnp4+k4bUa7KBDiDF0a3931fY0EdFcE8Dd5J45cIq8G9M4xHEIKbIYzo44DcqGPrWZmXLAmOiwn6bEU+Ix0MS4T+FmSq9vdrfH6bCfpjgGCSSEA4WbNCWY9kzHqNK0VS0FmpABg4eR3OUw2/LGFxl+d3i5BpaI18f1zJ4AgfwsMBiAMaBQtMAmqi0XyKa15SVGCGm4udl/+cXdabnsxvHN7eHLz9/+5re/PB6P23rtIg1TMQAgIg4b1YzozikUkSJ4muCFS2QgMrO2XtrytDx+d//d77/76nf/9Lvvfv/t/O5kGqNij7V9n2Cmw5TubqZXd4fDPpoKga2PKrkQEsdgMZS6Pj68Qw7rujw93deamf0S+tuWbVycEiKYePM2UTBDUau1uj9KSIlDYmIjI+ihuQFs5DQhaLdstS0te279gH6XPLNPL3hi8EZNChyIA4fGMfpEzDGXdc15yaUURyJVuYgui4bVBtZdoingFHAImAJGtkiNOTMCcaQQMSQKA8eR0kjDSGnkNNEwUZyAA1IwDEi80dgeQiNcfRZfgs7PAtBze/yGq5tPvKmYVJAKUlEbSCMTAkVECgEJgZGZ4Vq5xl4YNRSt0FTEvDtMl7Usucy5LGue1zyvOXt3KGys8svavKrCi85ZkZZzZmYzrbUsy5xictzh7S8Pd3xaRQ95OhZ19Nnu8v5NH3YRYgjs46kdhigEukZG3pbGjGBG6HjLzKAM7u0B6Joh4lIxOyEmlR3OwrCdXQS3kUQmdATsaT25yz+H6xSSQCEwAIjaXOFxlSpWGtQGRUAUmSwwjBOFwCnymHg3hJvdcDOmfQr7FPYx7Md03E9DShwCYUAjRMbAnAhJhKSV1Z2oSTRQiBS3NrLNCscBCLYb3oEYwc8J9rELvRECtq3CABHIdc/ur2LIOOxAM5WztJOKKhCqxekGOH722atzXjniyPjl569/+ctfj7/4Eqedmt/YqKKiYj69gphDcmEvelknDEARORolpNj7AomMyFory9Pl/Venb//h+2/++Ps/fv+//rfHbx5gkfTqFcQABtaarz9gwt0UX91MN8fdbjeoVgTFVpZWiSkQ0Ti12i7nBzV4enrIy9M6P8YQNkav02IOQ0jIhGJqCiBqgN5q35rU2pJqTBpjYogeaCMQEXlX+jYBZquIbcrvZyURqXlRf6OqOwxt7V+9ZkDIMXKMcUhxrKnUIZc85bzmZcnrkmspKs07oFuzojoTjoxjwDHSECAFSIyBgVvjKoQrAiIxp4HSQHGk1MMiShMnr69NGJKLTcG6zshFE1e55j8XAF0BFQxA0cdVmHnhVVsxKdCKSSUTRvfMMAQlJA4h2lVX3GNzMaiq0FDNmkhnLarPWX02orhSbbZZi4SOlXaVDpgZtCalZACrra7LwiEwUQghhhjcFZ4derY5Qtu4NTfW6WN0OgyFELk/fEOlwB7ek39/wyMMHU1Qpak70YC3fQYDtyICDhgSUfbYCmOgcQgquuUrGwZtrwg3LScTcZ+9EZipNCmtlSqXpTWBplbFarMq4HFTYBwSBsYQMUYaxnB3GO8O091uuJmG4xD3KU6RUaFjsQsVnEH0kqb7VUozUDYYaGCmQIGQPi2u+sLfOJUNfZyv2N5St3XfvOmewQhtM8pFMgqICYOFGCiYIIAZBRrGgUI8HPe7aXh1M0aQz9/c3L15C4fbhiNAAERGU1NWAfbqIYeQCDY+DQk5IsUXfTkEAK1mqTlfHi8fv336/qt3X//pD396909f3f/++/V+jRjjXoC56z8AjBDHIeyndHOcYoxpGFSjmWqtVlsIY0gx7HZtzU3aup5tuUgrJS9M+xiDddUCbN4N2k+dyzk6O+hrG0W1lOI9RykBAighoFKfocnI/bYDge2mMLjeWM5qbhoAA+/ivkaNz+HrMxghhZRCjONurLmWnHdeQVvzuuZ1Wdc1mzZuEFBngkQwBhojTpF2kVOASMBkTMhopCKyYF6RTkhEceBh4isMpYnSiHGkOGIcMCSk6AIFVx0YdAuya1r5c8dVFqWo0sUfUk2K1lVr1pbRNBAFIiDkHqf42MK+R3oKpJCrGFZVg9q0+wS5QYdcK0XX0OsZ08Pza3nxMkVaKWhmKiIxOk54P2togXsQ0bfBjSsFM2MOMcUQYtgiDd5CIk9//LMQ2LEsxhBieAFA3Y4WwRDVnbA95upTyzAgQUwcA9cY2hhbrdqaeTnfNR9O5F/ZKgREDMxE5HYAa9Oy1o5BTUrTJuY8JxGOySeCYGAMDNuc1fHmON4cxttpuJmGERHFUAEBKRC5Oo6YNjGPoikqmDICY0CAgMRIL1YvvvirAxB0zzlXjHfudWOiEbopy1aBulaPqTtmgr9VUB52DAXhqFLAgCiEcVRkgyqtkgkRpGGEuDeeiAYENpU+IZMCOBNgwCGRrOI0gaddV3REb6mzupzmhx8uP/z+9OHb+/fv/vD1wz98Nf/D1+W7+1YB92QiJgZgKmpgQAS7MeymdHvcMTNSJII46O6oIBbCwLsRU2rIc2kmtdbapACoqEQnYnqk6KUxUxUvHXave8LWiqlBb3lFAHAlGhOjqRkaKPThZxu9fb0eAFfHj84EeWrAAEoABtp7uf0+2mrlnxpoIABQSJEDD+NwOOzWNV+RKC/Zj9NaGGxgHAKOgabQpkhjpCnSGCgyur8NKSApoqGsUgryhThwiJSma2SEcaQ4YBwxRO9NIwpG7oENn3SivSSg4Xrjdv85NAFtINVasVa0Fq0ZTNyX3oiAu4kfAyC60zz7Dd8UcrVQBLGpWa3dHqh5F6p0d7KfomD46ZeukOQMIjiXyCwi3KRxde7nSgZtG7jrbppII860jXslok8AKGzQs40i7GAVMPqMsOi+X8gMgSEEVEUi8DGkRMRDJAJNQVr0d6nirV7e40imaNZNBES11lZKW5vz8c64msOzqHiY3SeREPpcst0Yjvvh5jDe7Ieb43hzMx6P0+E4kig3JVEUQAPqrfWRvQy8zfSzzWkXgPFaV3lx9LPVl2tHmJfhD16zAUQgz7epC0S8/N2ftt9C4OjlGEfEPAYLCJM1NzIZIA5e+CKwMYVpiMO0UxpEDDb9OxEDAwBtRJtxiKCrdQxCJALnHQxMmjZpZZ3v3z398NXHP/3T6eMP9/fnb96VP70vX31ojxfhQMOoVXRQ7183RIhM0xj3uzhNKQT2fuuAIyKiMRhRSoqWhqEC1ZLBmtSVmL0RbwNtj4EEEDuRAWCm2N0OSFoDAkD0JkrPndQMTcFQAdGk76Bbi6LX5RHBlMyBqLMkL/AJAXzY2dZudiWuP73MaP7I3riCI1FMabfb1VLX1ctna8655NJqPZX6uJTEOAaaYv8zRhoDDYGS8wkIiIpESEosUhuWyutMIVFMnEZKE6YRw0DxWteP3uoE1F1tPH/6RGIEL6BHG5iYdjLIarGatWRTAWYgNmJjUmLnERDR86AUOcWQq85ZeK0AqAq1aU/BZKsO/Rmo+XkM8t5NL5Z1olKEmQOrBja155+7jmZEAIDWEKluV8MA4BlyNs6IXyRqW4bWoScG9myPPQMKGCPFxCH0ONoLW4FBERCBmdCCK39sA6BctJRWW1try6Xl0nJu4ulo917rrm29IxSMCYfEt4fp9jje7Mebw3CzH24Ow81hTIm9YQSXlVRJDRUImTkGTsSBkRHdoqz/dx0PtTGZVyKhB2XYKYetJrz9AYCN4MMNfWiTXWgICQPTFXautMUWSm2MnrHMiIBhAAoAYJiMAhoy8+3t4WYXxnEcx2mudT3dI5dtIi0/r00kpv4WqKu9oK9jAG215lOZz8vjD6eP393/8N033747PZXLzB/OfCkkgEjoStFaWkuBQBGNCULgw344HCYKAbnXqpiHEAaE1GoTkZYXixg4QFBTEakOf00qEztfqaraGhK6/gKR1FSbBoYQSBogMyAzhzQkf2sAHYY67ptuOmkDoG71Y4BkoGq4WTVueqHrNdogp48OdHroGX62YiYguLcJESFpR8fAHEMahqmMee0BkfPWIm0WWVahtV3BaAw8BBoYE1NkCKSMsI0b9ipQoRAprBRnjgOlRHGkNGDwP/FTJOLn7Q6gEwag0DGogjaUQlrVYUiqtaLSrLERK7F47cl/c1fmOMsK2HUDqAZNtDSpsmVhm9XPX4BB/Wp5wcxMzUh74g2ChOpVfP/+tSLlD0B6cY9s8ZTUWnyiKiASdeTpHoOOQZ0a2gIlipFS4jRwqhwC+Qpmxhg70xM8h2MKRGZWiuYiRdraWm6tlJZzW3PLudUqGybaVrJXM2OC3ZQO+/H2MN4cxtvDeHecbg/DYZeYwJ0CSBuaoiiIMIXAkVMM5MmXMzv4XEPH59DGNtMpD2qeaeVO5thGIOBG+mxqlC0WWua5lCLSFDCNO2RguPYEAwI9L3jceGwAhIaG7qOCvs8BIzJSGIbh8zevtM4hjsRxntfZHjDUGIeUhpRG2AYhXMUWikZuwbZR5aYmrebLw/zh29P3v3t4/+379x+++n49zUNpNx/Oci4kPnTPtLldnjCjIQATDJF2Y7y9meIQOXAHUuYYR6SBcp7nS6tFIFCaQoxmKlK117xUFXzhuRYVnUTeYhJVbQAxUIysYNTtJEIIETbq2QzciNRpZi8pXStKW0RPhN1LQjeHBLsO8HNsNuhh2TM/9LzVb4ydmSsjGZmYglFQd6TlGEKMcUgpD8UNTktptXrYkNVqsUuVRDr2WZ42MEbGQBhYAipR67PpyUvOkUKkGCkOFBOngdLIaeTkYVFAjteA6Ln2YbZFQM2zMPQ/WlEbaVNtqM3V0EaiwoK97mSdEyZAREZDEsMqlpsspV3WsnrzZpOtJPrzUPNnMKgLa7ypGNBM+7lHRBTtq+BlBLQVK7ayW7/pTQWk0fWnAYCZJUQOrda6iYk4bKVyh6CUKEZOiWOiGLuPcIw0JAYIgcP2u+A6VrqaVG1ZZJVamptxKJAhAzTbqvYKqmPkaRz3U9rvhsNhvDlOt4fx9jhOiVOgQMB91I+PczMCYk4UOLDPcI1EjM/Qc41Jrlj0HPh8wufgFv50LPZ7HRCt03VICJjneTmf13WprS5LeTyd94dp2u3SOKVhSJGDR4xOgYeIFIgQuP9GAvA5yz0GI0NgAAZiJk5pUKwch2bYpNYiVRefmhRjwm4r4WwPEzPWJ85zaK2h6HKueS15yfNlfrw/3b//8O6Hd+9P338o3z2qNIgIQ8TDxE+FRVot7tnRWovQjT9pvxumadhNV/dbz1yRKMQhAGB7fFiltBEDRQopplHBWqvY+VBwZY9HRt4R5ikWABqYiCBuNz4Bx02c1ok6J3Q6N4pgG7e1gZJp74bhAGwoBH5tVMFdGN2TBLaAdttNvH8NNybpeVdC94XuKRsRYiQfZt1SizWloZY8lJJLLnldcy5OL4iqz08vamuTxDYGGgImRyKG4LNLUQiUsBKuhIgcKLrIKFFMlAZOgyMRp5HSyDHhRucBIoKBVtBiUkEKSCUpKuUKQ96tamAKBEo+fULFiZkQRD2WICIAEoOqlqsupV080dxaOOVnqaB/BoMcdzoM9RYlD3EIUYnEYQg2IvVaSNh2l+1K9EDArgAEW2SErddyDCyEEK+BEDMRp8gxcYwUYieJ0hCGxG3g2kKtYVMYUQiUkAGhqjRVAw0BEZlK/1UuaIxMKeAYeIq8S+GwG/aH8XicdodxHOMQKQVKhIGAEQjUpQJIgYGJmJEDh82ldqMzX8AvAODLWcDw8h3jC/QxRwifcS3SamlexhGRy+np8vS0Xp7Web4/54fT/PR0vj0Mx+N0OOzGaZymcRzHYZrSOKY0chqDAUcCtC6exS3u8pl8ruK0/k/mgDEZBIypNJBa8lzmLOzmUxyYExAp2CYqDQnW0ZYBBa1qXpenj8vp4/L0cX56fHg8ffvh/NUP69cf2yXDQHCbbIx43NG+8rpSKa2L1poSu/0wTGPaTWkcB+YAm30aghE5tR9VYT09qRGEcYgjBx4ACUvPnU1te3mIJFqv0bavcREFNHZvYwIEYELsfA0jmWvh3FHXzIicvCY0cis/1w0BKgJt/tSi6Ko9VfOmI8eva9jfo1gv7r/YhfsCuFYgDK7kGnJE4hDTMIytllJyKeNYSsnZja1yzgXNCHQlCKiJMTENAYdAQ6Ah+HJFJmNEt7VBqVQb4gqIREwpeYK2wdDAaQhp4Jg4JAoRiUAqSEEpINWkgBbUilbJRJ0qMt3EPG6tIwCMJNiEiCiwD2t4vCznpcxrXXKdc72s3SKobQ0Mf44R+vOcNMA2p8kr97gFwCSihCLPbLQR4jNd2O86L6xv1Ndzw4SJ6rVo3gkUVVNttXGf90wxUIg+jsKLaDwMIacwDJyGMAwxONwHTpF97JQ2BTAmICIL6IFMQB4DwsRD5CnyLobDEPdDHHfDsEvD5LNbQ2KMHv680OUgIgERMSF3ir0XAa8cDGz1ccDn3Osa/nRw6iHbFssTkpou69paFRUFuFzOp8fHy/m0zJfHx/PHx/nj0/z4dDnP+XYfT4d0cx4P+2m3G/e7ab+fdof9NE3DOMVxl4YSh5FDpBB9sAFRAAzYzdX97Pf8nyggB4MJeJBqKrLM58u8+gBl5hDjCIRdKkYExANWoGXAS22zlPn88H5+fD8/fTw9nd8/rt98LH/6KN8+GAAfE+4ZONgYYQgUmABQzVqTJj5hBQgxDWGakm85uHXbe9cSh0BmaRzb+x+sllZzUqUQOCZAbK2KtBdn2JULoL295FpXNxVDNMbYU9yXnTqA5PP5APpcKFVAcbaLiJ2A64IgbyRC96km7RCjWyOHs+x2lYC4EPaq2LeXmqLrculo1FeHkSEgB0TiGNM0TaV4SFRKLjmXkv0L9VwbIyTGxDgwpSsSMaZAiYkRuJdGgXzXV0MVzLkLiWPyielhGIKDUUwUAoESONZU1ApSUBtZU2sIQuhMvreUqRmq95UDKvjkUkJiBXg4r0+X9TTn81LmtSzZLYJ6H9Wfh6B/ti7mQGIb0dMRyGFIFcWNERym3Mu058AbDoFtatYfPzW44KKjnZKItKsxCCJKDEG4tVYCE1EIlIcwDCHnMAxxTSUGjomHFNoQVGMI5FZUXi9EIkYYGEEYzQLYLoYxhjHFKcXdmIYppTEOY0hDYCZUIAM0ZH8NsGEkeMtbj+evGPvMAm808DMm9aino48zCldgXi9LXpdSizRZS3l4fDqdnubT0+Vy/nguH57WD4+X+8dLbnbJuhvC/dz253p4qoddOeyW43S5OYw3+6f9bthN4+Bh0TCEmDgNHAdOA4fB2ynI+9opIMaeACIbuUvIaFDAbL7M87xyCO50a2aIZFu+oYBq2fQE5Qcsp5bP8/k0L/Oy1PcX+O6EXz/Ah7OtxaaEAFBVUARFVaBnK1s3omBvVQ4hjGNCn3sJdGWfwDxCtN3xOO2mElhNWlsDjsTdV1sEAL1ttdsMI5PJxlRuK019CGEnjrEbYmxRim0XiYj71qCq4GonzwpZFUyvEx0AAXXbbwGepeyOeyoGnqU878p9E+7i/0/KEgCAQAaKQIjaS7hEakRIPHho1KS1VnJx+/fiA3FqbbWda3taa2IcgodFNAQcmBJTCuSZGm+RARoSgfrE25KlVS1rWzjEGNIgvnIIiIzQCBShgTu/a0MQAlVvLN9SGgMEQ1UozaqaGAogIAng43nZMChf1rKsxW0S7Toz7C/lpK9Ycf1ct0AIxR1/gAjNiDzZ9j7HjvQ9mftnnvmZNjIgV6ySIaoIIqKqNOFe/Wdi5qGGWkOMYV1LCCElHoZQxygtmVgI5HwuB2LmFAgIMBKqkVlAHFNw9NmPw35KIYXe36+IPg0bkHvUQ9sqcoylLdp5BqAt+XoR+QECXpNWMNUmTTZH7lrLfD6v81LWtdZ6f5ofHi+PT0/n09N5Xp/m+uFU3j8t56VUsbXaadUYMIYaGXYD78dwsws3U7jdhdeHdNnH213c7dJuGva7cRhSHIYw7OK4i+Oe0xTSyHHiMFJMyIDM7r5syEADACJHAGlVnp5OudQQIxGlGDs/BWAI3oHeZK35Yzv9CfNjK+dlzXOFpdEPZ/z2RF8/2NNstdmUEBGqahABEdMOAACoaq0pI/isEmaapsQhEIfnIBIIoBdYp93+ePfqJJpRWyudlkIgJhL00Fx1K497HO55KAFIL2J1XbNfJZ8FtCHUhizYv6Xe8m0bAvH1Wm6GZ+ivbRte1n0cwb/aqz2mZtB3Zbpm4Qg/CYWeYQhAjQg722EEBEaKrKRKQVjEOz/c1qqHQ9Vdg0VaW1XXpri2tEVDA9MQKAUvomEkDIGCtyAbIQEBqAp5Ob6aWpO6GAGTAXdqEk1B1N11wHQr827Aa+Z29aXKWiU3LWJVoSmc5vXpsp7ndVnLmutamqhuBA3Ac2f+X4JBP0GOrciFAg3A1Trcr+i1we2FfuvnnuNTiVT/EQMAE9nGrplxYBZxQHB6VCTUetVm8zCEaYqttlZaLTFGZqYYGTFgcMEsIAAzJuIx8jSk/ZhiYAxUTGttRBQpdA0k9t4ytwLY4phr7fy68V3L6tfi5ouiLKKZlby2VlXEwET1fDot8znPl3VZznP+8DjfP84PT+fHp9N5bae1PV7qw7m6O2YWXYoZAJOXGIAJqlgRzU2WXOeVlzWfz+FpCrspHnfDcT9M+2EahzisnOaQzmkY4zDGOIU4hDhQGCgOFMYu4DZBYgOq5zk/vm+P782wua9oStoq+lIFL00rtjVfnpaP91JOreZmUIxXpXOxS8a1Wu2D4EHNsgiDq7CeSXfzG9S8zR3NgJjhek9vUYkXrdxZDmNCzQhoJiIVm6vDPcz0QVR67Ui+JjeISOx41Nk6b8E1Fez1ILdxsSul7KS4edjj7kJq2O0f8MUMVdiqXxu8eO5tW5r7gohQ0/4we35v18LxNfbv30Lw2nKPlYgICdg4xi4JjjG0FlNNNdWh1uIFtOqONdrEfKJjs6UJo1ypouT5moYhYgQMCOyzp9ywSwHUhVI+wN0reN3GFcVNZRRBvYEeAK7DflTNO/KWtV5ym4usTXPVpZTLWs9LXkqpTfRFuvNngxEA+IswyPNBxasi2T1GeeNfSUkJCP4s3sFP0ad/2kOibZsyAAFVJd3IPMTWnjGIiFoL0lorrQ6t5BYTpxjSwNLnE0YGiIyBOUSmwIIwt8ZmrMpMgThxoABGjBjRCZHrKLDt9V3Lftf8CnuOqbKdZCeKlmXOeW2t+Au4zPP5fLmcz8t8Lsv8cMkfnvLHx/njw/m85EuRp6U9XMqShYnMoKq1bnIEROystwGoYW6moEV0KXTJclroMbX9wLuh3h3l7ih3VaehxrACE4cwjcOQ0pCGFFOMiePAjkEckVzEyGqQ19Lu3/H5gxo3DEpRYirLAsQeF3a1R1316dw+nnMtzYBTUopVaZVSuxWKD/8AVS0oKGKi5gryzhb228tBSdRE0QxxkzV46gdXGYeqADRR9HEYKtIyERtS161tZi2m2uepblV2YgLQPv4JnJsR1UadyCNEUPddfLGT4DZPWVUMobMK5sEKOZr13+Uhkl1X6wtecFvJz2v4+Rv4omT8476Ja+HiilPOvSBzYIYYo6p481tttXT3c6euvTVXt5l7BmZizTQLRNbUaGeoSEbufUDeN97tc0BBq7dMw9bR0SfFqoEqau+KQ9hQyMCHKItoabKWNq/lvNbzWufSZ6Mvpa651iZXGud/9/hLMKjXtrfPVdm4R7uChGpKtqXeLwrVz0/w8tMXynF4iT5d1ycK6C54/fb3+7617hjSanDDyFJazjVGHoYwlNha9AQoIAqjilWzBsAGQY3VglBgGgIRohgbBqAE3u1oQqZEeF04L6kdZ8S8zmibgV1e87rMOa9ucZ9LPV3m8+lyuVzm8/my5qe53p/zh8f54+NcRZfSzqs8zrWq+j1hJn2HJUTAgOzO2X4em1uVFnUZd2TcMR2YdokOI19WyU2N7TIbg4rqkGg/xSnGMcUhxpRSSoniyGGgkJCCN7ubgTSRhx/S+lAriYVGg4ZkHCEEo2BEcQhEZjXnx8t8P5+bKoXpOFCICrC25qIrQlD0K2YNVEWx9jjIs/TrVfbMSUVF3Gfzyq+9ENMA1NaaQW4thIQMZq5ml00p7iWwPhBPRaBv1HYledBAdXO5NjVrqkQUmYJ5XUeq75o9pulrzjaS4GrM5kxKT1DNXOfS80AVBTN87hToct0NoPpk1Cs58RxNb3fTz98avYz/klelECOHkEZrtabi0VAZyoZIuZZSxBps/WwdLEBRjBUYMCKl3vBJkTEyBcbArvFsaIruyQUAYGiA3sq7BUZmJmIOQI5BtUoubc71vJSnpVzW6mX43BVh8i8EIPgfy8XAQFF9h/ObVLArhmwLH2wrIL08m8+n/scY9CkAdTXAC8UXgqmKEGLzfNsXi4iW0gJzjDyOsU4iIq1pyS0GdmBKZoLIooEoMEemGAKhBcYqtpYmMhNCQEyBKfg8gD7XMRABeutj7uijCmCl1MvlMs9zXpZaci3ltOTzZT1f5vP5Ms/zUu20tIdLuT+tlzXXJqXpnNt5bVeRi7rvhJ8jAlQMHPBKJliHoU5w9HowNLNiFkAr07xqUZwLzqZrrpd53U9xN7TEeQg8xDimMA5pN44ppRgTMnmQBQYmtl7OpgIiCIYK2moBzobNSJHSEBBBW7l8OJ0fyyIQRx5umIhUbVO+AiIyQiAkAlXLotaUEBNDJmSkFIMTVoGBA6pKzqXV5vz3lpIgIoqKKKy51CqtNZLGMbi1gKp26y6AzXwKrkHpRgX0IALAcBurbaQqDV2njHzN21SEcCOtt5TQnEpSufpDEpI6S28m6qP3uugODK72WFspxZ8dbRPId+CF60q/XluH3JeJ/PUG2QqpmwMt4HOPig93jCmOMvaAKD9HRrVU0ybS1JTVFCAYNCSjADFSjCGFECkxxUApUGBkBHU9tAlqAxCfawt9RKynaX3IWGlWxURgrbKWtpa6rG6WmE9LEbeeEq0i1zrMv+T4y3IxP5FispGyKKqoSiqiiApkW6XjuTa9hXH9FD9Dz49CoRePexnH9cB5K4WiqhsLmqq02rx1P6UgKk21NhmypBRSCsMQapNRVNVCV8mbBBYDMFKBGmp2VCJKHFSCqQYiJGIiVLvMy5pLaVW1gVlr7XR28JnXZc7ruuR6Wct5zqfLcr7MtWkRnIs+zfXD0wqgolpqm9fS1FuI+lq/tnH0sqNiJ7+6XG5bid1wsteI8er5r1AbVEGjdC6Um7y/L0+XcphgCMJgA9MQwm7gmym9PrRpCCkGDMhMTIiAqpBzdtsdZwpUYK32lLUINMOUAiLU1k73p9NjrYa3lDZnzB6LAPTpvszEjKBWBJoYB3QT5UA0hDAOaRojooVgpla2Ub8pJgcgeJHj5FJzKWYg0qKp2+P43ucpBG4xDF6JSPi0/tor9aLGqAJgKChuWkVMAgo+n87601zbfwE6OGhvYjYgBDIiU2u1lVJabbXUnKtbkQxj2u3GcUxdGHpV8j7H9n+GgIAOoJ217RDVX8i25jvG2vXrW2SJAHEYOISUUmsuL6q1FvUpYNJURaU1QLd3wxAopTCmGDlGTqEX0QKqVJJGKrUrgQBdmosbfe9g3jYbv9JsKbLkNue2lLqWNud6WYtHp2LdUvlfDkJ/WS4G1yDTDHx+sRfK1CesuI1Jr7Z61eyTn70C/4vACF4g0osiwovsuIeXtqHP9jUzVSURInIRVBWtVUpsMYZhCLWG1pI8YxB1uaMECVq5Rgcg5sRhCKHFKKpMlGtz5q/k4iqedVnneT6f58s8L/Occ1n81C/l6bIsawXkZpgbzkXXKmAmhk0sFxERQwzc19aL3e8Zff2mYt70MhsdhRsEX4NJA/CEeC22xwBxfMhyf9Y/fJeX3PYTsimpJKIp8mGgL24GyW2fOCWiCIExkEsX2IzAjExYpbUqWZe5PpzLUqEpjikoQBZ5fLqcL00Q4w4cKEHQOWEAQADCbroC1YraWu0m2hAgEAamFHgc4jgNZhpYELT2E9tiTD3Nhc7AqFltMi+5qQI1FSEO2NkiAe20cO8P7jc9dHy+rpiNVDETAFZVgIrIiiGE8IwRDjRbLNorabatrk6miv9CFc1dPljLNq9mXWtMYb+fdrtx2g3TmELgfr1wo6o/Cfe3627PCNWP52vdy33b2nhm1ACcfnvRT9OpeGLmGKPIANJMa4chaSnwNKZhSCElTimOo2vuUgwp8BCI0bRmqVnqKpWkgkJDl0HBxvuquRfHWjRXzU2XInOuy8YBuU3Hlid3K9N/HkxeHn9JLvYChvxMag+DVER6Ac/FfIREBrCRkhsAvcSV5+vx4qK8OPBHF057X3yniUUBmrnaERCDcKcrm1TmELjW2FqUpq1JLRIC+Q4wDKGpNJZC3IGJcQyxhHhZC5jVDkClrGWel3XNy7JcLvP5dF6WpTTLVZZcL2s9zysgGbICVbW1mZsjhUg+5KSJAigTMHTVsnZNQscRs+cT0jNM2Pz30WvFSAC9EwGuBBUAUwh02I+AVFp9msvjORNTEdAqIMImC7dlJTJUgf0Q0kAhoSvCmYzIEENrVpuZ2lrtUuS01vNa12qizstYEbnkmkUV0Pt+EUxEr7KDwBTQHVNJTZpaEROFFGiMSMSBaRiGw+GgphFKwFWauOBlHK0bj/hSUM2leH4NZCKi0lTY7ZbMzECxD0YhDpFVALrzDwGY95peY6GN4CZiJ2jUVLRu8QS76UentPu5V7iyxluyr94Qq7oueVmyDyn2Ruicay61VFnWslvH3W6cpjSkGELwSdf9rdm2mnt4hFuOvd1PcL0xntPJLTK61uU2Um1LSb3Y17VF7uAjQsZoAS2iimkLTOMQxyHthjRNaRhiGoaYUowxpZBiCAg+RV7qKCVrXWvJlotaUW391lNtTdbSLmubc5uLLqVd1jqvdclXGOp6necT+C8+/jIMej5L/bSYK9D6azXzOjqZ9ykjdtpoy7R++kQ//fKfOTw5vhJGnTzc+kg8ezbVVtm9ykREfWBRaWusMXIaeBiimwlU1sAYiJgxBl5JEFYVzaXmXKRJXvP5dHm4f5rn+XKeRXTJdS1tLTKvtYluQIsCaIjMNAwcYiCidV1bLdqKWXPJCW7MjgvXVN1dqCPQlo6YqhIiMPekjNFtUvUFrYCIQBQiH/fD4TCIaml6mUuubQpDFW1VpYo1DYRLYISaK+xHG0YaRtoNnCIE97dl8zkUYLBUPRc9Z7kUWauqGrdW1YpIqdIUTEXVRAzMmm4NbgBMmJhjZABUk6ZQxJrAEHCMjMQh0DCk/fFoICQ5VDHVmkvNRVWNPTUEv1NzKa3L+hUJpTUOrEJbhi4G6OJcpBBC6nDhrYFouuUPfYlI0+5D1qka7ZIfAzD3wDEDM/G0WFWc1d0SQ6ulSVM32V6WsszrMmc3xKlVcqkA0Hwge9NapTVpk46DxcjPlPyLCo1dMzztpJA+W1f/OECGDXN+8tX+ARGIEIjAEJhNhUEYBI3JGhoHpiHFcUjjEKchjYO3J49erIgpRiY3CZKatWSpI+UMvBhlxYJQwKqBVNFc2yWX01xPa5tzW2vPxfwMieqPX+S/+PjLMejFYX7htCfjiMhEZMzdKQEQAYl+BIsvObqX+dmndYOXX3z+xksKyezFJDJSADBVZiUkauLl81JDji0GTkMYatz8zoCpMWEIFAOHaKa1tT77sNa2zMvT4/nDh4fT0wUMSpV5LU+ndV4rcw/1zMBkM13bem5VZV6WvK6tFgALdGUtOg0kogoK1/nC20nokbj6EHWXvxMFQEZpnT7CTV3HgcbEx+O436VLlbW0y1L83ixVc5GySisNAMYQmuAlw2GEacdT5aNs5pBBmbrgGMHmKucipyxzkbX24chVpHZFiaqoqNamxijihuSKgIFoCBwDC4AZiLqXsIHh0NXXHFPaHQ8ICi3AnMGsxw+iMXjyTuCqKJFaq6o2E2QUrzpjBWTogaRuNX9iDr7spPWlhAjobRZ4XZ0Nla8yRUAyFQMzFeKARGagqiINfHirj4XSnmO26tSPtqbrWtc1L0tuHt/K1c6sNdHazLc3EfOULrB1le2LKH8LhLYpQur3B0D3Vv7kFt4ioJ/c2C+ShGeNAQEaRcRAFIAJmEzcYywFikyRKUYahpTGMQ1jGoaUhsBs0qxVRyKtmZeLUhRYGixoZIqCWoFWgaXIaa0Pl3xZa2mam6ylldb+mX7Uf8nxr8Qgb0BWT749mdjwx+tlSJ/+CPZ6xna+P4mqnj/dHvzT37k5zsL2a7drKiJeQcSulFdVq00CVyIaxti3qaa1NseOGMMwRGezW2s+m21Z8uU8L/OaxbJiLu08l5JrrgKECuSN1YzuCcuBGRFV27LkWkrO2dHN75QQPOfulqjSpDWxBii9xNtBCrZsY6utEGGIiAiExuzpmBFAIAqJQ+I4RgO8rPV0WZdcQ2Rm9sFxTbSJgUFFXYoCNgWoaMYwDIxigAZiCrqx3eDj38RMDERNzBD6XK7u/qigZsvahgQ+l8X39BhoSEyEbfNbREIDMui9dWpATLvDgdFAUpELWFGRmnOrVWO46h5Mm4hUEYPerC6im8G+X2UEAzVBY0Rwy2s/XSrNAKHPNFSzrTS2DdEEAAMjRPXGVDBVYQouyFIVbe4lqCpSa6/x9sHEpXopel1rzkXEczdwAYmv1tZknq019eFZ+92w2w3TGNHc6HJb7i/AyFe4IaoCEJBeU7QXN8VPPsGt0m9dBAUIcHWQCQQRISBGooDup+6cQ78nnGEijpymOO1CjF5s70jUCg2Xio/FTqhkDaGBUqtGWWCues7taSnntYpoFXUb0msQ9D92/KswCLZgZDMyQ6IfxzU9VOnc2RWRfkz3/GwQ95yBfPKoF+XN59dgAKBqhN1Mr9amZlz7plml67xyaXHtlmkphdYkptBNJ92XjxCJFbgaVSBBCoGbKFZW1RAjb521gQMxgmlrfRojIAxD9G9P05CG4JVsn6QkTXMugKgA85Kv74mulWFTAN6qsBATbecQVMHMyIABQyJKzJHPazld1vNcimga4nOA6aMiAACgqlBFQDCGSRjhxSAY65wsAhAR+eB2gGujBSEFMkDs/Qxqy1oBQLfmBkSMkceBAdE1m4jI3FWOBr0FRwGHaRpTIC3n9V6XAqat1laKDMlLfmpaWhNptTUzEPGkVVprFKwz84AuburN1E4McUAiRVIkbA2goaI7P79QE+q2MxkYdn0jCBITB29KVUSV3o/ZWq1FRW2e87rmkmutzWEo52oKxNw7JK9icAAza03mObtmLeead2k3DtMUmRg67/08T/BFOQwAvHvj5Y3Vr+YnW/Wz6AgAgAAYgdHdiwEBYscgiwQBzbN5JmC0PoDFbBtfSAYBaUBGSkjYraEonRcN0KhmWFEXlYuEVWkVWKpecjst5bQUrzuKWt2y8v/h418bB21/P+MBIoogAgiA2WbsTEhE27Z/zX9/NgHeyLt+fAJCL7+OL0IiX2/YYyOfn2kkevVTU+vDrGMugSmmMKQ4jLGJpBZdUoWEMbJ10zniEMYxEaGZJeurjdmdg3rDsC87M0GCmILfk/v9OE3DMEZmch7aqfF1yQamBpfL2vc77AURMFBw6roXVULklIjYQzw3vQFQY0SMxEMgptOcny7rWloIHAM7zWHmHjgAvcykolXBgEA1bZynS4m7Gg8MvE2uf2+T7NA23kPAjFDU5rUS+2r1TlGKgcchFFXZ2kiJyXvuO8mrpgYxpf1xlwja6d2yPpqp1FLyOk5jv5gGrVZp1Wdyq3iJ11QEEYzU+j4W/ArDJlcgZAQmYkHCrdcAtFd0NovDTgCZOjkAqg0MkBpxBHBtep8s0pqUXGtppeoy53nJ85xrbeIxTmmq9jzAKm6FNkRm2tICWJYsTUqpZWqlOBEcQuDOSutGhX+6+K/Ugz3/9UnGgACIdg2rECEgMAKDERqhBf9DHYwQDFF8FWoz3TZMM+9wCAYRAN0fwP1O2CKvBou1WRasJ8lPlc6V5oZLg6XpXOS81muh6S+tgv30+DeIg14GNe6+iAguX2UzRSUiMg/o8fms//zLvoLac3nyxeH1gOd/oDflbDtDz6s33hrxuQFNTaVJLUyMiDgMYRzT1IbWJMQCABw4pmD9HRkxpRQAzG0PUpLWnAS8akBAVVv1y2kAmFI4HKfjzc4LECF5jmatybrUdc0GVmrLj5ec/RJaV8gB9AF6PUAHDhgCEqH34pLnYmqqFpk4EkdWtHMuuYkipCHExK3KJpPrcaLPzAYFIoxCrvLQYFeJgBfg3P99S5M72fmslkSzTYK4lpYS+4QBJ0RDoBS5ZOdW+1Q4997XTjaBAXCI424/Rb6MuwUQzLS1Woq02lET6KU7BxKaemuSknvJ9awHkcn5sV6Z9xGb0JMyA/B6Tu9a31zFHJx7fzpzK4JIXsB2jyAiEkQ1zLkuS54vuRQptc1zmZeccxWRJn7FfZASMXNKcUgJh2vN8vqrPI5z9rq549OQQgzBrfdBP0EWvzcMTTdfWA+bcCvNezi35VzmQk0CC6heCWPsc5hf/unVavOKoTQTzkvhSBSJAoWRwkQhIrg0mhAwS7tkO616Wu0pw1Ohp8qnipeKS4PcoIg2Ub9RbIOhfyle/Nzxr8UgeMkqX3ni65VwG13zCXyAiPSJYqh/8dPU7FNart+YHXi6HuKqngB4Fk8YOMHh/1DFl0+IYCLN93wibNPgld9SaoyBA4cY1AwJry3UIRBRZMYYQ63amvir8d+uaq01X0ch8jQNh+Pu1evj8WaKkUMKMbGqrUtZ14JMojpf1vNpXpbsG/z2jsxprWvzoL9mEZM+zgCvc1jNLDKlwBjhVHI2gYgBKTEFJlPxAr+73V2jVNzs/UrTJYsXxZg6m0BX02i8Quz2ETaaAYGY+rCE2oi6pyChW4simKkaIwRGBSYmZ5fMIJLHjyENY2AMafQ1q6qtllqKSAPPxUqtpRAiM4fu3+kMRt+R1GzjerS7QvcZhQREbm7pt7/zRpvl4bZaVDEEROMQOaY+CkEbELu7kHMFxPT0tFwu6zxnr38tay2leUmh1OZYRuSctUhTEUmDmyKBu81e+SyfPe1EpO4GTRYC87W/4+V67zvsi00JEa3Hf76/EgChEfQGcQYN6DCkDkPo6AM9MupbihkYGqiq1rwiBUQGIDNstRkGdH5xmGJMRcOlwONcPzwt7z+ePn68P51P87Ke1jpXW5uV5hh0vbt+ypn/Zce/AQa9PPoG4DoLNFHvre9+CCgoINcA5xpQbD+7ffVFJRqu8H/1Znm+eNvhn/az8bzJ+7PChozc+l7lv0tEW5VYAgdOKaQxGRgSmlqtzcxc84L+uhE9kBbp6MHUA5kYOaZwvNndvTq+en3YH6eYAhKoai0NAFS15moijw+nZcnrUnqAsIUrHkriix5j3fz8ECAEYp+pCQAAjBgIBTU3FVROlCKF3o+OV8ISNyWX/x4xU7VcdS0yJZJgEJ2h6xDP29T4fidchXtbYOWJRqmtVA4Mnfq5KqTNyTgIGyFniGqgLmJAZA7DMAW0OIz9plBttdaapTUkbs1qreuymiIzP19QM1WfvosAZirAAsB9H1ZwBhwNCNmu0/fARHUjT7Y25C3LdL82cx9YUzO5Lj8ObADTlL7+5sN8yWY+m1JqE7yy3KoqHbNUFbyNQ1QGTTF6dua/T1S1WCmtllaKlNJ2uzRNw5CcNPRWlU+kQf1A6JZ4viOY+/YAOwEE4DBEYI4+jkQOOoRXqLo+N9iWO0krZZ09MFLVnHNVFGOjiJw4jrXpx4fzw2n98LR8//H07t3H0+lUa11LXpY1lyp/ZkTP//Dxb4xBnVlVsN5v+mmU1Glm3OSdm8gVoLNtPmmx842bxhGekef6mW2L1F5gkW34Y5+eeb/XpYsDt/QRTFQoMzENY5pclOJzOkXMFHuyhMwEgK2qdH0/APTtjhKFwIfj9Or14e718dXr4zBGd5ZQMx/loU1N9fHhdH6a1yWLyvVMbXpaAADd7nczk2auCWd2X2DsBtYATiwWbc0AAoUAAYEQalFmQAN7ISbffg+IWBWbs+wG9ZZm6BLI67jFa33uOfZ5piYQvSPQ4yAz6lwWIm8JowoQQCA0ICBSAJ+v4CEMEaVhYmtp2HFIfqlEWim51cIxNWmtSc5FgIkTM6sJgItuYXOEdPsbxcA957ZNmojYu10w9ZkFKtqsR9KevAGCGRGbCnNQZl8q5tOjNq2WD7+bpuGbbz6GQGDgJQtmhm3h+YWQTll1LxHt1Qc1NQ59+BUAAKI7K6+5rGuaprqb0jimcUz8PJ7vRTTxggnqmdeWVQUwRmU0BmMwQqXtKw5D1ydwPHqxEHqAKLV4gNZaa7UCz0u1IlCVBNgoliofH58+fHx4/+Hj+4/37z48PD49iYjLJnIpfdzOv93xbx4HwcaMYq9CbFAO0FdMj0MJAYiANqPRDv34SVdQj3l+9Dm4h9Rm72a9NtdX1PMaeXE8e0kaAEBrzcCo+owU2uwgVJoQoZmSDw4iNnVG2XHpxbObMdM4pv1huLndv3p92B+mcYoxshmV0mouPtyk5PLhh8cP7x/nJa9rvb4kXx/XLWUL7dzFDUSkNX9h9IIx6FBhCAyG3bagkzXugRRjiMzmbTv99aLHkaZQql5W2axpA6Al9kl4Bq6ODBjVZ+eCDxMDADACNWyGCCKeSBkTe41wc3wHYo7kPoQ+0hDMjIn9goYYoRrHFOJgsgKCqTVXM8S0LmtrlUJA6QGy9+ODPZ+dbfvRLUgw6K6J3njBvWxAwdiVoD1oxWv9whQxIPpk3EG1GZDvZdfNIKVIROOQjsfp8fFy3UNFupkiXa3re0Atlk17l5SpGgy+PNi4Z23+Qms1EV3Xsq7DbhrGqU5DSimGwNcNG68AhAAG1FOq3r3FqMHTLuhIRNgfE9A2LabHTHC9Wfr+buiuZdYq1FLKyuuiGE+rXLKt1bJgMyxVn87zw+l0//D04f7h4fHxdL5Yt2SV2tq/shL/0+PfPA56Zqn9oiOCXFMnMuzDPL03Cp7zkR5z+Mj5l06G8PJzgL70EHx6rHRN2fNszB9FiVfi74XIGqCJqBn28ir0JgozqeLuKu5ir6q1OgBto9o8D2dKiccx7Q/j8WY6HqfdbpimOA7BAEqRVlsrsi75cna54+Pj4yWvVa8jkj45XdfPwUXnSNCal5q28lnnid3PBpXA0NyShxAAjSsSARENKRKAqTXp7XxbJRhFNRe5MDnnkpIGJggA1lW7HJADRQNAf7Y+xBzMQEFRQaCpqiEABMYUmRBFQNRpGYpEIcQmktfiZ52YfFZZCFFEQhg4JmmLv+FWWy25tTGva8lrv8V7nr4lKT0g2LAJDKxnRtCbeE1NXQiAaO6NbSqqDQFU5MWpdiGb2/0GRHAO2LbrYh6ypRCYpzE9PJw9r+2rz/O6DoVwDbxNVZrlvo+JqAwpxRTZ2PNK9FGuWzZnS25NcqllbOMYnUriwNjFWFsSCkDQ4YbAwMMfhyHQgG6/CAjAXoZ/se6fCb1t73JSyESLqJRskBFnUbg/18elnVa5ZF0blGZrLpd5eTpd7h+fHh4e5zU7B9rz0H9dJf6nx789BsGn9xV0u17PIRSlt1kDsF9LkecmaEbkPsXx+SC8nk/onjH9cO3uT4c4/tw5utJF/XlMTa15Sb4PbAO1xo0DhUDMFIJIYDercwwyM3Lfg8jTmHb7Yb8fuhptiiGwvxSpLa/lcl7ny3J6PH98//TxwymvtZT26St6PlE+bkM9TzNAxEZi5rWe/hBPfIgQkFAN0LqhOIGIuoCbkWngiCy9YU3aNRwya80WEOeSU9K9XDk4MFcbMHKg0MUHxryNogIytabmAkjyOi5iikTktmQgBoGZYsAUsVLJdeNfyDOOEJO16hMdmvUkWkVKzpNIyes6z8ojUuhhm15jnStq4/Ve8EDar6gioAqhADGAdduVoGxiLhDoK8A242qvg7Hzf67PtG1YHgDsdmNMgYnHMa1rYWZ/uc8XD93I+Opg3xVqrTfsiyNRismiMfN1N7XrbAhxR1apLbVRhhRiCiGEyK6T6HQYmTIogVH/uGVeoIxKnXLvSNQH8D6/xr4D24abRKgAtclapTUVtdr0/rTcn5aPT8vjpZyyujFmbbJ6v2TOOXctmztf2o+3+X/t8X8IBn16XHMlgBelMc83/HyRuXO8rx6MviG8gCFTE3kBPf3va+zzya+Dn3zpk+8AQK+w9LwNTVptBcBEW2Bu6BOGYgxXAPJMjhm9Xnb1BpnGOE1pHCITI6CKllwvl3W+LJfT8vhwfnw4f/jw9PR4qXVzdbryxS+PbYt0E8DWNES65rTXQjRRV7IgKpKbHAERqCJxJ9lipIidf0dEH0jZeUTD1qAGy1Vrs1K1JXeLAwMjBu/sNfY3a4G70hC8BULMxUY+HJmIhsgI2MTczysEjkPAFHvNH/w1E8eACCHECgjIFILataHCainrMktrNWeYBgpR3dXISWiAzuZsza3XDL/fZAgeOagpmbofKiIxR3cXhE4K9SgakczUc1Mvk7oZraLvEAgAIfBuNzBTDGHWvPUfbbXovpcZYW/o2+SQJu3KlZTuNWsao9cGyYdJI6EZel5TWxNpJk0lqSVTpeADYtHHBxMYOwxZxyA3n3ckouv734j3jWG9shsbeG7ljlxlKc2bH0uVtcjTefWRGA9Py4fTfFmKqrnZZq3tJfvjFvV/wa3/Lzv+D8eglwxR/4KBhz+O3QZAZj54HAGYKISumPPSpit05SUI9aTo06Lg8439M6fJXjz2mpH5GgFXpqGaNRLxAlAI7OPtXwSf6BM+YnQjBB6GOAw9kCbCVlttbV3K5bycTvPT0+Xjh9OH908P9xfP5n70Wn/uRfZV67NURNTUem2jZ7jEFNyLH8BPDVg3gYRe4FcQVXA5JVLgvgdovwAYmQhJxHKRHDmQxkhmyEQx0YDMaubtTgxu7g4I0gzNUGGtzczYgAmHyKK2ll6sHQbe7aIQi8+/RDf3opQSETJzqzXnbIbW2+4RAGotl/NZ1EIaLEQcRs2rSbuS4n6tyZ0FQGWTHflF6SSZgb9LJ5p9gg9zNDBEkiZETN3K1vsjfCv05/EyJYF3nBGK6jil3X58Os3jkJoqIBB2baD7iVrHtmuG6CQR9gwaALYKLAAEZiLtE+g7SqKZllxMKmoFragVhxQGNiACYEYXH3Jnf5RAfeIJeoL2TIr1M+WbBb6AoCbml4Y2FnXN9eny/2/vXdvcRnKk0QAySars3vO+z/n/f/DcZsauKonMTADnA4Aky90z6+7p66yws267LhJFiUEgEAgcr/fjfW/7MQ6fhXw/vrwfr/f9/dEeezfArLs4a8ivzP78OH6HPOhDp9z/KjEV4Emsc0PVCgULoNqC5omhQB9A9TnJkJufucT84ycAaD7zv4Ylxzgz47h7i3O6RDSHEgJxtrptAUC3bdm2tS7VzFzOfxz9/v74+np/e338/W+vf/v/Xv/x9/f3933SQPNUzOOhbw/KVMEU9KYTt3HfNzDKUlaYCZMOVdVJTGi4fjjJTdFw4Tw1l4LVr8Q+bG+6rbpUxxr4tsitYIGZWa1wDPKjlGFs0G72Hh/8Wnhby/s+Hl26GIi2rXz+tBzGTdQ/9MQg5mVdnXoTkWPfw4rVQkcmY+z3d6XKZUWpVBdWtXbEyXFTY5vsIJNPWpAhqSMiTtcaM3fP5wKAS62AgN3pk7lkp5WNFZ5GBF1vvoTLTSCJUCvfXhYirrVK6xSCBxegM1hjUVAgaVSdiK+ZDDXrmn4621qXUhDCdMS4OwFMKwPa+z5YG+u6WlUtWEslWoi8y15cixgrqomyMU8fP9gGeDFP2cwVtb2NNkTFHEbfH/31fvzt6/3L2/H6OPZj9BFjz2+P9v5oexuc6K8fZC6/VfweGIQzG/K/Ow8QFBcRzAoTjUFAF9FWOLvbQO70Vj15DXyoq/Bj9PnmvP00AAHIDD0e19TXpoGgZj4BRUQ+WbZty+3mMLR4Y3XdFmZSld6ld9n343E/3t7ur18fX77c//a313/8/e319SGi5yE48n48lgnHyByNmMdQA0q5TnehlmUty1BVGZpKAiC6z2owgqoxUymVyXf6ei4RyoOJ4q1rLXo03Ra7bXHVrdVePCuA1UqlTD4GoyuZtV1S/UCF6baW933sTZxY29byw6eFBu1NE82JmHx3kKma6b4/MpN1SYALqZpRrbetlIXqUs2IWWWQt6bzTfcKXSkQerriExEwHe+9JUThLQ0QkRInBVQS9nneERhsZloqUXdNxrJUIty25eXT+thbrWlFc94S8kNESNMxiweNu5upYnTxbIJMaV0WFCvMwMpUqyvUsVZsldZKtQjLPnZSqy4DKuDC5GRzgRWE7MIZZsoPt11KTVWL7JmEifc+Hse4731vsfb97d6+vu//eNv/9vXxt6/3+94BiGrrsrfhtiQSnymc+qLfMn4nDPomkiGyOKUG+Okb4ivoT7J2dmQn+3PStPFglz+/9/nzwV0SYrh8ztUMoj5/X5jrWpZa1qXebsttW15uy+223rZlXUthEFTGUNV9b/f78fZ6f319fP36+Pvf3/7x97f39/04OgBKFy58S9j/6LBmouTLp3D2awuXQgVGhX12H16SUepoPYt0DUIwT8wU7RKCmRKEFKHdA8CqPAR9mJEqqxaEpTWjViqcy6XMCKTDSmUnJmsJW1hfdxdttcLLwiwna+AUupMpvR0yxrE/xhBQGi3AAMgYYrr9sGy3F62LENW6dJX5Rkc16hUTyFSNlGjmIJgptJmYlUicwCgoRG6E6KxskAHJCWT31rcbMjGbSCEqzLXybfNqjiabGVM1l/fMP0J+aPO9MCeooIANoDMKozKYUUAL261iqeSbHpeCtWKttBQwWUWnMYi5cGFj31PIQHGpIjiSofhIUZYFpGZH97V2sXDpfW+v78f7oz2OcfTRh9z3/v5or/fjy/vuRZm5gNrgjdSPn8afc1X90vgDMMjLDZqzUpMHKTrCbwUA4J8Lp6mzXTlZ6vmLP3Gevv3CjzLWCwrQ9atGIMxZDW/IFebbtnz6tL7clpfIg8q6Fm8JeQOkd7m/P76+Pl5fH1++3L9+uf/jy/v7/WhtULoF6MWr6vrsFDD8oQEGUKlMgAwF/Cacbm1ka6lmUgsb5XwGgciTJv/w2xCNVi3FcnsAxBZsP+C5EsB94H0XgSiL3lgLc6FSqFby+VzzOke9y061MhuvtdTI9jEkrmdPVEQl1ioYuMRbaGb749Hb0fbH6N09Nyy77mY2eit1+fzD/zq4yOC6LKMf2TC1GXkaoarEOr+dGOQZs1DYengLxJfUTYtugp3tZaLEK0S3a4ipaq1MhLrwp5f1fj+8MwCE1GbmP37y/XKd98v5sXKxhRlsYahAmUCVaC38Uu22Yq3kQ+1LsbVgWwgGJhS2CmFVb0VE6hMzlza78KI2RC2gHiLm3oYtv/o4+j9eH1/e9rf74dmQuBvZ3t/ux+v9uO+dkq/1OfifuGJ/4/hj8qBsbthFAOI2jHGh+ntZfM9qbtvJL5LZTJQsr+L4B769xn/8tDh/6vp994md2wyImPjltn7+7K339bbVbeVt5XUtrouBmpj2Ph778fr6+Pp6//Ll/uUfj6+v99ev995FRDnTOrr4Sc4nPw+donK5/AmvxkuJn+Ew+irLUof1qiwg9m23CmYihntWiCqZ+EUZ/R9OaY0ZGTPBdzqq8dGtiQq6FS3LUgFiKgvXhUvxrN9ETAu4qPsuFfC2lFrY4H1cmKEwXHajqiO12ByetAxYOx6jt7Y/Wjtu0eVNGYuhHQcR//C//k/0NhrVZZ0zVRSgY0wak6vmDJdbu07bVIrPlA4OI0/QrLmyiPHR0PwkzOWEU6PotL4tS6m1wKxWNmeCJ4SBACvMLl++3BMRFnepJwWgVsTXdZkW6EJamVfWW+HPi91WKxSGYwvbUlwEBvaBDDMyIjXK1kD4s5G5uFbMjiF9qGoc2Ps+vr7v73t30/025P3Rvr4f/3h9vN6P90crTO6msB/9vvf73jnNJWYt8DvHH1WL4SN94zJTv4sZABfsmBkrEYmX4pw++Rw8MYBoBMzbFIBLdTxjpkLf5ETxXfOC0OZv87aWl5fQ/txuy3Yr61rWlZeFq69+Bbk5w360+/v+5ev9y9f7P/5+//r1/v6+70eHmc/K0dwFckl0PwDQj8+PGgwq5npmLxa8/7PWWplrIVZGUD8YBq4EMnF5sioxM5uakVnxjrifJDPACnFhImZRNFGxIdZpsZdPpaAyc6lcl9C1+PsixVxt6KtrV0eozIOmw7zvmfP6FBdbKWLq7Ri9tWM/Ho/tJQ6G0x7Siebbyw+ytMN0WZfcd0vAhQhMmlRVSSX63fm2ztpKVVTVX0ASRn7WvSOl14KK3KY/r8Qkl23baq1MsFp5DKWL6s+v+aWyV8ShAswJpLyo4xtquFXaFlrYKtnKtrJtxT5V+2E1f1uYsBRbisXgDIgJBRZ7jFwOpSRqaqRQT/Fa13sbIbNQE7G3x/H2aG/39vZoj6MP0TbkcYy3+/GPt/3vXx8gLIUBzJ7Xb9Rx//74o/KgD2FmcpLEZObdTzUzyXeXiY0tFs5y9h4RIj3grLEut6XMjGaXN7744/wo8IeIauXbVl9e1s+fth9+uN1uy7bVdS2+E85txGHWuu9XaF9fH29vjkGPr18ej8exuzWHgdP+Im/o5y35mvLMF54JEJUSAm4iqKj3c+bPDB2K2F/gtDPBSmwNApyyZe8h+yQCANRaauGgpi04bDUbYl30GL0YylgW1ew3+dVZQGBSgnipsq7lVuoP21IKt/TG5ZJZaikA6UUrYIjxyNZ2Gd3XNwMrcle7D3vVusCs1Pp5e3nI2NdbXVaV4W9pHLWeKo9gfljMe29wViYLsvz2+Xk4S6Q5EW35gQFyX26+Cm5m21pv2wKgFBpi85E4DOuNmbe1uOgnn8SVQDQ/oT6odFvpVmlbeKu0FSxkC2klXtlqgffnmFHJ4L0zmCdEXdRAS2UoiK032YepkQ+v3ffx9d7u+zi6TyXi7XG83Vsg0aP1oUN0iBxtPI7ehxChd3ez/aax84fFnwKDrpF3OzNFjuoBALOxESszG8RvOt6GYLtM55Ezg96iztSHUqnx45Nu815N8GW4y8K3rf7wefvh8/b507pttVQqrl5lIljKJPXtbf/7P973vX19fby+7m/vx+PRWp+epsEahiMEkTFL7F2PQ52HcX6G/QbKeTkZRGz1CsIM8NWiPRYqw4Lo8QoDqJX7MKVTi+UpBDGtW12XKiKjS+si6r5HNtTakPveF+K1yTZyQ4GaT1/5UlSX4XGhZauf1uW/bqspHsfoQ5GXJ1PkfTOjJ2IzGyqi2vZ9jO4GYgieHnBmh6ium1eBtx/+170f++1tWbd2iMXYmbmZZLTJ/AjNV20qkYIKM6sh/NsibZqfgQ93HT/T5iqskx80Zubi07DKhURtXZdtW482Cp9wZv4GubcU0W1bcOGtmTjmkJBvNOjTSi8L3xbeFtoqvyy0FixsC8d0OxO8/yWmomBQZarMTaQNexyhhOhtvN3Ho8tQEJeh9npvX96P90d/NHE10OPoDkDv98Nby6LWo+0lUz2E36Xv/j3xp8OgiOxUfwBrIyMT9fl6T3qZL5bMfjsyttiUnTOe8/GAkwM6W28+pFbIB6A+f7791w+3Hz7fPn/etqVw8XKdiAyqAkDlaPL6tv/jH+/73t/vx+vr/ni0x96mFtHyeYjCQBJE4sOdk1X4Nmj2s+fxuZlyopWJyTEOsDoGBXAZyKfICIWpW5jnRhPOzIBS+OW2LrX0waqQQxzlFSaGo8v90TcutyYi6tewOghZTVsjMhgX2rb6+bb818vydh/v996H5oslYiq1cC4adewD4LuAWtulH571Itc9ztZ7rQsMxPz5v/73+/F4f39dt1tvDyK3EPKuM64ic1MXcYmrbRzVNItyh9+s0nz3ZuxCjNQ6k2TLPrfDqNu+rmtlRqm8rhVGPprrtz01glmccmYiWpeaBwQzYqJauDLSdQ0vK39a6WXl20IL060GBpV0ApqNGtctqMJ3/z6a9KFtmJgx897l/Rj3Xd6bL0+xo8v73r+8H1/ej9bVj/Po8v5oX953UauFmUjNgh3/s2Q/Z/xJMSjelvxnUgJIMYaXNWyU6j9cDIby2pzJcao24gMxM5RZwBemWsvLtnz+vP3XDy8/fN5KZYIxozDMdAwthcm3PN/7+729vj72oz8ex/vb8f5ovY0+omVzyQJcJ8xqBhF8yIC+SYJmAN5E89UdUdB5XaWi0qUVN2Y31yJHRwxBnpCqEqKhpWZMbIZlqZ8/bUQA036Moapq3qcWNfftx7K2Lqrmw0qqgBGhkvlCeXKCvGz108vyf3xe7w+5zzwoX0QthYj1cgqcu1Gz1pu1wyyWxAdP7k0vVa7VG5M//Nf/fnu8396+rtvL4/5lfgriyuSTOvUCjdxsKTTQnOKE+D7BKRazvCFdmwJnYy5ECG7Ry6rMhZe1MsfgzhBxPoiISjJ7/na5CcFSaQyIqM9SmJqBaqWlek6khfiHjV9WcrMlBi+MQtFNL0zrwgDeHv1oAmCtTMDXe3/fR+txRh9N3o9xP8b7IfdjdDExa0Pux3i9t9d7s0yN+xARG6Jj6IVx/zPGnxSDvgnDzLEBv5O5dd/ZfwDr5TqmBCAiM5ZMyinn7hAVeKjmnQP69Gn7rx9upVAfg6hgIZHRmsIZAaLHMRyA3u/HGHIc/fFoj0frXYZodvOiR+MRvg0IkeXMaeij+otmwob5AjO3Z1I1Kt4LUjGQFVUJDHXSgeMng+hlAmgKGM1sqbxuBQAVvj/aeQ2bATTEfIJxDDW1c2LSZx4oCIv4d+VauVYWtcc+hsSJxaXpF2WQnxH2M0K+fZgAhGlh4KaZiYz1VrlUM9u2T8uyuleznxY/V54KIR44ZyXc68TNZV3rTBapkgHmZaR3A226MyB1jOouUZZdAGfxiIwZQutaa+VSaF3rvneEmimmGgEw+zE4Sx28udc71XXQCijf1kKwo42v71Jp+a9beQx5e/TRyT4vZvb13ttQZnzeahvyj/f2OMQMa+U+9Oujvz/G0WOt5NHlvY37Ie/HcA/vPrQN2dt4NHcKFQ1btbPP9Scpu34y/hoY5PHxPHpSFKU/EIvvZnpBrqllIpHkBIiISPzvfvtST01q4aUWM33sbVnYrKiM3tpSGdMZXmw/xtu9vb3vxDxy0ULvkoLqk9RBQIMPNEbR8DGxO1s59KF3A0w0Cc9TmIKqV12qiiEIS/l8Xc48hIdXyKnUN4T4ufJB11p42+j11RnuqFY9BXB7jTHU4LtPL7mN1yj+N+/TEUTNGy5D4kco/G01xhMSgzi2zQXWALFyLu4VgBnGGC91ub18iqMlNpXgxEm8B+4kemRB56WlquT6SlNBqfM+4MCV9P/5OfFfw3xyi5/NSQ9NLo9KKctauXCpXApJOiIxB47l2FcymMDb/cGRV4EJW+VtYR1spkvBGDx6+1rJYIXw1ezLG5mhDW1djiGuI2hD349xPzwnpSF6P8aX93Y09ayqib7v/eu9A6iFAQy1o4+9CedugrzB/AXir4RB17Bg1Oz6FbEpLwITGxEUE6GubFwpTjFyYTOz1gdgQ6qo7HsQK0vlynwMGcO7P/bY+9t9L7WYud+zyMh9EnwOL/l14pjiowM+6Y+ggS5wQ9k7+UgSuaO7f5RPSsj9AtlMxaDzF/0mTNkf5MK+C4wzy/Bikwi3W3XjFGbyjRd+tCDUyjBfmetgd/Z0Tv9uR6NCRhCz1mVvXbUSqoOuio4uKp50WlBUpURdiFiSUUqNRCWeCqIG0O3TD8xltFbC1dCYi6I7BPrCMLeXNotdPZENiWrxeTGdRjDzY0JIjyXgZKnzrLsxdaKPP4ZSNL9o25ZlqQTUWsZQx1lim+4yM/eshUW0lvL//H9fXH5lZgzcFv7hVkVNTZeCzxvfFh6iMGPCWmCGIdq6tqF7G8xkhi56P+TrvRWmWljUHod8eW+iutZSmIbq3qUPAVAKUw42qsZOhz9v6fWj+Kti0I/jG5mDuhvzWY74P64sjP+WHq0vSxlSSh/2ambmAMRMvUsfcTtpQ97vR60+fm3B+wTt+YHTmUPhlr3ff8ZG0+UQP4S7oIIAckoIcWdTqLtrn/Dlj+hXhXeoIlPI3yo+V1Hp06dFch/GnMX1wyixB82mjgHxqsCgtJ9xuwsAngfp4xiFuZbIKmYedN4diEp1q3kAEBlmyMzoPBXuFbBst7osqqPUSoD55utQyIDIN6F6LWaUDUcYlISlaFFT4ZLnwY/BzJVV3rSPtZhIBHQUml1EF4SLEFMphZjXddm2alksW6qP4p42S26Aw/2Kay3/1//998JcimtJbFt4Xfy+pUvBy8rM1MaEITLg6NqHHl1qIQ3vXX008ZkyUTPF3sWzpHms/lJ8tPBHn6C/TPznYNA1nM4g/2tcTZEin7gx4s01s00X9/dpbTDRtlUm6kOONpalAvDFUu7XNbnv68eQnSTIz2lqlgDARzQjv79gTvBWJxXkwJUVgsYLgatsLBIjqFKqhZDXhqcnwYsxDVjqEYIectzZtrrvg4hK4SGKpOe9MnWOyYsmIzsXjp3hyiFSRPnQumyLeZ0IYAw9jpEwZOfLdEmhwSeVvc8dcAln1llES11KXUbvfmpVZWJk5EHhDkxZ6BnxpYxSDevp5NwchBA9i+S05oci/Sk/8NTOV6kxw+n8dV22bbnfD2ZyhsXlTqWU8zcJaubJ9VLrstT/929fl1o8G2qt39a6LMUMR+8EfL5VZupDX+/t863elmLA3uT13l82H4JBG3o/xlLYlV/uN5ZvR2DQv3mZ/EniPxODPK7v0YUMALxTk2UaEbUjx9CBUkgeGoMzZqJ9Wv9k5eJJ+OVWmPUVpRkokO2WaN/oBJ9v2Oh/FsWpKFE1k2FUyf0NM3egy/8D4cjhG25coHzet4moi/qG4iHGzEutrTsxhil0GAEcbs2MZLcsXmtMXcEIbQhEjz5EzGruU/T+2vtxtO6eB14rgcinkUltjKEqXAqnn6GPeJe6mE89lbIfDzE1FdMx2TUqM61RUsxsxg9vvtmzHLuspzuzYz5XnBpMfRkhhVAIwXURg9lkiJi7dBNhXSvB+2WOQerue0S5AMQgqvH0qqWUZan73hDj+fq+d+y9FDIzUbsfozIVJhH7+1sDsNUCYKi9PgYQUmanigC47fHs9v2VkpzviP9kDPoXcfXENTPV0yxujDnNn59XAI4d/hdPvD27+CYhcbMoV74YFJbtsFl3/VSdngwqnVQMvMJ3wbG6BOf6u6evYLgs+q/XSo1iP0k+NMysNVE1v4p85gs28TP03E48wSBqtViSQMbMMHWy28uWLtp3PbrI5TyqoTV5ez+OY6j7KuYB+ILJojaGyBBHtPwtA8B1UTMD12Ubo3Vxr/CeL8TRjIzIlMwI5vRQvB+UKZ/nROE/e2a+Np29GL55LQq6i4wCxAg2iQnidbQxhVZzWcsQdU87S88ZFbISlS8TjTCcUjOtASI2XDwCA5FmXU+ELtbHCSZ7/7Cson90DnMy/Cc+Of8R8T8Ug/51RKb+U9+IqbVSfGkaLgw0pRbJf1bVPRGmRQnOe/N3HQRG1wmD8bjfPED+k5kAnx0LAsVfBRncMrF38QTNQEutpbiEJ5pDPmYpoqn9Sw0BGcgYhBiCYjBMrYmOQ47hvm8xjmtmRxt4p30f0QTMnroZRIRgMmSMsRWfYo0GlgHERdRAtL18vu/3w6c6VMyEspfgKgcXQltuuzGDe9pe3iI915EGCilQkNsOL9tYgoeevS1iMiUm1rSI83kxNS2cLouz66Q6gCJkhdVQ2ecawUzuv8dMI0Xnhg8g8p+LJ78k+L//kWcAQBZhTIW5ltgelcgQlFB+lYnZPTRsUjv/9HHjwb+JMHLNWzWnt13E7PwgOGYiFGa3j44fSBhN9Tb1oUS05BLqSBbSXUSG+IGcdHUQTJ64hEGEOj1/dE9XLD18VK01eX8/9qPLUMt9XgDM3KYSIjJ6J7ju3H/RVJW4eE5Vlw3M+/7eRzcTU4kClkNKGoouplmbnOcxPQ9CWP3hfM1q2v/nH3ufXL2SQSe35ydwXUut7EqbWXMnw2XuECDRq4MzREstTOQF6b9635+R8cyDvjeCfb501imNuCZte/60WQ5pGFDiNnslg4IOJTrzm1lARX7DhQyQoWamYlyihzWPCNfUynxwFOTZkEHNnNXR3GH9eHQz1FrYO/QUPhiurDU3KhNz/7aQ1MA8G8pjNcDEbEyby48JY3TEIu+bMkKV3m1x4kaJKpfiIKcgMXBd1+WHIfL+/ipjjN5MlalcXqAz18wwLuzqp5CtgqZG/sLuq8uecI6VkS+1nh2/OGA9+6fk3otmzMqFZUjv4uSO2U4MFaMoXYv3TxHza9b7oFAna61lXequ/S/VnvrD4olB3xvJ55yocdLCdH72AfjYbCmR7ztVGpQEcGINTQHOxCa73rvJl6ipmrvnlCvGfTgw19lxGHITM2w4fMRRESCixzFqiQ2gpTBiJ/vZ+wcgQ1UUVMhxDG5XmMUPkQGOQT/2u8pkAROVIw9UHX1oqZ73MKH65QsykBiVsqwvn8Vwf9zBNEYXGZjjuJMVApi5mJpxkkJToBAkkE/JGoxUfWxeVQlBZlE25fMNtTk/FQ0DZjIupZiqFPFuV63FlwdYZHec55nKxQipj6GqfYip1Vpw9FnlPeNfxBODvjvyfoyoyfgDYOQV6Bjl3+Wpk2aZ3neXbOkEoGQM0uHUaWaFjNA5zSsHwCxCiLIkM+8lgxiFSclEdfpPejdnDBlDb9vah7ogKLy1HE6zKJERw6pAqkAJIE1cMW9d97mt6Xz9fiDxuhCtHDMzNZXRYy7ClAi+fNVfhBoVKvX2ybgc7SjLIqOL9FmgXrl/H74p5u6xZyWVWOSejRpOieqw721JommBmA8X9wRVSlaJmMkKA6xaa3Erfoehfe9ON113BYcgAyiFe0fsohhi6k67z3Lsv48nBn1vBB8EmvzLZD0j2TFz31V3+lLVMHInMPyW7o90Ba64vvTCXc5IX7epp6OP9MWHx3KGhYm4UDId0W5zZtxbY+taW3MnoBAEpeFWIOYY4qpreGcPuWqVokAz8zzoau6epyjxMfEgSkJVGzJUYs0Q4CkbO2CpkYDKcqOytD4W5iFj9OZT3n62LXONQAkQlKfYa/bGvJgy7yfCAI1yDAp1Gulk4iPxwRx84blqXMFcCmtZlhL5TuExJFQ5Z/k9ZWEEoBa/5bhFfDd75kDfFU8M+hmRnEMM4l+/mLIhSqr6QxL+AX3oGxD6J09mp4BAL+NXmAXbh3IMVOJK5OsqjqweVM29ZQH0LiIxaIfJjBhKISh6VwcmIJTZChETpO4xm1OJQKH6Qz7MPKDoc/v5cGGfV2Gc5zB+lFgVxAVc+uhUfUlsTy7/TFvMMtUhAzvjY3MOLzA6T3AQPeTSKYvpeXiqRicCEYCQCAUMERNZ4SLMq3vWFq61cOHexUn0aTySiS+56GGWovoHeTP/FeOJQd8bUy+DXD3GvrKK2a4SSGLEPl/VucTmI9n8TVzvllnNRU+JQuBnpvAekdGlBLmwv45KbkdPFJJFVedQ2cxaH/ve7/fj7X3vfTgPcuHXUUsxgwzxOTIzN62ULu4LIIlZP3ohNtF1XtXwPl0ppda6rCsTTEdh2ralZJPPIg8skgOWox+lcqxdNmfWMrO6VKAUW1zDxVVimXiqfohgMJVg0+EbjqJW9LeKLJJKT2zDKY2MMstB2OjysvgMf9m2ZXTJsTUloiC1ANBcUQcfg15q6eOD5OcZ/yyeGPTdEUogb/6qzxNNysA/lzEFCsx9sHo25hPC8GMcuhAf/l8AaWkmw9StGaO+8ju6t6o+PB4RauzecRDxSajCRCLa29j39vV1vz/amHZWAIWmkXzBbHzL4vWKSdM+tMcLyUvu20M/XyPNPM+X0yQGqUkvDF68eU1JpRMxR2VHJKO3BtVU7yXg0dmAS0LOn4hAs9eFACE/jSqDuKQ6cZqjZrMvWTV/HPVzmujkv1K4EPG6LMtSmXhZauzddd8iJ71doQQQk3Q3uzZRX+lMYh9PzzN+Kp4Y9F3hPDEs5SxMnjgs6+qfMkecHwOQnVuJ45E+PuzHpzgzilA/uz+XGZCSl6CELnkQRW5kzFQqzxabmrlJqMHGGEfrj70R076PVC6dchf/byzX9m8ZjExUIE1E1MROrLxoAuZxIGmZBFIukQet68a6m4xCVpZSc+o8NJHEMjFIhx1DZDiXncUfefPvZJTnPF4sfLesCGFmzD4laybiprjGZAjDEH8PU9cYKEyxIMgC4j09q5WZl6UuSy2l+N9bH5ReQSqilYkKAWli69VeZMr/xifuf1A8Mei7wj9zrpX1j3spZVmXUiuAMMpUd96BZlwooUmC/viR5/cR/Sn/OswMMnxcg9RmsXEBoPhPwJf7XhP5PZ5U1Q2VvBBj5vvjEDURu6JJGsiCiERExVOgYIuGBZxerBHxDaeVbHHQL5c8iAtz8TyoNxPh2JmTPG4QPDwEDkNk1luT0YOJms/n0oDzqAHiSPfIp+71rGnnxK5KaKqVwTo3c5kZLH1ho74kNTHLmtaiRVZqIS7ruixLhaEUpkGUtbYQstZmAL4YyQ+htfFt3/AZ/ySeGPS9cd43DfBCgwv8oypCRKVWSkWMxVSRJZkae7Hm9fmRIYqyAqnxI0IsBPNtUg5G1WlWMoJL8CykxvAjo3yscLb3HMyHqBSq1rsCA4D/8/rq3LB/ZH4kYiUzhalymswzXSHoirPRLzIi3+BOXGI4jIkIyoyFay1nEhITI8AYvbcda5XRVMfs6yU/Nh2ErtSQn0BKqj4shNTEMycJmbUbLbKZpJoxNexx3ADFHgV3JYhdJRb+B7WWZa0UUzp8yqB8pk/UCo8RtxwD3AH2aP0Xf9j+R8UTg743suXhfw8rwugca1xsZpAxpjJxqn6uPG5+es8HBs7mmuVNnNnXFjFAIiZDVUgZbttOREqI4XPPj/IxmalW7iQJHnndmomIF16WaQ4uOQsR2zAYmUJClj2P8ke3dPr265SCywvPzcyBN27XweRaG55Oji4iIKLRWzvuxDeVbhpTIxwEMVOoztUxNag1ixZXIJ9lDZRpZax188abKkgtVj/mNkQ/CRbVqBeAGpWe+2cXELlScVuXdnR/XwD2zfSUnvyq2t0wQM3SJO8Z3xNPDPo5EV4csbQPCHEIl1KJzAeInJx2vzHnFi7tK5qAk9cpog5iYprCFoKr4JjJW1OqK3sBoQwy+MVlauDYbk7pAJtoGM1zhTnnnJsVBs2JewJiOizALveCBQlFP4E98TqQhLh9k5XgBNhoGxIRzG0sGVZrqQszYumEZlt9jNHbXhdSaao9Mq9zUQGDwMbq0/125l+WB+RJkFuwhKuvuWM0qYgWX3irTCVb/VHuUSaTTrx5G5PSPb8UH68NNxUO3yjkiAfBVwQzSTZCfVKsFBZ5ahT/+3hi0PeGOWlp0fKgkK5VuHk5sZLGaIWqmZpaFE7f0NB0YtDEpIAA0rmv0YlSACIWn2wxZbCR8yS5aC+1LxzkTq1MTBrtrZBeA/ABS2ZjtvkK/HWRW5EAIt728yUggOdPJ8icwHlyzzg5LU9aKJ2DiInLzIMIZGxWCi+1moUiRwEzApGI9HYsWzFpjuSXDNEvfDJVgs5DoilxAnDWV2oK5aiCVZRApGLKZgyLnZChJYr9PzRbb2YGc4ciR1mrS2wu89ISw2fPwC6bRNhV+t3Itxip2hN9vj+eGPS9EX0xTZsOolJKiS3rJEMAmKn7SwfbcjbO/beybKAJPoBzt8XnWmnysP49VYyuspqpKxUpdo3G5UNTq+wSYGaqCwPwPR9Rb/j6HLFuUgpKQbp8RGSl4/OxLq1OVc5Pxk99faLqrFlj5WH6bINApoVpWaqI5PbCoMlEpPdDejXrpi3KrhOyiZiMWZUo2/T5H4dE9w/RaBpoCIJ8WIyEVNSKGgmMkzsHAFMBTysmpMxCnBSC76ddYnGj2/gSUbjmZxHsSZ2LqmWoqLY+Lh2JZ/yreGLQ98asL5IBhZmNMdzSQsOtzP1E86MZ6RLOLcIJRfOyZCIXOnoC76WVjwAUZubYeOW0tOvj2IeiLKz2SoHftTU1hORTld61Dk4k+Y9JVhlcGGSeh1xw0eknu6gKfupi+vDNj6VmPCERheGpClU4OeRzDz6EYrFvGgj4Fm+HTaYmGS3vZKWR4OxleQriVkTqPkFTLWoUstIga1RExetCIgrvp3grQwRw0vu5ioVFpLVOTLUWEJUSBlG+eNbPv6oqIXa2OUVIVGvpffy7n7n/GfHEoO+Ombq4CldtiBuDFdcLOQIhhCw22RBnMSN3wiUFiukkdo3/WRBcfBodgzw/GsPgEwhkkcUYmbHLg5yf9kdnolpZDNpV54KzC3uTMBTJwjd6H6/+ZiM8fhszTwgZUITNgii/Fhc2iKIWMxUyFKZiOXslDBLngwLt1FRFVc7xO5zAifRCSyIoQA+E3DfkZtNzzaQRpuWYp4EqokQKlqmhchxKHWm06Px5RcRnVUYXXDy5ay2j+y5aMPsMLdR3Y7Q+5/v4coae8a/jiUE/M6J5YqoiY8CAikLEVMyFdk7CsCv0HIgSgGj2jhBSXCJfcg84sxCXesxjA+Rm9eYABJiSEBG0+KgBRynCpAWYk6uMWlmHU7ExGkbZDsurO/KgM2MD+ZHICOYXSZuQI883+dCP6Gh/hd5udyiNl6Gx1NAxyI3I/FEjD4pqUXUM4ksWNLkfZ9cTieIJo7hlUCGG64TgCVNQ2uAw/TFVUxGNktXfyHyExKws0czncfxGYqoygmM2T4RZCeRVeZgTmBEwhowxND4G5/KSZ/zreGLQd4d5E8pMfYmluE8Qm2839s1O09WcS+Hi6lof3cwVM8AJQERUa7jVUK4bPPEK/rOmCgxfSxNfX5ZoyxMZM7GaKYi5lOjol8Jmknt2AMxMYsKQpQur37QJ5JNlGENV9Fqc4Mfl2PU2/4H4cFI6JAmlFI6N8e4zGfkdMSONXHOnma+RFaYrUM7iMbvpkdCR535ERFTAzEaqAly7ZuoKqqn8USHhWGUdqxrPBDByrNjmCBPN7bwGSR9F331ETAx2Xw4/2wRvn1nvMkT82X61D95/ejwx6Hsj2coYRpXBTGyF/Urr4dXg+pJpOe0gFHRQPFByt/7pdVdDpUgdLqTRWYjIUFaYQQVA7rw2MJGwlWIqUKWloC7EhFKIGFkbguYQ/zWL8H/b7D0BgEuc3L7jcrBIOPqnzXqcWYkfYIw7uK0tojilkpUps9uZQA2ixgVRjelgLdmfulBCNBOiWUVFLRu1GBOpQIdnT5FinWsC/J0xkph6c2UFUtJkU4eeOifAVMS8FjaTIZ4rWThnehVmAEphmKqSW4y31sfQ58Dq98cTg743JmmSZGrUXqY6eu+9jzG8M+uWVw5AtZTkoa+MSWBQrewYBLs6op14FYSqOcUcrRhmIrAPcIhiDN8koQCpqUjs6oGDESCqnL2cmVm4vVH0d3JGv5YCz4M0Ote4bBMxO7mfeZwnqk1/QgYXrr4ZK06ZkLGfDQ4DRXi7DjF7m6LP5J0+HK2qZdvLTzsTB5O2rKUsbjftZI7qiMItrBbDSA4ugk722WDMXhmCaHojxRvtKDmGaO+5Ayo2Ylv3tT6+9M37EgJTN1FEAnphlmcq9H3xxKCfG763ODIiC3uwNmRITHzqGggU1vez5XQ+BLlRPNdaljV7LcgyA5FzuGJFZqttssTkhAuZerPMf4ENKoNUUXIgi0seL/iKPn5IUeRkm5mJwuB1aDoWOWvysdqyy2HMr+GEKhCVyotVTj8dr4yYaVmW4u6nRDaN5pF8PbyCw5UIN2hMqAT9bdnOBxHXui7rSyAYkRlG32Fi8Fl4YzJjTqo4xmc0hKPGsZ7Myy9f/AOcNv7ONMt8q0+cTO7fK0jAeh9HG27dnd9/xnfFE4N+RsQ1bMaqquyJUIhjVVVFTWsMi0clNpMgAE5eIucwauVlLetSHY8ApGkxkHoii+fKDCC/BQAuVhwAXO+nAKgQELWY/6T5AP2cYztVQ2Zmlhtv/IeLiwDEd97A1FASdOxSB2VQdv0vXwoevaKc/T6okzeluqLKQZks8wZECUQzD/L/d7wxxEuPkXdzuXgBoZS6bi9ExQDi4mfKZCRtxGTmJozxMJ6qsF3fi3hrCIA3F8HpRQeMMYaImrlDqzKRhI0JuVLR33lV632MPlLsjrP59ox/GU8M+jmRcwxKlsggvbeozlTMtCYA1dxB9uGWSEAOc9alLEtd1oCrTKziinZVkYiysojKgGq0bFx8o2YmoQ1WgSdOxbgU8gUbBt/bl7JhmonARKFoj/mRMTMrJ5YaAN+c7C88WFvzgTb86DZ/Xm1EcB0NF46a0AyqTFSrV2NRGE4HxMmDzZOEJJYmWMY5NnMJdaRpXGrdSl0NKGURERlDQYHlpNPRG+FJb7FmA0QUo2VnpZzpZyaqDsphHWAx7OL0VErAmACSAQAi2vpwxYY94ee744lB3xt+CZICrKqkjkM+1BAeNkaEUkudbBB/O7joNY+37Zelrmtd16XWWphFnU0yX1nDxdtHvr5CTOF7s/LeaqqAmZKRGDMABikzl43VFETEMIUGk5qCmw/NJovmPIiiIUQqpuJyx7zOZ2FkP3oxJ291/WIodmJ9EBFMYMoUJWqmQaRnYnXJF8187sSBLoqvKA1zY1vUkUbgUpd1+2QwXbYho7s00DNUKEyZ2YzUJBBNgw8iAiAASjieTGR17Q8x87IUJqiGgUl39XPO2fqvLLXIiAcfQ1obbh/1a3zo/kfEE4N+XiSNerZs1Izjv5rNeHYb058oVXycopS6FAegZV3WZQGBBimF6xAzxTYxZhJviZkQWOPiIYqsxB0V2a90H91QGl1VrTDLnGuIUurs7s3+m1/MyNF5J2nMEONpaT1oF/TyF0IX8jiCKIZFiOBDcOT9OzAZcbBgUaiAvGTJxChL1g+n+3z2mBEjIiogqsu6bZ/W9bYu2+32yXPI0dvoDSZiKiZkYsSAhp0Is4lccHPWpq4XiqZeeBAQwc1w803Mv8SBGSy68IQhMob4OdTcJ/CM74wnBv28SC4m/qlmJblSBJcZ67tyBOzsd/mvllpK5XWtiwPQuqzb6nocZVJlMytMXMLZj4ighgrS0Lz5Ki1KhsZcCOPXj5IIejNTMLOxr22OO78f+ZUUijF98jERd82BMzVjKDNbzddrE6x+dDYQDwwCV65LQWxYi7FaIiuZZ7k9AJ1uzmHcTJlVWTqJxcPaFEdH4425UOFl2bbb5+32aV1vL7fPQ4eIjFvvvYkMmJp20wK4qzSBCZabNc4HjzvIFB1QfiNR0hVZceZLYXV9RNBUULNuKn20PkTEe5HhavBEou+LJwb9wrBs0iPzA8pdz7kMmixNNpAAxMy1eDtsWde6bsu6rsu6SB+AkRKrAcaFSyEVI1YCUAtgJCTi2URgELmPn5JlmqMCGSbDYGAmmYrBWYoROwL5nxQ8t1cfvg7EtTEYw0q1mlNRWbqlBDnECnEy4gImME8MYi5MgSkoHAxX2gABRAoyC/Y3nBV/3E2ybIclV03MpSy1ruv6cts+bdvL7fYyRProKtL7MUaDDh1NWYgKoEhNKIhnlhIlpqn5inrKVfQ4absrUTW9ehHGcg7iKmPIkDGkj/DzTZLrCULfFU8M+tkxi5GZGPhXrlVYrQUg3+fn4VesS4eCCdrWZV3W21o4RM+qpGrM4QGkrCShkwGKGwkygwsxRUtehFTJRzKhUKXRg6KOi5q+OfAwt3BK1osxAAQUnj6nUDUZqq40vjBIkZVcpExAagkBzwLqUkAFxKWEkyzBmFGi30SizkmTgRThifEtBMV2jHl+nY3znIaYC5e6rrfb7fO2vazrrZpxb2bW+tHbYdJlNJWhPMLzzQiRVV6L43xhZKebd+6YLIXz/kFE5IT0rMQRFCFEtQ/pY/Q+eh/6UUjxjP82nhj08+LK7AJThguEuyunQLHqR1fzsMGvzgQt67as27Jt63ZbwxIHxkpqysylMgGa/XlYuAoRGReUQr5SFWZEIkKmqZwZUDFVXKbTkHI/P1L1VnXUY6qaCpmZB3n6NoapTN4rs77kZZCWGXFa/A8CFQ4MQuFS5hVfKPIgYibSFArG9EnJud6PeVDWZsF92Vx5EUbVy7bdPt+2T9t6c8M0AL0frR0y2uiH9IOoAByQFpXgzN6QZbR6BkRR78WqW5CbdcTIsYjmPP31AwEzjCEiOoa01mfv7BnfGU8M+gUxLxTzdjFSc8gUsqDzR6f4hYgLlVrcnHhZlmWpdV2WZZExWLmgmBKbb2nlWXyY+uJ6MlNm8w1ivtk5XdNJFUXhW+FjpCkDldiHJyyXBsJS7OdIpAa+vCiQmzemUhGIK+1ShyF72llpwussKoVLTQwKh1sqjMKYxm8W9AqRb8WJIdYoc3IkA+Eg9PHAPNdclm1ZtsUZtWUrpZZaVVVU1mVb1220bdR1lEpSiTudi4nSWMQskqMEuakYyjc2cjtXw7uTwaEDqfI2gw/tiKioSZwseq42/LnxxKCfHbM/ouoaXio0i7CJQd45BoC8zMilQ3Wpy1LrUkutbu/uVxaAcIQo5OPannqUYrBiSmoA+ZUNZipMadY3hbnmXmeJhrZUBFmkUROlAAb+ww5Byja5WidOfLGiSrbS9BsAOs8FJhudCFtrNfC0tK+FS7FS4Qppb4f50gnioGl4DnX4iU2mzeaSa382Llxqqeu6vmy3z+t6W+q61KV4bVZKLXVZ1nXZ2rLVZeWyltJFiumIybg85owwHvAT43x99MdiR7Z6gmNmXNgynUoI1zFcxCiuoXCR9DN+Vjwx6JdEdqpVjdjgq7S8G+aOOZpe7cA5fxRJkC+synAEslKSNPWqjcyihkABzOWCTkh7uuHqv/QCIeJQF5IZXFtXS7GFlNXElGxu84qXENmQqoENhoKsLinsijT25fiPhp08kFtjLzPnIEJhKpVLLaVUA6u6lauPiaEWKzm1YoahULflppg59WZfZmgnB4VMX/JlVi7L4kzQ+uKpUJgUcKmlLHVZlrUua61brauMg7kIUTYHg/kh+mDzjfOkBPEEMxniC7L9f5R6Jb7QbCLau/Q+hsSQ3dOy4+fGE4N+QZgZud64TIo0+vFcckRyXqHkpVGy0ctSl6XUxVOGAoulE5Mk9kFtTdt3vycXb72xu5rxxCAHOGVm0TEEg4yN2ZhLqQRSIRWom4UByZaSwTs7mQ3NMQm/WFUutZjNPCgbzjnURaERCBj2OZVSixmD4Nx6LaUUK8UKxyEbEErswNCLufVZKDocOCxOcYM320qt27Z9XrdP67pt6+a9/VrKKKUuixdofVnLsnJfcreHt+jNrsPDl8g+livJgXTgNjMR7X343h8dZsWYOcghg4j4wLJbfPyWH7z/zHhi0M8Oix6WU87eo5kIdGqjT8kJh0eHQ88lB6qlFF8YHK2XqStW8/UZBBg7hcwl4IxqcetFEEEYrGxszAKDKpgKU+RVTAzILGs0GCxgKlyg5guLAZzGgxBRGWZiMwPCTIiSD8raJv7BkQdxLVWNDGB4EkS1UCnI4Tny4/RqLF84EU/lZ9RiPobCdMlWnFTjEo352+d1vW3rzcwUVrhUrkuA0NqWtdaVy8KlMhdRlzLPQZCkvE/wQRSW0SmDwUTFifvhEG9QVeLi2gM/KDMbfXgqJPLcbPiz44lBPzvsbMcj6VQOp5xwoHCtSVw7/o1kgpa6lFK9ZCnB0VIKcrMayr1bMCLPudjbOUREoXIkgBSk5EJJ6qSCIrBirIWIjFnY3UWSXyHNwal8GR+7XgCCmRqmojozoEtRdvadiWYxRsEHRR5ESmpG4OIsdUEpKOfpgZiFejJwiZgpFgvhA97ZfB6i0AbVhcuyLNu6vSzLVutiZqJirKVEvVvrUh2DuDJX5qKzgMqkKv+cPXnMDCklCOYbMnxVk9dl5n6P52snAGrmo63PEY1fEE8M+oVhUWyZqMZou8VYI50f0EnfcF3KUs95+jCsB/za4jlx5o/N0Xj3yqCoqhZzJ0YCFy5cvGNPxE7nmFER1NnGAgMoGh6FhVk55tFy7Q9gqV1OihoxR+52kTAxkRjSytdnIjHDT8m1OwL7dvlSeHb5CURMPrjKabjj+zQ09uf4XCuRm/BTLPeaBNBsTxERUaFSfERjWdZSSy2ViNTcHSlbk5zzwpGfzleYbFhsp6XzC4g1PZT7RaLbAHLo8RuEhtk+fHgMQO+jj2Fq7C68Tz76F8UTg35hUGhn44L0pQ4hY6FZp9DpE1RLXWpM06cwOPkX5I9HNUBOFMNgRKpaSpnrwpgKMxcOaV3AFqlRPbMVi0pN1JMyGSIJQ0JssQPiTGUsb/1E6ffhdmEjyWGFT/XrMJVYvcMzj2GawMrMfs0iWtqJUxSKPlVThZO857AYE9v1NMDIiLK5GJaMpdY1RIllqbUyE/JlcKSinLYFpynKBR5mBTlTHtBUHfqMW9BCYWASrlAwZh5jAHN6Xl2UKCI2V6rRpcH/jO+LJwb98vD2reX9cQYw2+XEhVy16C35iT/nbAJlZz2rEDIHoJwIExRlq+GZz3zulba4+6bI5RLuoSrh1ezaHREigRJ8DbuGcC9Y7zhy1+moeOsZKp4BRQtNxURNYwPibGkRl8iDghFTInaLa2/hsQsUzVHbILHSZ+4/ilqMlVyJaEF401k0EjNXH5Nf19uyLCV1idk0ozMPuth5ZyJ0GaGYNaSfPTvHbqdhpZLBxNdmjCEi5mma8z0ibhMyPBVS91X5vT54/2HxxKBfEn65qqoqh7lYdpgKUrtHofRhJqeiywcE4okOADCvNZegwKbJKgxWDICQmhqX9OXxRChY3khjfN80obqUcXTxuddBkp1xkeRbJgaFzHpy0mYzD5IAo1j8pWJn6pdPnvuLuFR3z472l8WiNGQeROH2bqaGobl4g32LEUNVT+P/UB5EI84sTU/qsmzb9hLlWC0z6ZypkCdB7mTJaeF0lmMe/lsuCzrVBghAhDFzpEWAiLm/bb7XZmZjeDvMYSjF0c8k6OfHE4N+YaTJxhyOj97KlPkg/dtnS95vzWcahJOOoZP7OIkFTgFg0sZkUGc5cv47ru34LTITdca0Vq6F3XaarscEIKRNkWd4e8pUkcsONZz7HXQizVOxMAwMbmhe9pEKRQ6UPJdf86mNCtLefXVU4alQ+tEHJw0Q9COx4mWSzfWNhZhLdUpoc5VnyqpPdXj6x1VPiIKJJvomUUkaDETsIs541wBjd4HkRCT0PgKOIxOKzr0DUG/9OaDxi+OJQb8wrrJ+L8hEREqpmnbxuWOnZgXmH/pp4fUtSZHL7C+daMC8nOHswkXVc+WbiMlMXctYqhXRYqVUKkw1NkQ7SBLN279lxym4aOeWQGykaQhkMLXRvTftABSXYFxu6ROQ4gTmLH6MicmbR84ZRRfeFdKicbmf2Dih+8RTAuZ8GLFRqcuybrVu1bVVpTp2eVHpEfy2pWukk2F0UkKULfgzrm0ycjYMMO8SkAqYyBXS/rLFF4glTutJ1j8B6BfGE4N+ebgVsbmltLAIi4hoIQ1DHL5sOkQyvsir7sef2is/PVvJnLwrAA6jsfljQYFkNWFVVKUS4Ld/LiglzaGjue1Pb/OJvANv3sNXEJ0T6yomPRivaeA/KWry2QlvQV24dmJim2sTcVZABDV0McmW2GTEAYTaGglNTL4pHmbMBVSWum7by7Y5E1TK6VE5RUrwU5NSQ1EZphrZzuXH4umiCYmTvXPAZBoOL4DrfZh4WepxdDMbIioxoaqxbgDM9BPjrM/4vnhi0C+OKJ38djiGFGYtoiJWmMLPLMqTy5WWFMxkWmHZCpotm4lBBFhhUqKSV/O5qcyQEMTI0XotVqovpCczY7Fo47s6yHvhsw/kYwliCEMh+FA5JT2lYuMDBqXpmWOQbwkLefRZiDknzcwgjReS+GuGIdbVDMTF9wyejbD4W7TJo2VooTKvpS7rektNUHEZ+IlvdEoNYAYjmKl7CCTdTsQWxd5ZOZ/3Bn/CEHx5v9G3J/myRs7aTYfIiDkxby9eAO4ZPz+eGPSLIz6RquQ+nrUWidtnGHhlm+i83WcV9u0jXb8Ns1mYxGVoVwwDTv4jpDkAuV2qVl2s+q+aqhbTVP4RkTGZMiH2/3kmJhCL1T+kcwaWyMwXB2XnL5MgwF83CFQKlUyF5qwKxfYvmsfvB27wvew2xF0eKac0cIp3ZtuKiJhIya2xXXu4LNtt+7Qum0sdiHgKn8+aKnfrOImlIS5QzE7j9YYQhDcQAqWgp5iZSJnYjCS8UM5qWkScCRJHIZ2qzWf8knhi0C8Om94WZuZiHFW/BX+owuZ1SNkJB5LFPj+6SY4EAAXXwaCrRwcuV40rVqL48cWhQNWKvJZFiNVK/q4yQWOXu6+TzyWNLjIwZfPL3o8G3ol3P6JYLjTLR0tDai5cQh59su2sF+1jEFCAt6GGWh9q8K2EM6mD2awTgeStImmMibyyLOu2vazbbVmWpS48y7FUNtB0jPVzpSoOIfax8r20yM58FBSDs94NI/FHcomQpc+knzcR6WOM7tro55jYvxVPDPrlcRHjcCnsVypSG+1k7aSYrw2z6KPn41BeaVmMZIeYiJl8DD+e0ZU8zjDPNlD2ts2xIX/UbJTCFv5nxEqR+2is2VJVNoNB1cdc9ZJ/wQw6stbM9nwesP8ZxWYJcVBS0kyxwZQms2yBmwZR62IWmVIoE/yIA6FTMBV5iRLImfhSl3Vdb+v64sZL85TkMZlzNxNjzEykq8uZTC9TGcCpx4q30j2UPA8qtXDuPjQjtxBwzPVKVkVliA+yPifE/s14YtC/G5c7eXTHeh91KXUpqsVvn+XKmHyozswh6eRmr5/nZGeTMqIP3/O6g0LGwlnAcInUhkspcXkqQcyTIFUupVYFYEqxNRFZYcWvej+fMZkcgAk+3DohAnBkyJf2UXiZcOaiR1JDH54wkJ5WPqdewGxmI/4V31hdSkF1Jmi9xawdF0q71TwVIdcSkTF673s7HmO09IrGJJ5g+YuUw2mBROEoqaqqgKH1se+ttTFkaKzzHiJDgomem62f5vX/bjwx6N8Nz4MsXPV0jNFHWUb1So18zVbhUucoQ15kV8AJZSKAbBNj5kazoTNLFZssbyZMNAGLUbzRbgZUv8QFoFAQ+HKIWgD4DtczufKZEzOXCLBv5vnQrLOskOKZ55BYcTRKGJo6gPxJmNkQdWWQGnLBO7IfnznKpVqlGKDlUpZlvW3ORpdai2NQmNBOatnMhkuXe2vt0fshOghn9hPslCV+JOLm26hmSiBRVdExxtH6cTSfxuh9JAzFVl1XJ+CpkP6344lBv06Yua2n9D5qrdN6J4mhc/EYzfUVF5YHF8GRL7uaqT/yYp2znIYLAiUYwEXJxGHF4fwIE2VzSpVMAKBUNvPVfdfSDebT+u4nFMhB14vXD/b6tB8LMWbvdWWPibO4JFcGiS9wJUWKxGc6SKctyBQlxOEXLi4N2l6WZVtqrXW5lnBXdkdV+mhjtN6P1nYZ3UyRK6Uz2cO1MW8XEHKVlKmOISra+9iP1o4+Lpb14ZoYdfcTfH6FeGLQrxCzChtjENG6ilsQUnC24fGXVdjklbO1fCoTMbOBeR2bnbfxD89K5IWYX+3mtoTMyCSIiciYYkCdNJ1fTRlmyicGUbyGGMF1tshrqEjQIjWbTE2Uf/nSIgmKJuAFt2YqZGYSU69QcyNnOtE1azHkV6Nw8u5iCcOgdd1qXWpdLuKgD6fD1IaPT/TjOB5jNDVRkyCb4n8aPok4MYgIMY1CHByY6RijtX4cvfU+uvR46BEe0m4/8MShfzueGPTvRuiIzQD0TgBUN/+nD2rk8vnUzoDio//NdXTtkXmTZpq6YuZBk0WyCQYBParBBlkIf9wLVkUIkNDSmJmVWghQJY1F9akGUohaDQw6K0NPTCx32szxLr6QQScllK0uOptaxO7fGnmQP1S+0CCQYFkkZSUKuKY8TMuSElrWpdS5w/ZyxoyZDeband5bb4/WdpFuJimYztwnhFkTgpyVM6LwaYqpFNXe5bEf7egzD+q99+4pr6Zg/Bn/Vjwx6FeIUPF7pVM4tPtTgTv/jIzgopimvFQ/BqVIj5nVJJmhCzeUD5fTWu4Dy76pLCef/KkvsBUNNQhASrnmxlkSiDgAmbsO+UuYR3nWf8xEVM5tstdJ3CjEgPOQvb3leVBs6ELsXZzp3kzFkvDGzKJyY1KtdVmWtZQac8AfzlncBrwlMMb8X1ORsxybgDr9nrM15hSYqQoRGbwQ88T2OPrRuru1hnt9UkK/xmfnGU8M+jUjLiBVyzS+3R/FCV5iZipUCITY5zXVQ2djLXUxiATB5lUSj09Iv46YBHVcO+kkylomEwVTMwYzrFD1sk7MH95F0UYEqKL6TLyZGZhFw7IfedmqaXDVoYz0tCfnUaNJB5rHcgVX9b2JZyKVYxyJlB8iiTIiKrXWupZUIJiZyLiMufoNQB10ejta23t/qHY6CWcHwmkyMGXqMwWLd87MRh8wOlp7PPZ9b611kdFaP2XR2Ut7NsR+rXhi0K8TNFsuMBFpfbSj78vhg1TLstSqWGbnhzJrmIu9LIjkYH9pCoaiXrjc9ylToAlhsySIOuhMJQBjM0MpIJDAAQjmCxQDJpRIFSJW1BYDjJjZ7/WYTTwf7EjonP6Q8beQM+XBGJzlmgdsBhG4pVp4e5z9vvivIcw05m+BqHBd6splst2mKqq+Spvz1JnIaP1ofW/t8bExP3fJU3Js3/T1zyd04c8Q7W3se3s8jtZ6H6O13tO2/iIWfzbEfp14YtCvFv7JVLXeR219r4Ur1aWu6ypDPNtHllAlqZOZELg65yoUdtMvf+xEL1zYIu/zA2dvZ+Y/lMIcCwwKZ0UyoBj8n5r1EpOJmudBAIGYh7DoEPWdzAYjJYVOM4CrDvMi12Z4u8/OOhSZMohiSMih7QJBSRXPiuhCg/us2LKkKWKB2zaZkBWv3fypRKT3o7fW2t7aY/Qjxvwdv51ep/nAl+emy2ESDZPRZQw5jv54HPt+9NaP1vqHVOiZAf2a8cSgXyv8SjefHUuXZdq2zT+5nlzMltJF0Rd8RN5U7WR6fOgJ87LJjtMciGW2HAgDJgRNsnpSvUaAWnTZzMyseC/OldGmVsWkWK0gKJMOYiIBKaDpLu1iP1yH4LhQepDFMxowZzpmnuUJgxjEu4Wp0byy8HYFhstXiUstjkHFm27ehJziKT91ajp6b/3wrnxrD5EejgEfH9Nx/0yFZuqYJ1pFVLX3SIWOo/fWW+tjyBgjxUFPEPrV4olBv0549aGq3jXxi6oU7n2IaLqa8hWAMom5PMKHWzJFfmJ2MstRwxC7qzSTKJ1tbSRNQjHn4QR1iA6NzNgb0CjQQDsFk/slVolZB2YjEhAZBMQG9RFVc7kT+2aheQyXNIgiCXJcuDI9PqUxNJKvIGjiqDFZpLBQylPhToy+P7Wk/b0huGM2cscxh/8ho/fW2tHafhyBQaeKh84a9UPyg4tMCe5pb6oqQ1rr9/ux74fLjWRce2FPFPrV4olBv2akswVGH8wksnkzZQzxz3SpKMU/9gaci72yYxUCPb40koGAIMoqi086aUJTNOtxkQV6jkXxGyG/Yy0cdhPOlBDMlMzXEJpBKZpTXnF4k81ybTVnRywTvfwbEeWm1JnRJZ8V/1QjUUvayA/Wztd4xiyZKDwLo1BTVVErEDOLxUBmUBUz8S5Y78cYTaSPcYTpbDDTSdBTOEab2jzCeO9C5KV9iOsSRx/H4Xy0jO6be/RJAf3q8cSgXz/8g+2XwOjj2Nvjsa/rYoZlXVdfwbcuJxmUtDQlPUN0FmJms8LCaZ2aaZQbBWVJ4g/jE1/BInul5zw4EGw4nFix2OLKbhHr+9R9p5mZalG3UU0Mco4ppt6I3DyImd02O0tAP4KTmnImyFcBIZadfqCj7ezKZwI4H8sdSETG6K2343iYhVuAmxmVUvpoY/T9uLfj0fuhMrLlPtcXTlbaeTJmIsRW16hWVc1XNrdj3O/H2/u+70cfseAwHi0qticI/crxxKDfJBwQRKS1vu+HG9qL6O2mdjMiqrXG/fmyWeakc7KumT3kqLEw+aSoGoiIiR1NbP6CwU2HLCuHhIegkY3nNWgwKIjZuDhLpJTG9QozI4NdMYiZyuyDJTN94s+solL8Y0ZeUBrIcLWApGCBLElzmv38BGOQQ5CMMfpxHDsAX97jUuZqtbWjHY99fz+OR2/78PmMj/KhCXpEFlscA0m8iFPfkNKOcX8cj8dxf9/v96O1run+8SOu6hm/Wjwx6DcJV9M4r+kDnSByKpOZl6XOLtjJzeZ1nPIZSp3j5Cyy5plrvNLFJ342ryvKfr7ZeQN36oSpKBsXUzPf8O6kuOdBAEhAUFXWpD00EyYhcCwLCQy69MRybj8FTcmew+B5EHLN8iUPmvWmKVw1NdEps0GLSbzReytlJ6JSRLRWra4M6v3Yj/u+vx/HvbV9jKYqs479mLmchSpn7gkzVQjUzPoIHvr+2N/f92NvYwwZomkv8IzfIp4Y9OuHWdhctdZnS97BpHBZ10V1CwYnrrsoGD7WIZhKFApBIqVxYq4ntexzX7Qq2QfL9MKyz88MNWJjKzBEKuQAZFTYrFiigE+3OwYaGdk5B2+U7mM8DfazxTdxx48l2n4GH57zVCioLQA4YSBp7CTEwgkggDiqsdFbOwCqdSk1dw9Z7b0dx+PxeNv3e2uP3neR8aF7fq3ILtlgHJyZqg0hEInYcfR9b/dHe7/v98fejpjMSNv6Jw79+vHEoF8/zFSEzGyIjiHmiT6olrKuy8u4WRoGJXjk/XnSyaGyAa7ANPtPsx/OPpU6V9Xn8Hf0wy7CISIiBhvngDqzafTEzQBWZi3zkHQCB4zMLhiE9Bo8CzGnpdOzbEqVMLltydXQZ4lEMx0Kwurks2briohiglR0jFG6U1qqWs41Xz5Zuj8e7/v+3o5HO3bR7jbSOCkhP6TgwGdvLzFImcSfqw/Zj74/jvt9f3tzA6HxHA37TeOJQb9+eOIuYj4f5bfcZVla6+3oPvd47Icu1Wqd7IT3ywKLfPwCQNYm86rkS9oxB8UuT50uZN6Udi4nrTIQKRWbmnHxPIgARRiepZzHAoMAVfe7B8ITyIjgezMSgDJv+5YyyZY7EO7VJ9lz/VnK1lmQXZdEMLM5VdHBY0wNpO/TURlSWEb041vbez9Euui4lqZXIZJXg0mTkaqa6BjSWrTA2tGPo7c2eh9pHvQcDftt44lBv20QYkpeRX2C7HHfl7Wq2rYu67rEbpzCgDtS5DS9ncPelxJl8tHuMmSX706No+cHsSwDSVfPuo6JjdWMFLEpkc2MgRKNKwCsVIoL+UzUWA3MRuYpGlE6p52NeeAbEEqgSePESMWuiHkFh3h1KX88f8Jn2GX4zhJl8VJLVYnAZKqjtccYTUbXaTf77buQ//FESG2oEGlvvbXWjvb+vr+/P16/3u/3fd9b7+OSQv78d/0ZPyeeGPSbh5mZ2hjSWtv3WpfKhUYft2273dYlWvWVSwxAzY4NgIuEJZKEOSCK6TQ28yeLfMPM0n7D5hSI5RpVMMiYpzTJAEbBWRsZrCjUjM1JKVVmNoNxTn5OyviiDPoYWWr6PG00yfhjQea8+bVT5pXd9eTB1FRURGUoFxUBSFXBAhOYmIzjePR+jNF9Sv4iUbocyeWUWqyltONo9/v+uO9vb/e3t8fr6/3t7f54HEfr7rb0rL9+h3hi0G8YTkaH8q132gM/zLR338ogt5uCUJi0Frv4fWRxgnnZctY/nn7o2UkDTviAAVAzsnmp01mUuTFjFCMMhAIRnC06v+wKK3Irmft6OT7a5HFmYTin4OnDFXvJi4xyq2q+HgfM7KBFlQfM8bMzazP4yg/VITpIC2nxHp0Bpl2kq7Rjv/d+jHGcc+12PQ7fvDrPBZCbmlsbj8fx9nr/+np//fr+9fX++nq/3x/7fvQxgoV+otBvHE8M+k0j2jpDBhrcXFFUY/rRdx4T1VpkqT++5V4rnJTmpFsGcWzeInwob86r16JOm/RHih0BMLGxzQyJ2JOkYJLYjAuKQtnUQGoJHjwBj04QCt3zTG++YYYMpBcmaP4Z37RUH5zSp6Kq5r6z8GEwtxSLWgwg37Us0kbbe99733vbe28z8fs2D7r+xU+qmgwdQ469v7/vr1/fv3x5//Ll7f2+3+/7I8VBT8PW3yGeGPQbhtdEZqJqMmQUHiKt99G74xEzLWu9yTYN8JHX8NnDTlVNyKPPPpk/Sf4S0kXIcsKBfOgDTuL6EJab7pgZg2320Qw8qWyz4l9T8/YTM2UZl08V9djMy+gKg3FYyORn1mI08RSpj4R9GCtzm3z32z9X5sSUhgzlqiIAiwzRMfrRjkc73kffRdoYbVaH11NzHk8mQX4gjmyt9ft9f329f/36/uXL29v7w2npWF94OnU847eKJwb9tpF5iYhAhByMAJRSai3rumxHb1tfltKX2lqxUE6DCXxSuDYJaTq94gmpLb42prJ8MBDInBN3pnmKFcHMwQOpGYzjd818qtUKG4IScvPnc4TLX9Zs1E2NwAcMCrwJsJlPftUFTX449opNUQIz28VMlgKPPBUaogXEKkOky+ijt96P0ZtqFxlExGC3+KDUjE+tAjRqKzNqRz/2tuf/7vf9ft/v9+P9bXfTaP1pdvsZv348Mej3DMoxdpMhrY19b7U+mEnVd3L0bVvXbV2Wdal1qYv7DJXTIvXbm7uzw9kmNyJlpvSW+CZjAi6UDRMrqZ2DIgQgFnr5haooyd2euuezmMpWeiqkQXS9al0GbpEHfWiazTwo/pmj/pfvOodjKVTIx1RVGQCpDtMB89YY51JF9sGTZJoMgIbtqugQGSpDREyGHm3c78f9cby+3n0sYwyZNevHhv4zftt4YtDvGwYDVLQPqa3v+0FuITpkDGl93G7jNmTb1LaNQBWFqMwuFKKmmGzzHCCzCwDFVUt0Jhfxq6mbRozSW1xs+eDMDCtWULwfpcUf2ZJfPxEh15CmRpEmKX59schm3ceGfCZGZ/v7WtCdhBKdkGtR2vr++FgkL5R2ImZsxLFMVfPhzURUfB9G6+0YvfXepHc5jn5/HPf78fb+eH9/7LvvDktl4zN+x3hi0O8ZmQSJ9j68pFKJpUBjjJ5OfVD4XjJmKjYbTx85FyIyuFLajJk02JnsAU0Amg2q6NBPRlkvGZL/JKPkjxY1VRNlFgJ4aoey6nO2h89UKA2BLlcwTX74Qj3/iHqPVCgLuvOIv3GbdmEifO2F6TAVmPnYmhmbsu9xDQ8BdRm1+kD8/mj7o+37cez92Pu+98fjuD+O+2O/v+/uVZai9icG/a7xxKDfLyzFioOGt3uGSGs9l8bE8jwAhcuyLLKIamw8RIIQXXOK6E0xDMJMrBzAQtkKv/bWMPMgL6KUdFY+UfSE0idG9lmtiApzAAddZkxcqojTP+hUB+TB5e9EXUaTgU4i2g8pa0a+guy19MySDoCZDjW4StpUACV3lVRWYjorMTN1GsnL3n4c/fHY39/2x32/330w9Xg8DkefFk6J4+pa/4zfJ54Y9PuFwVRhUOsmoqMzH62U4tMbow9X+RbmbV1kbCpiVi+0zqVEAXz6iYiZDIyTr46VYedvnTqcyXZQNtTn42U/iQBCMCNaVNxXKORKOR4LZ3+yNxZszjVNOwEowWke+E9d35EHxfK1hLlzwIOyRPI+ve8jhOlJCbmRgFAAkCFmWsVk6GjSWn88jvf3x9vb4+3t8f623++HI5FEqCu53Iju13rTn/HfxhODfsfwWS4xBQDXv4CZ06jYiIkLL8vycuthoS6LL/2MJaCu9QnBDqIpxmSGD3OjH+joMxPyWgzApYM9SaMIZ4Y5VEIShmHR3bJsw/m/PQWhOeR18Ro5W2GGD3yQ/egvOZKSYuvkhJH13gf5E1Q9AzJF9O/NYYjAALsSIqhoURk6usSUzKPd7/vb2/316+P19f7+vj/24/Foz3GwPzaeGPQHh19gqp4ZjeNoj8exLA/mMrocrR9H27Z1u63butZa61JLKcEVnRvGQESFWbkoK59g9K1s5+NTM39U90z0csGycilFpUikUNGkvxRJRNmY//aJTmC7MllXaAlPxZPscRo8qyHCCY7XHC1UAtnrdwWWiegY0tvoo4/u/JqMPsaQ+/1xfzzu98fb2+P+OPa9t5hEtck4PeMPjCcG/eExLXKk9VH2VsvOTKbmVurH0W637da2bdvWdVnXZVmWWmtdSi0FhcosiYq5BYcvSv2GxCakkWp+wdMu6KR+45r3oXiUIkWZpXB0yegn8qBvy0R8fEbgJ741kzM/IqQDm/mJUEHKhs4cDUjqCEzQePh0WVN1DPLB99Z6O5pTPK2Nx2O/Px73x/647/f32Fw4Fxb+9KE/43eMJwb94RGX3hijtbBrVdXR5WjtOLbjaC8vt9bHy21st02Gyqbrmo7N2RfPPMj/V6Yi+PpE+ReaGU/hYrHVOVkmL4sYBShFSilSxMKQMfMg/22XXtM1R8knyIyFkkg/O2KnrsB/iOfBBQjJWSUm5sw8yE4+ymu4GP5y7llHl+Po+37sj2Pfj5Qg7vfH7ntTXZfomwt9X+GT+fnD44lBf3AEwyHU+wCgqjLC5eN2bMd+O47c8em7ZTQ6W16QzTFSZgaBhd2fK5d8pQJnPt/ZGicmRgmmSaHzqifA2RnmwkW4FFVln3rNwsvMNdjxCw4b14bSpRYDfXOhX79z6by7pSEgdOLqt/VcMmBJOAWDlIqHVH6+3/f7/fG4H/f7/ng477OnedPoXUaAkD5B6A+PJwb9weFJkNciIlr6aKWXUtZtuR1tv7XmZqK56zzImlKWRdUUkbeACpOSFhVhZo4B2W+ToROACL74HUpqUMjZ8Jri5BI7NNximmB6stZE54z/LK2uEBRfDwD5Ni+b+oHLLyUlRACB2Z0+kg9iyp7ZWWPSCV+qENUxJHYT3o9373+9P+7v+2M/9n1vrYto9r/sua3wTxJPDPqDw8xEDNAxUnxDRIR1XffbcbttKVoBEOhTa13WxVcnelCsGrP0mPf9zvTTPeaspYi5MMHtOSbHjCzwYG4XXUqBUqwCmolN7O04H/RbYuVMhK4dsMlKX75hQO4mMjM1H44L8bQ/MoOuOd2V1cJZxJkM7V3aEW3419f76+v729tj3499P1rrNp/ymf/8aeKJQX+iuAxZeoEmPj95HL3WPb2HbN7wj+M4jtu2rbHuC/CZD41bvALgLK+iR5928dltonPUIZOWs1IiLly0xKocs8v0FgWifKPenq2rmVCBaHaxQj55rgKaWBAvnPKRwycxchwjkEXXzLU8I7U82to4jn4c/f7+eHu7uxvZ/b4/Hi2In+HiT3sKoP+c8cSgP2NQ7JOIhn1rzdcrm5oMX8XXHvv+6eX28nJbt7XWUmsBkYuDZ31BDMuJ1pngXBbEBwgVY6oAUh6Y5ZhzS0UNEBjE1AurU/zz8ZgjtZld9Xyo2OhKmdJQioGM5tCs5zLnQ1psUFUxkJghIDkGW3wPqowhrfX9aMfeHw+ffX/c3x/v98e+H1cLjt/pnXvGz48nBv1Jw9LgpvfBe4NBRUeX1vt+HI/Hfrttj5fby8vZsw+XeqIpGyyFEdlTToV+xB9K51TKlYp2AgccgiI7gy+dj1JO8WFlBS5N+Cz1Zs/MTvHztJ+OZCkSn5ij8yVjnjhZ+NGqiSlUbXTn5kf3yNb7cfTj8P7XsT+Ox9kUO5zOTyrNt488wehPF08M+pOG2ygPFurdYKLaxzhaX/fFEed2215etpeX2+1l227b7ba5PT77H7X4zkK4++HHIuhDyUVcmIxM3Trasmgjr8WAAjPwmVwBMDbSb6/nwJMPsqGpRUz3a545EpzYviZCMZsRdqxkBoj4ah9N1Y8Lf1o7PP2ZHkDHvrd2tONUBvXeu+/meXLPf+Z4YtCfNMxUFOgj6q82HGJqrctS61InBn16ub18eumf+rIs1W2Haq2msErwwfqY1rpuzrmKkGPWlFQFBJ0gwRxTYD78eRI3MMAY19FTQs7DwhCjbJR6xvAHQSZil1eZXTPL1r7LBi02iMFrzzFkZjcONGFH/zge92Pfj8fejv1offQ+hqt/RMZw9kh9QdhzCuzPGU8M+jNGFCIqSjqGXIGjlOI5zstte3m5vbxsx+fPrQ8RWbd1W9d1W2xVYI29rKq5QZXmjPtEnwQjDu9FddlPYNDkr9UsLmMK2nx6cgCe6ZxHfjb38mHIKBdyZAcvYW+OeUQZmCtXzTNBM6flW+v7fjzu+/3+iKznOO5vD9/J83gc+348Hi0Zo5y51VOAnVz/M/508cSgP2lce2TXcIK5lOL6FhFJBgZbH2MbQ1bvjo2xLEsdyzKGLLK4JXyt3laSUmupWkqhtKoXUb96yTe3Al5aXXdL2FzMHIbPNltbebUHNBmrKlQpt3hYbEMjgGJjkIY+UqMjBqMwRSM1EzVR7a0fR3Pnjcd9v9/3FEAf9/v+/uYOZF6LHVP+8zu9Sc/4NeKJQX+9CB8ijUHN1nrZDyIaY7Tej6Mt67IsdVnqssRf1mVZ5qBZra4wWpZaSnUdjrfEHWv4zGEI5mtwMr+YUGOZr8z/839bbkmkWAdEhNhOoaEIV1iYZUgaAmiQUQg5EMU2H5HehjNBk/Q5jtZaO46+P8KDNRWcz1TnLxlPDPqLhRsFIjdkcRnH0YhI1VrrwRbVMmmjpdZlCRp7WZdaS6l1WZZtXddtrbXSKRRKN8McxwcAxPDaGCIqZhcMQl7251fPnj0TOZHNHBAVPXuGqI70bAvVj4TsZyq7VbW7v20brUUX7DhaO3rrvffR2zham4Msem6Ef8LQXyyeGPTXi8yDVER6JwKZWR+jllJqqUEYlYlEgUHbOtOgdV1ut9vttk0M4sLVf6dwrnINvaGpjiFDRMXnPzQxR8+qbGJQyhSZYlWha5yDzCYDTESOo7fWxhimZqYi4qgUSh4zUXWCubfefH1Guk26I8cQGX20Nnoy0E/S5y8aTwz6y4W5dFhEATd41yFSmnsKcf5BpdZay1LrsnrWc5p+bNvmHrLLsrB7D9Wyruu6ainFH2PSxabmVJEnG7loIzFIgxqaeVDIAFjdBy3tYREOAWZjjH0/9n3v/cQg99wYsd3UhkQGNL1uR1ZdXsSJ+v5n8a86Zf5EoL9iPDHoLxZzrMqnLp0SYu5RUfHZ9yqeB9WyrOu2LpvnQUuttd5ebqMPEV2WxX9+WaqKmtmyVGbWE4PILBa+XwwdrxiUmwANU+DDIGZSYzX+gGaqatp7d0a5Hd1MYTaGHEfzkS5nqsW/0no7uqdIvnsniaPMwPwR9ZKUPVHorxZPDPpLxsXzWP7Zz3gt5pXXtq3brMWW8qk13zRda/Wya9tWA4jJzAqzlMInBkHnBFpe8oA6pqT3oc72GSP3UhtbYpA/sqdTs9G+74enTyLiYp/jaJ7UjCH74Qy0i6N98c4z/gPj/wd4p8aqbb8ohAAAAABJRU5ErkJggg==\n", "text/plain": ["<PIL.Image.Image image mode=RGB size=385x385>"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["pil(in_im[0])"]}, {"cell_type": "code", "execution_count": null, "id": "3b54f448", "metadata": {}, "outputs": [], "source": ["pil(image[2])"]}, {"cell_type": "code", "execution_count": null, "id": "e7079605", "metadata": {}, "outputs": [], "source": ["x_inv, y_inv = DA_grid_inv_(dist, 128, \"polynomial\")\n", "# grid_ = torch.cat((y_inv.unsqueeze(-1), x_inv.unsqueeze(-1)), dim = 3)"]}, {"cell_type": "code", "execution_count": null, "id": "9d8926d4", "metadata": {}, "outputs": [], "source": ["x_inv = x_inv[:, 15:-15, :]\n", "y_inv = y_inv[:, 15:-15, :]"]}, {"cell_type": "code", "execution_count": null, "id": "da9112fa", "metadata": {}, "outputs": [], "source": ["grid_ = torch.cat((y_inv.unsqueeze(-1), x_inv.unsqueeze(-1)), dim = 3)"]}, {"cell_type": "code", "execution_count": null, "id": "e00b5556", "metadata": {}, "outputs": [], "source": ["in_im = nn.functional.grid_sample(image, grid_, align_corners = True)\n", "in_im\n", "pil(image[0])"]}, {"cell_type": "code", "execution_count": null, "id": "47123656", "metadata": {}, "outputs": [], "source": ["res = 1024\n", "cartesian = torch.cartesian_prod(\n", "    torch.linspace(-1, 1, res),\n", "    torch.linspace(1, -1, res)\n", ").reshape(res, res, 2).transpose(2, 1).transpose(1, 0).transpose(1, 2)\n", "radius = cartesian.norm(dim=0)\n", "mask = (radius > 0.0) & (radius < 1) \n", "mask1 = torch.nn.functional.interpolate(mask.unsqueeze(0).unsqueeze(0) * 1.0, (640), mode=\"nearest\")"]}, {"cell_type": "code", "execution_count": null, "id": "537379d8", "metadata": {}, "outputs": [], "source": ["in_im = nn.functional.grid_sample(image, grid, align_corners = True)\n", "in_im = in_im*mask1"]}, {"cell_type": "code", "execution_count": null, "id": "fb1790f2", "metadata": {}, "outputs": [], "source": ["pil(im[0])"]}, {"cell_type": "code", "execution_count": null, "id": "d36299ee", "metadata": {}, "outputs": [], "source": ["pil(in_im[0])"]}, {"cell_type": "code", "execution_count": null, "id": "3e4d39eb", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "# for i in range(10):\n", "\n", "\n", "_, ax = plt.subplots(figsize=(10, 10))\n", "ax.set_title(\"Sampling locations\")\n", "colors = ['#e41a1c', '#377eb8', '#4daf4a', '#984ea3', '#ff7f00', '#ffff33', '#a65628']\n", "# sampling_loc[0][0].shape[1]\n", "for i in range(xc.shape[1]):\n", "    x = xc[0][i].detach().cpu().numpy()\n", "    y = yc[0][i].detach().cpu().numpy()\n", "    ax.scatter(x, y , color='red', s=6)\n"]}, {"cell_type": "code", "execution_count": null, "id": "69a6a246", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "# for i in range(10):\n", "\n", "\n", "_, ax = plt.subplots(figsize=(10, 10))\n", "ax.set_title(\"Sampling locations\")\n", "colors = ['#e41a1c', '#377eb8', '#4daf4a', '#984ea3', '#ff7f00', '#ffff33', '#a65628']\n", "# sampling_loc[0][0].shape[1]\n", "for i in range(sample_locations[0].shape[1]):\n", "    x = sample_locations[0][0][i].detach().cpu().numpy()\n", "    y = sample_locations[1][0][i].detach().cpu().numpy()\n", "    ax.scatter(x, y , color=colors[i%len(colors)], s=6)\n"]}, {"cell_type": "code", "execution_count": null, "id": "1f57ba5b", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "# for i in range(10):\n", "im = Image.open('dog_d.png')\n", "im = im.resize((64, 64), Image.ANTIALIAS)\n", "\n", "im = np.array(im)\n", "\n", "_, ax = plt.subplots(figsize=(10, 10))\n", "ax.set_title(\"Sampling locations\")\n", "colors = ['#e41a1c', '#377eb8', '#4daf4a', '#984ea3', '#ff7f00', '#ffff33', '#a65628']\n", "# sampling_loc[0][0].shape[1]\n", "for i in range(sample_locations[0].shape[1]):\n", "    x = sample_locations[0][0][i].detach().cpu().numpy()\n", "    y = sample_locations[1][0][i].detach().cpu().numpy()\n", "    ax.scatter(x, y , color=colors[i%len(colors)], s=6)\n"]}, {"cell_type": "code", "execution_count": null, "id": "69f09a58", "metadata": {}, "outputs": [], "source": ["(x[0][0] - x[0][1])*(x[0][0] - x[0][1]) + (y[0][0] - y[0][1])*(y[0][0] - y[0][1])"]}, {"cell_type": "code", "execution_count": null, "id": "e5978e32", "metadata": {}, "outputs": [], "source": ["d = 0.5"]}, {"cell_type": "code", "execution_count": null, "id": "918c2c00", "metadata": {}, "outputs": [], "source": ["import random\n", "eps = np.random.normal(0, d/3)"]}, {"cell_type": "code", "execution_count": null, "id": "2baa7cca", "metadata": {}, "outputs": [], "source": ["eps"]}, {"cell_type": "code", "execution_count": null, "id": "6a8bdf22", "metadata": {}, "outputs": [], "source": ["a = torch.tensor([0.0, 7, 0.5, 63.10926839658489, 1.0, 119.21853679316978]).reshape(3, 2).transpose(0,1).cuda()\n", "a[1][0]"]}, {"cell_type": "code", "execution_count": null, "id": "9f7ac3af", "metadata": {}, "outputs": [], "source": ["sampling_loc[0].shape"]}, {"cell_type": "code", "execution_count": null, "id": "0fc8c29a", "metadata": {}, "outputs": [], "source": ["d = torch.linspace(-1, 1, 8)"]}, {"cell_type": "code", "execution_count": null, "id": "f608e787", "metadata": {}, "outputs": [], "source": ["meshx, meshy = torch.meshgrid((d, d))\n", "grid = torch.stack((meshy, meshx), 2)\n", "grid = grid.unsqueeze(0)"]}, {"cell_type": "code", "execution_count": null, "id": "f7417f5c", "metadata": {}, "outputs": [], "source": ["grid.shape"]}, {"cell_type": "code", "execution_count": null, "id": "c0ac7fbe", "metadata": {}, "outputs": [], "source": ["input"]}, {"cell_type": "markdown", "id": "914832f8", "metadata": {}, "source": ["# Image\n"]}, {"cell_type": "code", "execution_count": null, "id": "c8f4704c", "metadata": {}, "outputs": [], "source": ["im = t2pil(Image.open(\"../tiny-imagenet-200/train/n01443537/images/n01443537_0.JPEG\")).reshape(1, 3, 64, 64)\n", "im_ = t2pil(Image.open(\"../tiny-imagenet-200/train/n01443537/images/n01443537_1.JPEG\")).reshape(1, 3, 64, 64)\n", "inp = torch.cat((im, im_))\n", "inp.shape"]}, {"cell_type": "markdown", "id": "c62ad440", "metadata": {}, "source": ["# Test"]}, {"cell_type": "code", "execution_count": null, "id": "81a8c642", "metadata": {}, "outputs": [], "source": ["im = torch.arange(100).reshape(1, 1, 10, 10).float()\n", "im_ = torch.arange(100, 200).reshape(1, 1, 10, 10).float()\n", "inp = torch.cat((im, im_))\n", "inp.shape"]}, {"cell_type": "code", "execution_count": null, "id": "20b0e4d7", "metadata": {}, "outputs": [], "source": ["grid"]}, {"cell_type": "code", "execution_count": null, "id": "0245d605", "metadata": {}, "outputs": [], "source": ["im"]}, {"cell_type": "code", "execution_count": null, "id": "08e252de", "metadata": {}, "outputs": [], "source": ["t_ = nn.functional.grid_sample(im, grid, align_corners = True)"]}, {"cell_type": "code", "execution_count": null, "id": "c2857118", "metadata": {}, "outputs": [], "source": ["t_"]}, {"cell_type": "code", "execution_count": null, "id": "a29f164b", "metadata": {}, "outputs": [], "source": ["D=torch.tensor(np.array([0.5, 0.5, 0.5, 0.5, 1, 1, 1, 1, 2, 2, 2, 2]).reshape(3, 4)).transpose(0, 1)"]}, {"cell_type": "code", "execution_count": null, "id": "1e75b5a6", "metadata": {}, "outputs": [], "source": ["a = torch.tensor([0.0, 7, 1.50, 0.5, 63.10926839658489, 1.50, 1.0, 119.21853679316978, 1.50]).reshape(3, 3).transpose(0,1)"]}, {"cell_type": "code", "execution_count": null, "id": "2e01d028", "metadata": {}, "outputs": [], "source": ["a"]}, {"cell_type": "code", "execution_count": null, "id": "c45488fb", "metadata": {}, "outputs": [], "source": ["a[:, 1:][0]"]}, {"cell_type": "code", "execution_count": null, "id": "41ed16f0", "metadata": {"scrolled": true}, "outputs": [], "source": ["\n", "import numpy as np\n", "\n", "sz = 5\n", "input_arr = torch.from_numpy(np.arange(sz*sz).reshape(1,1,sz,sz)).float()\n", "input_arr = input_arr//20\n", "indices = torch.from_numpy(np.array([-1,-1, -0.5,-0.5, 0,0, 0.5,0.5, 1,1]).reshape(1, 1, 5, 2)).float()\n", "\n", "out = nn.functional.grid_sample(input_arr, indices)\n", "print(input_arr)\n", "print(out)\n"]}, {"cell_type": "code", "execution_count": null, "id": "006bb765", "metadata": {}, "outputs": [], "source": ["48+16"]}, {"cell_type": "code", "execution_count": null, "id": "b112e4c4", "metadata": {}, "outputs": [], "source": ["t = torch.arange(16).reshape(1, 1, 4, 4).float()\n", "t_ = torch.arange(48, 64).reshape(1, 1, 4, 4).float()\n", "inp = torch.cat((t, t_))\n"]}, {"cell_type": "code", "execution_count": null, "id": "6a51b82d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8ac01130", "metadata": {}, "outputs": [], "source": ["tensor = inp[:, :, x[i*radius_subdiv:radius_subdiv + i*radius_subdiv], y[i*radius_subdiv:radius_subdiv + i*radius_subdiv]].permute(0,2,1,3).contiguous().view(-1, 64*3)"]}, {"cell_type": "code", "execution_count": null, "id": "b86f3638", "metadata": {}, "outputs": [], "source": ["t = torch.arange(8).reshape(1, 2,2,2)"]}, {"cell_type": "code", "execution_count": null, "id": "5c52d950", "metadata": {}, "outputs": [], "source": ["t = t.repeat(3, 1, 1, 1)"]}, {"cell_type": "code", "execution_count": null, "id": "027ca4f5", "metadata": {}, "outputs": [], "source": ["t"]}, {"cell_type": "code", "execution_count": null, "id": "e47abd55", "metadata": {}, "outputs": [], "source": ["out = tensor.contiguous().view(2, 2, -1)\n", "out.shape"]}, {"cell_type": "code", "execution_count": null, "id": "de0b4234", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "989cb618", "metadata": {}, "outputs": [], "source": ["out"]}, {"cell_type": "code", "execution_count": null, "id": "20b3e1dc", "metadata": {}, "outputs": [], "source": ["x_[0,0:2,3]  = out"]}, {"cell_type": "code", "execution_count": null, "id": "52bc94aa", "metadata": {}, "outputs": [], "source": ["out.shape"]}, {"cell_type": "code", "execution_count": null, "id": "5fce651e", "metadata": {}, "outputs": [], "source": ["x_ = torch.rand(2, 2*radius_subdiv, azimuth_subdiv//2, 60)"]}, {"cell_type": "code", "execution_count": null, "id": "f77b459a", "metadata": {}, "outputs": [], "source": ["for i in range(azimuth_subdiv):\n", "    print(i, i*radius_subdiv)\n", "    tensor = nn.functional.grid_sample(inp, out[i*radius_subdiv:radius_subdiv + i*radius_subdiv].reshape(1, radius_subdiv, 20,2).repeat(2, 1, 1,1), align_corners = False)\n", "#     tensor = inp[:, :, x[i*radius_subdiv:radius_subdiv + i*radius_subdiv], y[i*radius_subdiv:radius_subdiv + i*radius_subdiv]] .permute(0,2,1,3).contiguous().view(-1, 64*3)\n", "    tensor = tensor.permute(0,2,1,3).contiguous().view(-1, 20*3)\n", "    o = tensor.contiguous().view(2, radius_subdiv, -1)\n", "    if i < azimuth_subdiv//2:\n", "        x_[:, 0:2, azimuth_subdiv//2-1-i, :] = o\n", "    else:\n", "        x_[:, 2:4, 0+i-azimuth_subdiv//2, :] = o\n", "        \n", "    \n", "    \n"]}, {"cell_type": "code", "execution_count": null, "id": "6abe0684", "metadata": {}, "outputs": [], "source": ["x_.shape"]}, {"cell_type": "code", "execution_count": null, "id": "4ccaba45", "metadata": {}, "outputs": [], "source": ["o = torch.arange(16)\n", "x = torch.rand(2*radius_subdiv, azimuth_subdiv//2)\n", "x.shape, o"]}, {"cell_type": "code", "execution_count": null, "id": "c3884bda", "metadata": {}, "outputs": [], "source": ["for i in range(azimuth_subdiv):\n", "    if i < azimuth_subdiv//2:\n", "        x[0:2, azimuth_subdiv//2-1-i] = o[i*2:2 + i*2]\n", "    else:\n", "        x[2:4, 0+i-azimuth_subdiv//2] = o[i*2:2 + i*2]\n", "    \n", "    \n"]}, {"cell_type": "code", "execution_count": null, "id": "0f02f64f", "metadata": {}, "outputs": [], "source": ["x"]}, {"cell_type": "code", "execution_count": null, "id": "f71c7405", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d82094a1", "metadata": {}, "outputs": [], "source": ["ind = torch.tensor(((0, 0, 1, 2), (1,1,2, 3)))\n", "ind1 = torch.tensor(((1, 2, 3, 2), (1,1,2, 2)))"]}, {"cell_type": "code", "execution_count": null, "id": "428366a9", "metadata": {}, "outputs": [], "source": ["inp[:, :, ind, ind1].shape"]}, {"cell_type": "code", "execution_count": null, "id": "dd6b339e", "metadata": {}, "outputs": [], "source": ["q = inp[:, :, ind, ind1].permute(0,2, 1, 3 ).contiguous().view(-1, 12)"]}, {"cell_type": "code", "execution_count": null, "id": "287bdfe7", "metadata": {}, "outputs": [], "source": ["q.shape"]}, {"cell_type": "code", "execution_count": null, "id": "d7bf1f95", "metadata": {}, "outputs": [], "source": ["q"]}, {"cell_type": "code", "execution_count": null, "id": "e9f0d6cf", "metadata": {}, "outputs": [], "source": ["inp[:, :, ind, ind1].permute(0,2, 1, 3).flatten(2)"]}, {"cell_type": "code", "execution_count": null, "id": "26679288", "metadata": {}, "outputs": [], "source": ["q.contiguous().view(2, 2, -1)"]}, {"cell_type": "code", "execution_count": null, "id": "5f1e0606", "metadata": {}, "outputs": [], "source": ["t = torch.ones(( 2, 4, 4, 12))"]}, {"cell_type": "code", "execution_count": null, "id": "ee956055", "metadata": {}, "outputs": [], "source": ["t[:, :2, 0, :].shape"]}, {"cell_type": "code", "execution_count": null, "id": "adaa2752", "metadata": {}, "outputs": [], "source": ["t[:, 0, :2, :] =  q.contiguous().view(2, 2, -1)"]}, {"cell_type": "code", "execution_count": null, "id": "9b920791", "metadata": {}, "outputs": [], "source": ["t[0][:, :, 0]"]}, {"cell_type": "code", "execution_count": null, "id": "6b90c41e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "swin", "language": "python", "name": "swin"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.16"}}, "nbformat": 4, "nbformat_minor": 5}