{"cells": [{"cell_type": "code", "execution_count": 18, "id": "5e330601", "metadata": {}, "outputs": [], "source": ["from utils import get_sample_params_from_subdiv, get_sample_locations\n", "import numpy as np\n", "import torch "]}, {"cell_type": "code", "execution_count": 19, "id": "eaec615d", "metadata": {}, "outputs": [], "source": ["radius_subdiv = 16\n", "azimuth_subdiv = 64\n", "subdiv = (radius_subdiv, azimuth_subdiv)\n", "# subdiv = 3\n", "n_radius = 20\n", "n_azimuth = 20\n", "img_size = (64,64)\n", "radius_buffer, azimuth_buffer = 0, 0"]}, {"cell_type": "code", "execution_count": 20, "id": "718ad808", "metadata": {}, "outputs": [], "source": ["params = get_sample_params_from_subdiv(\n", "        subdiv=subdiv,\n", "        img_size=img_size,\n", "        D = [0.5, 0.5, 0.5, 0.5], \n", "        n_radius=n_radius,\n", "        n_azimuth=n_azimuth,\n", "        radius_buffer=radius_buffer,\n", "        azimuth_buffer=azimuth_buffer\n", "    )"]}, {"cell_type": "code", "execution_count": 21, "id": "7a8efa2e", "metadata": {}, "outputs": [], "source": ["sample_locations = get_sample_locations(**params)"]}, {"cell_type": "code", "execution_count": 23, "id": "ea099f9b", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAesAAAHiCAYAAADI/ORpAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjUuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8qNh9FAAAACXBIWXMAAAsTAAALEwEAmpwYAAAXdUlEQVR4nO3de7Dnd13f8dfbhGCFcBmzOJALQQiGCK3gGqAqYEEmZEryB5YmylAYhsxgoa0iLWJBLr1ZFFtqLIQRQUVCpNVZITTIrbGUpCylBBIMXcMlFyDhFu6EwLt//H6pJ4fdnF825+y+d8/jMbMzv8vn9/t+zieb8zzf7+97vlvdHQBgru872BMAAG6bWAPAcGINAMOJNQAMJ9YAMJxYA8BwYg1DVdWLq+qPlrdPqKqvVtURW7Cd11XVv9rs991gmz9dVVceyG3CoUysYZ2q+qmq+p9VdWNVfaGq3ltVP3Ew59Tdn+ruu3b3dw7mPPZXVXVVPeCW+939l939IwdzTnAoOfJgTwAmqaq7JXlLkmcluSDJUUl+Osm3Dua8gO3NnjXc2gOTpLvf2N3f6e5vdPfbu/uyJKmq+1fVu6rq81X1uap6Q1Xd45YXV9Unqup5VXVZVX2tqn6vqn6oqt5WVV+pqndU1T2XY09c7nGeU1XXVdWnq+pX9japNWOPXN5/T1W9bLnX/5WqentVHbNm/FOr6pPLeb5wOa/HrbIAVfXMqtqzPKqwq6rus+a5H62qv1g+99mqesHy8VOr6n1V9aXl1/E7VXXU8rmLly//0PJQ/j+sqsdU1TVr3vdBy6/pS1V1eVWdsea511XVuVX11uXXemlV3X/5XFXVb1fV9VX15ar6cFU9eJWvEw4lYg239rEk36mq11fVE24J6xqV5N8muU+SByU5PsmL1415UpKfzSL8T0zytiQvSLIji//n/sm68T+T5KQkj0/yL1aNapKfT/L0JPfK4gjAryRJVZ2S5HeT/EKSeye5e5JjV3nDqvp7y6/vycvXfjLJ+cvnjk7yjiT/LYuv/wFJ3rl86XeS/FKSY5I8Msljk/xiknT3o5Zj/s7yUP6b1m3zTkn+PMnbl1/Lc5K8oarWHiY/K8lLktwzyZ4k/3r5+OOTPCqLtb77ct6fX+VrhUOJWMMa3f3lJD+VpJO8JskNy73LH1o+v6e7/6K7v9XdNyR5RZJHr3ub/9Tdn+3ua5P8ZZJLu/uD3f3NJH+a5KHrxr+ku7/W3R9O8vtJzl5xur/f3R/r7m9kccj+x5aP/1ySP+/u/9HdNyV50fLrWcUvJHltd//v7v5Wkl9N8siqOjHJ30/yme7+re7+Znd/pbsvXa7LB7r7ku6+ubs/keTVe1mXfXlEkrsm+XfdfVN3vyuLjyLWrsOfdvf/6u6bk7xhzdf67SRHJzk5SXX3R7v70ytuFw4ZYg3rLL/hP627j0vy4Cz2Iv9DkiwPaZ9fVddW1ZeT/FEWe5NrfXbN7W/s5f5d142/es3tTy63t4rPrLn99TXve5+179ndX8/qe5v3Wc7hltd+dfnaY7M4ivDXe3tRVT2wqt5SVZ9Zrsu/yfeuy21t8+ru/u6axz6ZWx8N2OvXugz77yQ5N8n1VXXe8rwDOKyINdyG7v6rJK/LItrJIkKd5CHdfbckT8ni0Pgdcfya2yckue4Ovt+nkxx3y52q+ltJfnDF116X5L5rXnuX5WuvzeIHgB/ex+v+c5K/SnLScl1ekNXX5bokx1fV2u9HJyy3uaHufmV3/3iSU7I4HP68FbcLhwyxhjWq6uSqem5VHbe8f3wWh2MvWQ45OslXk9xYVcdmc8Lwwqr6gar60Sw+g37TRi/YwJuTPLGq/u7yJK8XZ/VwvjHJ06vqx6rqzln8cHLp8tD2W5Lcu6r+WVXduaqOrqqHL193dJIvJ/lqVZ2cxdn0a302+w79pVnsLf/zqrpTVT0mi8/6z99oslX1E1X18OXn3l9L8s0k393gZXDIEWu4ta8keXiSS6vqa1lE+iNJnrt8/iVJHpbkxiRvTfJfN2Gb/z2Lk6bemeQ3u/vtd+TNuvvyLE7SOj+LveyvJrk+K/z6WXe/I8kLk/yX5Wvvn8XJXenur2Rx4twTszgs/X+zODkuWZzc9vNZrN9r8r0/cLw4yeuXZ3s/ed02b1q+5xOSfC6Lk+OeujyqsZG7Lbf3xSwOnX8+yctXeB0cUqp71fNOgM20PGnr40nutDxxaqu2c9ckX8riEPXHt2o7wNaxZw2Hoap64vLQ+l2S/GaSDyf5xMGdFbC/Nox1Vb12ecGBj+zj+aqqVy4vonBZVT1s86cJ3E5nZnHi1nVZ/A73We0wGhyyNjwMXlWPyuIzrz/o7u+5MlBVnZ7F52OnZ/FZ33/s7oevHwcA7J8N96y7++IkX7iNIWdmEfLu7kuS3KOq7r1ZEwSA7W4zPrM+Nre+qMM1WfHShgDAxg7ov7pVVeckOSdJ7nKXu/z4ySeffCA3DwAHzQc+8IHPdfeO/XntZsT62tz6CkzHZR9XHuru85KclyQ7d+7s3bt3b8LmAWC+qvrkxqP2bjMOg+9K8tTlWeGPSHKjC+kDwObZcM+6qt6Y5DFJjln++7O/nuROSdLdr0pyYRZngu/J4pKBT9+qyQLAdrRhrLv7Nv+5vuXvbv7jTZsRAHArrmAGAMOJNQAMJ9YAMJxYA8BwYg0Aw4k1AAwn1gAwnFgDwHBiDQDDiTUADCfWADCcWAPAcGINAMOJNQAMJ9YAMJxYA8BwYg0Aw4k1AAwn1gAwnFgDwHBiDQDDiTUADCfWADCcWAPAcGINAMOJNQAMJ9YAMJxYA8BwYg0Aw4k1AAwn1gAwnFgDwHBiDQDDiTUADCfWADCcWAPAcGINAMOJNQAMJ9YAMJxYA8BwYg0Aw4k1AAwn1gAwnFgDwHBiDQDDiTUADCfWADCcWAPAcGINAMOJNQAMJ9YAMJxYA8BwYg0Aw4k1AAwn1gAwnFgDwHBiDQDDiTUADCfWADCcWAPAcGINAMOJNQAMJ9YAMJxYA8BwYg0Aw4k1AAwn1gAwnFgDwHBiDQDDiTUADCfWADCcWAPAcGINAMOJNQAMJ9YAMJxYA8BwYg0Aw4k1AAy3Uqyr6rSqurKq9lTV8/fy/AlV9e6q+mBVXVZVp2/+VAFge9ow1lV1RJJzkzwhySlJzq6qU9YN+5dJLujuhyY5K8nvbvZEAWC7WmXP+tQke7r7qu6+Kcn5Sc5cN6aT3G15++5Jrtu8KQLA9nbkCmOOTXL1mvvXJHn4ujEvTvL2qnpOkrskedymzA4A2LQTzM5O8rruPi7J6Un+sKq+572r6pyq2l1Vu2+44YZN2jQAHN5WifW1SY5fc/+45WNrPSPJBUnS3e9L8v1Jjln/Rt19Xnfv7O6dO3bs2L8ZA8A2s0qs35/kpKq6X1UdlcUJZLvWjflUkscmSVU9KItY23UGgE2wYay7++Ykz05yUZKPZnHW9+VV9dKqOmM57LlJnllVH0ryxiRP6+7eqkkDwHayyglm6e4Lk1y47rEXrbl9RZKf3NypAQCJK5gBwHhiDQDDiTUADCfWADCcWAPAcGINAMOJNQAMJ9YAMJxYA8BwYg0Aw4k1AAwn1gAwnFgDwHBiDQDDiTUADCfWADCcWAPAcGINAMOJNQAMJ9YAMJxYA8BwYg0Aw4k1AAwn1gAwnFgDwHBiDQDDiTUADCfWADCcWAPAcGINAMOJNQAMJ9YAMJxYA8BwYg0Aw4k1AAwn1gAwnFgDwHBiDQDDiTUADCfWADCcWAPAcGINAMOJNQAMJ9YAMJxYA8BwYg0Aw4k1AAwn1gAwnFgDwHBiDQDDiTUADCfWADCcWAPAcGINAMOJNQAMJ9YAMJxYA8BwYg0Aw4k1AAwn1gAwnFgDwHBiDQDDiTUADCfWADCcWAPAcGINAMOJNQAMJ9YAMJxYA8BwYg0Aw4k1AAwn1gAwnFgDwHBiDQDDiTUADCfWADCcWAPAcGINAMOtFOuqOq2qrqyqPVX1/H2MeXJVXVFVl1fVH2/uNAFg+zpyowFVdUSSc5P8bJJrkry/qnZ19xVrxpyU5FeT/GR3f7Gq7rVVEwaA7WaVPetTk+zp7qu6+6Yk5yc5c92YZyY5t7u/mCTdff3mThMAtq9VYn1skqvX3L9m+dhaD0zywKp6b1VdUlWnbdYEAWC72/Aw+O14n5OSPCbJcUkurqqHdPeX1g6qqnOSnJMkJ5xwwiZtGgAOb6vsWV+b5Pg1949bPrbWNUl2dfe3u/vjST6WRbxvpbvP6+6d3b1zx44d+ztnANhWVon1+5OcVFX3q6qjkpyVZNe6MX+WxV51quqYLA6LX7V50wSA7WvDWHf3zUmeneSiJB9NckF3X15VL62qM5bDLkry+aq6Ism7kzyvuz+/VZMGgO2kuvugbHjnzp29e/fug7JtADjQquoD3b1zf17rCmYAMJxYA8BwYg0Aw4k1AAwn1gAwnFgDwHBiDQDDiTUADCfWADCcWAPAcGINAMOJNQAMJ9YAMJxYA8BwYg0Aw4k1AAwn1gAwnFgDwHBiDQDDiTUADCfWADCcWAPAcGINAMOJNQAMJ9YAMJxYA8BwYg0Aw4k1AAwn1gAwnFgDwHBiDQDDiTUADCfWADCcWAPAcGINAMOJNQAMJ9YAMJxYA8BwYg0Aw4k1AAwn1gAwnFgDwHBiDQDDiTUADCfWADCcWAPAcGINAMOJNQAMJ9YAMJxYA8BwYg0Aw4k1AAwn1gAwnFgDwHBiDQDDiTUADCfWADCcWAPAcGINAMOJNQAMJ9YAMJxYA8BwYg0Aw4k1AAwn1gAwnFgDwHBiDQDDiTUADCfWADCcWAPAcGINAMOJNQAMJ9YAMJxYA8BwYg0Aw4k1AAwn1gAwnFgDwHArxbqqTquqK6tqT1U9/zbGPamquqp2bt4UAWB72zDWVXVEknOTPCHJKUnOrqpT9jLu6CT/NMmlmz1JANjOVtmzPjXJnu6+qrtvSnJ+kjP3Mu5lSX4jyTc3cX4AsO2tEutjk1y95v41y8f+v6p6WJLju/utmzg3ACCbcIJZVX1fklckee4KY8+pqt1VtfuGG264o5sGgG1hlVhfm+T4NfePWz52i6OTPDjJe6rqE0kekWTX3k4y6+7zuntnd+/csWPH/s8aALaRVWL9/iQnVdX9quqoJGcl2XXLk919Y3cf090ndveJSS5JckZ3796SGQPANrNhrLv75iTPTnJRko8muaC7L6+ql1bVGVs9QQDY7o5cZVB3X5jkwnWPvWgfYx9zx6cFANzCFcwAYDixBoDhxBoAhhNrABhOrAFgOLEGgOHEGgCGE2sAGE6sAWA4sQaA4cQaAIYTawAYTqwBYDixBoDhxBoAhhNrABhOrAFgOLEGgOHEGgCGE2sAGE6sAWA4sQaA4cQaAIYTawAYTqwBYDixBoDhxBoAhhNrABhOrAFgOLEGgOHEGgCGE2sAGE6sAWA4sQaA4cQaAIYTawAYTqwBYDixBoDhxBoAhhNrABhOrAFgOLEGgOHEGgCGE2sAGE6sAWA4sQaA4cQaAIYTawAYTqwBYDixBoDhxBoAhhNrABhOrAFgOLEGgOHEGgCGE2sAGE6sAWA4sQaA4cQaAIYTawAYTqwBYDixBoDhxBoAhhNrABhOrAFgOLEGgOHEGgCGE2sAGE6sAWA4sQaA4cQaAIYTawAYTqwBYDixBoDhxBoAhhNrABhOrAFgOLEGgOFWinVVnVZVV1bVnqp6/l6e/+WquqKqLquqd1bVfTd/qgCwPW0Y66o6Ism5SZ6Q5JQkZ1fVKeuGfTDJzu7+20nenOTfb/ZEAWC7WmXP+tQke7r7qu6+Kcn5Sc5cO6C7393dX1/evSTJcZs7TQDYvlaJ9bFJrl5z/5rlY/vyjCRvuyOTAgD+xpGb+WZV9ZQkO5M8eh/Pn5PknCQ54YQTNnPTAHDYWmXP+tokx6+5f9zysVupqscl+bUkZ3T3t/b2Rt19Xnfv7O6dO3bs2J/5AsC2s0qs35/kpKq6X1UdleSsJLvWDqiqhyZ5dRahvn7zpwkA29eGse7um5M8O8lFST6a5ILuvryqXlpVZyyHvTzJXZP8SVX9n6ratY+3AwBup5U+s+7uC5NcuO6xF625/bhNnhcAsOQKZgAwnFgDwHBiDQDDiTUADCfWADCcWAPAcGINAMOJNQAMJ9YAMJxYA8BwYg0Aw4k1AAwn1gAwnFgDwHBiDQDDiTUADCfWADCcWAPAcGINAMOJNQAMJ9YAMJxYA8BwYg0Aw4k1AAwn1gAwnFgDwHBiDQDDiTUADCfWADCcWAPAcGINAMOJNQAMJ9YAMJxYA8BwYg0Aw4k1AAwn1gAwnFgDwHBiDQDDiTUADCfWADCcWAPAcGINAMOJNQAMJ9YAMJxYA8BwYg0Aw4k1AAwn1gAwnFgDwHBiDQDDiTUADCfWADCcWAPAcGINAMOJNQAMJ9YAMJxYA8BwYg0Aw4k1AAwn1gAwnFgDwHBiDQDDiTUADCfWADCcWAPAcGINAMOJNQAMJ9YAMJxYA8BwYg0Aw4k1AAwn1gAwnFgDwHBiDQDDiTUADCfWADCcWAPAcCvFuqpOq6orq2pPVT1/L8/fuaretHz+0qo6cdNnCgDb1Iaxrqojkpyb5AlJTklydlWdsm7YM5J8sbsfkOS3k/zGZk8UALarVfasT02yp7uv6u6bkpyf5Mx1Y85M8vrl7TcneWxV1eZNEwC2r1VifWySq9fcv2b52F7HdPfNSW5M8oObMUEA2O6OPJAbq6pzkpyzvPutqvrIgdz+NnRMks8d7ElsA9Z561njrWeNt96P7O8LV4n1tUmOX3P/uOVjextzTVUdmeTuST6//o26+7wk5yVJVe3u7p37M2lWY40PDOu89azx1rPGW6+qdu/va1c5DP7+JCdV1f2q6qgkZyXZtW7MriT/aHn755K8q7t7fycFAPyNDfesu/vmqnp2kouSHJHktd19eVW9NMnu7t6V5PeS/GFV7UnyhSyCDgBsgpU+s+7uC5NcuO6xF625/c0k/+B2bvu82zme288aHxjWeetZ461njbfefq9xOVoNALO53CgADLflsXap0q23whr/clVdUVWXVdU7q+q+B2Oeh7KN1njNuCdVVVeVs2r3wyrrXFVPXv59vryq/vhAz/FQt8L3ixOq6t1V9cHl94zTD8Y8D2VV9dqqun5fv55cC69c/je4rKoetuGbdveW/cnihLS/TvLDSY5K8qEkp6wb84tJXrW8fVaSN23lnA63Pyuu8c8k+YHl7WdZ481f4+W4o5NcnOSSJDsP9rwPtT8r/l0+KckHk9xzef9eB3veh9KfFdf4vCTPWt4+JcknDva8D7U/SR6V5GFJPrKP509P8rYkleQRSS7d6D23es/apUq33oZr3N3v7u6vL+9eksXvyrO6Vf4eJ8nLsrgu/jcP5OQOI6us8zOTnNvdX0yS7r7+AM/xULfKGneSuy1v3z3JdQdwfoeF7r44i9+M2pczk/xBL1yS5B5Vde/bes+tjrVLlW69VdZ4rWdk8RMdq9twjZeHsY7v7rceyIkdZlb5u/zAJA+sqvdW1SVVddoBm93hYZU1fnGSp1TVNVn8FtBzDszUtpXb+337wF5ulIOrqp6SZGeSRx/suRxOqur7krwiydMO8lS2gyOzOBT+mCyOEF1cVQ/p7i8dzEkdZs5O8rru/q2qemQW19B4cHd/92BPbDvb6j3r23Op0tzWpUrZp1XWOFX1uCS/luSM7v7WAZrb4WKjNT46yYOTvKeqPpHFZ1C7nGR2u63yd/maJLu6+9vd/fEkH8si3qxmlTV+RpILkqS735fk+7O4bjibZ6Xv22ttdaxdqnTrbbjGVfXQJK/OItQ+47v9bnONu/vG7j6mu0/s7hOzOC/gjO7e7+sAb1OrfL/4syz2qlNVx2RxWPyqAzjHQ90qa/ypJI9Nkqp6UBaxvuGAzvLwtyvJU5dnhT8iyY3d/enbesGWHgZvlyrdciuu8cuT3DXJnyzP3ftUd59x0CZ9iFlxjbmDVlzni5I8vqquSPKdJM/rbkfiVrTiGj83yWuq6peyONnsaXagbp+qemMWP1Qes/zs/9eT3ClJuvtVWZwLcHqSPUm+nuTpG76n/wYAMJsrmAHAcGINAMOJNQAMJ9YAMJxYA8BwYg0Aw4k1AAwn1gAw3P8Dgk6H38IJ3uIAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 576x576 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "_, ax = plt.subplots(figsize=(8, 8))\n", "ax.set_title(\"Sampling locations\")\n", "colors = ['#e41a1c', '#377eb8', '#4daf4a', '#984ea3', '#ff7f00', '#ffff33', '#a65628']\n"]}, {"cell_type": "code", "execution_count": 32, "id": "e0a7b2c5", "metadata": {}, "outputs": [], "source": ["for i in range(sample_locations[0].shape[0]):\n", "    sample = (sample_locations[0][i] + 32, sample_locations[1][i] + 32)\n", "    ax.scatter(*sample, color=colors[i%len(colors)], s=6)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "8f5b8ea5", "metadata": {}, "outputs": [], "source": ["out = []\n", "out = torch.empty(len(params),1,n_radius*n_azimuth,2)\n", "import pdb;pdb.set_trace()\n", "for i in range(len(params)):\n", "    sample_locations = get_sample_locations(**params[i])\n", "    # cProfile.run(get_sample_locations())\n", "#     x_ = torch.tensor(sample_locations[0]).reshape(1, 1, n_radius*n_azimuth).float()\n", "#     x_ = x_/ 32\n", "#     x_.long()\n", "#     y_ = torch.tensor(sample_locations[1]).reshape(1, 1, n_radius*n_azimuth).float()\n", "#     y_ = y_/32\n", "#     y_.long()\n", "#     t = torch.cat((x_, -(y_)))\n", "# #     import pdb;pdb.set_trace()\n", "#     out[i] = t.transpose(0,1).transpose(1,2)\n", "    out.append(sample_locations)"]}, {"cell_type": "code", "execution_count": null, "id": "3c3ba07a", "metadata": {}, "outputs": [], "source": ["sample_locations[1][2]"]}, {"cell_type": "code", "execution_count": null, "id": "8d55623b", "metadata": {}, "outputs": [], "source": ["out"]}, {"cell_type": "code", "execution_count": null, "id": "9fe88ef2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "75be32a1", "metadata": {}, "outputs": [], "source": [" D_min = []\n", "\n", "D_s = []\n", "for i in params:\n", "    D_min.append(i['dmin'])"]}, {"cell_type": "code", "execution_count": null, "id": "9178f61e", "metadata": {}, "outputs": [], "source": ["for i in params:\n", "    ...:     D_s.append(i['ds'])"]}, {"cell_type": "code", "execution_count": null, "id": "c3dd8ad3", "metadata": {}, "outputs": [], "source": ["D_min"]}, {"cell_type": "code", "execution_count": null, "id": "39c0a7f9", "metadata": {}, "outputs": [], "source": ["dmimn = np.array([ 0.        ,  6.27356265, 11.8556185 , 16.33477941, 19.70256506,\n", "       22.21763762, 24.1367413 , 25.64966975, 26.89142218, 27.93158828,\n", "       28.82656034, 29.60348559, 30.2946761 , 30.91569069, 31.48018589,\n", "       32.        ])"]}, {"cell_type": "code", "execution_count": null, "id": "fdc2bbc9", "metadata": {}, "outputs": [], "source": ["a = dmimn[:-1].reshape(1, 15).repeat(8, 0).reshape(1, 8*15)"]}, {"cell_type": "code", "execution_count": null, "id": "f8a7a492", "metadata": {}, "outputs": [], "source": ["a"]}, {"cell_type": "code", "execution_count": null, "id": "24adebc1", "metadata": {}, "outputs": [], "source": ["\n", "phi_start = 0\n", "phi_end = 2*np.pi\n", "phi_step = alpha\n", "phi_list = np.arange(phi_start, phi_end, phi_step)"]}, {"cell_type": "code", "execution_count": null, "id": "182631fe", "metadata": {}, "outputs": [], "source": ["phi_list"]}, {"cell_type": "code", "execution_count": null, "id": "2a13229c", "metadata": {}, "outputs": [], "source": ["p = phi_list.reshape(1, 4).repeat(2, 0)"]}, {"cell_type": "code", "execution_count": null, "id": "ad8ca1c5", "metadata": {}, "outputs": [], "source": ["phi = p.transpose(1,0).flatten()"]}, {"cell_type": "code", "execution_count": null, "id": "0929897c", "metadata": {}, "outputs": [], "source": ["phi"]}, {"cell_type": "code", "execution_count": null, "id": "a2ea5ee9", "metadata": {}, "outputs": [], "source": ["alpha = np.array(1.57079633).repeat(8)"]}, {"cell_type": "code", "execution_count": null, "id": "a4a27f38", "metadata": {}, "outputs": [], "source": ["alpha, phi"]}, {"cell_type": "code", "execution_count": null, "id": "4a1771f0", "metadata": {}, "outputs": [], "source": ["9.18691667, 7.14786274, 4.71206872, 3.08989318, 2.16201313,\n", "       1.63283384, 1.29558844, 1.06749939, 0.91096282, 0.79436107"]}, {"cell_type": "code", "execution_count": null, "id": "acd40ff9", "metadata": {}, "outputs": [], "source": ["r_start = np.array(D_min) + np.array(D_s)\n", "r_end = np.array(D_min)"]}, {"cell_type": "code", "execution_count": null, "id": "645e1b50", "metadata": {}, "outputs": [], "source": ["alpha_start = phi \n", "alpha_end = alpha + phi - azimuth_buffer"]}, {"cell_type": "code", "execution_count": null, "id": "74ad6b22", "metadata": {}, "outputs": [], "source": ["alpha_start, alpha_end"]}, {"cell_type": "code", "execution_count": null, "id": "e1e49870", "metadata": {}, "outputs": [], "source": ["radius = np.linspace(r_start, r_end, n_radius)\n", "azimuth = np.linspace(alpha_start, alpha_end, n_azimuth)"]}, {"cell_type": "code", "execution_count": null, "id": "5b2221f6", "metadata": {}, "outputs": [], "source": ["radius.shape, azimuth.shape"]}, {"cell_type": "code", "execution_count": null, "id": "3ea2c1c4", "metadata": {}, "outputs": [], "source": ["# radius = np.linspace(0, 2, n_radius)\n", "radius = np.transpose(radius, (1,0))\n", "radius = radius.flatten()"]}, {"cell_type": "code", "execution_count": null, "id": "069e228f", "metadata": {}, "outputs": [], "source": ["# radius = np.linspace(0, 2, n_radius)\n", "azimuth = np.transpose(azimuth, (1,0))\n", "azimuth = azimuth.flatten()"]}, {"cell_type": "code", "execution_count": null, "id": "913cb8d5", "metadata": {}, "outputs": [], "source": ["radius, azimuth"]}, {"cell_type": "code", "execution_count": null, "id": "6dadc6c6", "metadata": {}, "outputs": [], "source": ["azimuth_mesh_, radius_mesh_ = np.meshgrid(azimuth[0:4], radius[0:4])"]}, {"cell_type": "code", "execution_count": null, "id": "c81e5dbc", "metadata": {}, "outputs": [], "source": ["radius_mesh_"]}, {"cell_type": "code", "execution_count": null, "id": "af3737d3", "metadata": {}, "outputs": [], "source": ["azimuth_mesh, radius_mesh = np.meshgrid(azimuth, radius)"]}, {"cell_type": "code", "execution_count": null, "id": "d902c234", "metadata": {}, "outputs": [], "source": ["azimuth_mesh[0]"]}, {"cell_type": "code", "execution_count": null, "id": "82025d5b", "metadata": {}, "outputs": [], "source": ["radius_mesh_r = radius_mesh[:, :4].reshape(8, 4, 4)"]}, {"cell_type": "code", "execution_count": null, "id": "889d3921", "metadata": {}, "outputs": [], "source": ["radius_mesh_r = radius_mesh[:, :4].reshape(8, 4, 4)\n", "azimuth_mesh_a = azimuth_mesh[:4, :].reshape(4, 8, 4).transpose(1,0,2)"]}, {"cell_type": "code", "execution_count": null, "id": "0d87dbdd", "metadata": {}, "outputs": [], "source": ["azimuth_mesh_a = azimuth_mesh_a"]}, {"cell_type": "code", "execution_count": null, "id": "e6896ffe", "metadata": {}, "outputs": [], "source": ["azimuth_mesh_a[2], radius_mesh_r[2]"]}, {"cell_type": "code", "execution_count": null, "id": "2c69245f", "metadata": {}, "outputs": [], "source": ["x = radius_mesh_ * np.cos(azimuth_mesh_) "]}, {"cell_type": "code", "execution_count": null, "id": "9a485468", "metadata": {}, "outputs": [], "source": ["x = x.reshape(-1)"]}, {"cell_type": "code", "execution_count": null, "id": "88c62233", "metadata": {}, "outputs": [], "source": ["x_ = radius_mesh_r * np.cos(azimuth_mesh_a) "]}, {"cell_type": "code", "execution_count": null, "id": "b2ba6418", "metadata": {}, "outputs": [], "source": ["x_ = x_.reshape(8, 16)"]}, {"cell_type": "code", "execution_count": null, "id": "79c27e23", "metadata": {}, "outputs": [], "source": ["x"]}, {"cell_type": "code", "execution_count": null, "id": "71f9077e", "metadata": {}, "outputs": [], "source": ["x_"]}, {"cell_type": "code", "execution_count": null, "id": "c68a791d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "swin", "language": "python", "name": "swin"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.16"}}, "nbformat": 4, "nbformat_minor": 5}