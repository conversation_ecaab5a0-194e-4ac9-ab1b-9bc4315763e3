{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f9228866", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/tqdm/auto.py:22: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import torch"]}, {"cell_type": "code", "execution_count": 2, "id": "cbce7d8d", "metadata": {}, "outputs": [], "source": ["img = torch.arange(8*8).reshape(8,8)"]}, {"cell_type": "code", "execution_count": 3, "id": "fbe532a5", "metadata": {}, "outputs": [], "source": ["H, W = img.shape"]}, {"cell_type": "code", "execution_count": 4, "id": "d57b6be9", "metadata": {}, "outputs": [], "source": ["B = 1\n", "C =1"]}, {"cell_type": "markdown", "id": "a02a7879", "metadata": {}, "source": ["# window original swin"]}, {"cell_type": "code", "execution_count": 23, "id": "b7fe50aa", "metadata": {}, "outputs": [], "source": ["window_size = 2"]}, {"cell_type": "code", "execution_count": 24, "id": "b1d02f3b", "metadata": {}, "outputs": [], "source": ["x = img.view(1, 8 // window_size, window_size, 8 // window_size, window_size, 1)"]}, {"cell_type": "code", "execution_count": 25, "id": "ce6ae80b", "metadata": {}, "outputs": [], "source": ["windows = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(-1, window_size, window_size, 1)"]}, {"cell_type": "code", "execution_count": 26, "id": "8d613d27", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[ 0,  1,  2,  3,  4,  5,  6,  7],\n", "        [ 8,  9, 10, 11, 12, 13, 14, 15],\n", "        [16, 17, 18, 19, 20, 21, 22, 23],\n", "        [24, 25, 26, 27, 28, 29, 30, 31],\n", "        [32, 33, 34, 35, 36, 37, 38, 39],\n", "        [40, 41, 42, 43, 44, 45, 46, 47],\n", "        [48, 49, 50, 51, 52, 53, 54, 55],\n", "        [56, 57, 58, 59, 60, 61, 62, 63]])"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["img"]}, {"cell_type": "code", "execution_count": 27, "id": "84764d44", "metadata": {}, "outputs": [], "source": ["B = int(windows.shape[0] / (H * W / window_size / window_size))\n", "x = windows.view(B, H // window_size, W // window_size, window_size, window_size, -1)\n", "x = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(B, H, W, -1)"]}, {"cell_type": "code", "execution_count": 28, "id": "e66ec55f", "metadata": {}, "outputs": [], "source": ["x0 = x[:, 0::2, 0::2, :]  # B H/2 W/2 C\n", "x1 = x[:, 1::2, 0::2, :]  # B H/2 W/2 C\n", "x2 = x[:, 0::2, 1::2, :]  # B H/2 W/2 C\n", "x3 = x[:, 1::2, 1::2, :]  # B H/2 W/2 C"]}, {"cell_type": "code", "execution_count": 29, "id": "8124c51a", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[[[ 0],\n", "          [ 2],\n", "          [ 4],\n", "          [ 6]],\n", "\n", "         [[16],\n", "          [18],\n", "          [20],\n", "          [22]],\n", "\n", "         [[32],\n", "          [34],\n", "          [36],\n", "          [38]],\n", "\n", "         [[48],\n", "          [50],\n", "          [52],\n", "          [54]]]])"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["x0"]}, {"cell_type": "code", "execution_count": 205, "id": "5644ac12", "metadata": {}, "outputs": [], "source": ["x = torch.cat([x0, x1, x2, x3], -1)"]}, {"cell_type": "code", "execution_count": 245, "id": "d39dfd8d", "metadata": {}, "outputs": [], "source": ["x = x.view(B, -1, 4 * 1)"]}, {"cell_type": "code", "execution_count": 249, "id": "4ca816c2", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[ 0,  8,  1,  9],\n", "        [ 2, 10,  3, 11],\n", "        [ 4, 12,  5, 13],\n", "        [ 6, 14,  7, 15],\n", "        [16, 24, 17, 25],\n", "        [18, 26, 19, 27],\n", "        [20, 28, 21, 29],\n", "        [22, 30, 23, 31],\n", "        [32, 40, 33, 41],\n", "        [34, 42, 35, 43],\n", "        [36, 44, 37, 45],\n", "        [38, 46, 39, 47],\n", "        [48, 56, 49, 57],\n", "        [50, 58, 51, 59],\n", "        [52, 60, 53, 61],\n", "        [54, 62, 55, 63]])"]}, "execution_count": 249, "metadata": {}, "output_type": "execute_result"}], "source": ["x[0]"]}, {"cell_type": "code", "execution_count": 210, "id": "8c5333fc", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[ 0,  1,  2,  3,  4,  5,  6,  7],\n", "        [ 8,  9, 10, 11, 12, 13, 14, 15],\n", "        [16, 17, 18, 19, 20, 21, 22, 23],\n", "        [24, 25, 26, 27, 28, 29, 30, 31],\n", "        [32, 33, 34, 35, 36, 37, 38, 39],\n", "        [40, 41, 42, 43, 44, 45, 46, 47],\n", "        [48, 49, 50, 51, 52, 53, 54, 55],\n", "        [56, 57, 58, 59, 60, 61, 62, 63]])"]}, "execution_count": 210, "metadata": {}, "output_type": "execute_result"}], "source": ["img"]}, {"cell_type": "markdown", "id": "0c294aab", "metadata": {}, "source": ["# merging swin along patch"]}, {"cell_type": "code", "execution_count": 5, "id": "9f3ffa3a", "metadata": {}, "outputs": [], "source": ["x_ = img.view(1, H // 1, 1, W // 4, 4, 1)"]}, {"cell_type": "code", "execution_count": 6, "id": "8d343dc3", "metadata": {}, "outputs": [], "source": ["windows_ = x_.permute(0, 1, 3, 2, 4, 5).contiguous().view(-1, 4, 1, 1)"]}, {"cell_type": "code", "execution_count": 7, "id": "63caff74", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[[[ 0]],\n", "\n", "         [[ 1]],\n", "\n", "         [[ 2]],\n", "\n", "         [[ 3]]],\n", "\n", "\n", "        [[[ 4]],\n", "\n", "         [[ 5]],\n", "\n", "         [[ 6]],\n", "\n", "         [[ 7]]],\n", "\n", "\n", "        [[[ 8]],\n", "\n", "         [[ 9]],\n", "\n", "         [[10]],\n", "\n", "         [[11]]],\n", "\n", "\n", "        [[[12]],\n", "\n", "         [[13]],\n", "\n", "         [[14]],\n", "\n", "         [[15]]],\n", "\n", "\n", "        [[[16]],\n", "\n", "         [[17]],\n", "\n", "         [[18]],\n", "\n", "         [[19]]],\n", "\n", "\n", "        [[[20]],\n", "\n", "         [[21]],\n", "\n", "         [[22]],\n", "\n", "         [[23]]],\n", "\n", "\n", "        [[[24]],\n", "\n", "         [[25]],\n", "\n", "         [[26]],\n", "\n", "         [[27]]],\n", "\n", "\n", "        [[[28]],\n", "\n", "         [[29]],\n", "\n", "         [[30]],\n", "\n", "         [[31]]],\n", "\n", "\n", "        [[[32]],\n", "\n", "         [[33]],\n", "\n", "         [[34]],\n", "\n", "         [[35]]],\n", "\n", "\n", "        [[[36]],\n", "\n", "         [[37]],\n", "\n", "         [[38]],\n", "\n", "         [[39]]],\n", "\n", "\n", "        [[[40]],\n", "\n", "         [[41]],\n", "\n", "         [[42]],\n", "\n", "         [[43]]],\n", "\n", "\n", "        [[[44]],\n", "\n", "         [[45]],\n", "\n", "         [[46]],\n", "\n", "         [[47]]],\n", "\n", "\n", "        [[[48]],\n", "\n", "         [[49]],\n", "\n", "         [[50]],\n", "\n", "         [[51]]],\n", "\n", "\n", "        [[[52]],\n", "\n", "         [[53]],\n", "\n", "         [[54]],\n", "\n", "         [[55]]],\n", "\n", "\n", "        [[[56]],\n", "\n", "         [[57]],\n", "\n", "         [[58]],\n", "\n", "         [[59]]],\n", "\n", "\n", "        [[[60]],\n", "\n", "         [[61]],\n", "\n", "         [[62]],\n", "\n", "         [[63]]]])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["windows_"]}, {"cell_type": "code", "execution_count": 8, "id": "e607a7bd", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[ 0,  1,  2,  3,  4,  5,  6,  7],\n", "        [ 8,  9, 10, 11, 12, 13, 14, 15],\n", "        [16, 17, 18, 19, 20, 21, 22, 23],\n", "        [24, 25, 26, 27, 28, 29, 30, 31],\n", "        [32, 33, 34, 35, 36, 37, 38, 39],\n", "        [40, 41, 42, 43, 44, 45, 46, 47],\n", "        [48, 49, 50, 51, 52, 53, 54, 55],\n", "        [56, 57, 58, 59, 60, 61, 62, 63]])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["B = int(windows_.shape[0] / (H * W / 4 / 1))\n", "x_ = windows_.view(B, H // 1, W // 4, 1, 4, -1)\n", "x_ = x_.permute(0, 1, 3, 2, 4, 5).contiguous().view(B, H, W, -1)\n", "x_[0, :, :, 0]"]}, {"cell_type": "code", "execution_count": 15, "id": "ecbdf5ee", "metadata": {}, "outputs": [], "source": ["x_0 = x_[:, :, 0::4, :].reshape(B, H//1, W//4, 1)  # B H/2 W/2 C\n", "x_1 = x_[:, :, 1::4, :].reshape(B, H//1, W//4, 1)\n", "x_2 = x_[:, :, 2::4, :].reshape(B, H//1, W//4, 1)\n", "x_3 = x_[:, :, 3::4, :].reshape(B, H//1, W//4, 1)"]}, {"cell_type": "code", "execution_count": 10, "id": "c26aa31e", "metadata": {}, "outputs": [], "source": ["z = x_[:, :, 0::4, :][:, :4, :, :]\n", "o = x_[:, :, 0::4, :][:, 4:, :, :].flip(1)"]}, {"cell_type": "code", "execution_count": 16, "id": "472bd0b8", "metadata": {}, "outputs": [], "source": ["results = torch.cat([x_0, x_1, x_2, x_3], -1)"]}, {"cell_type": "code", "execution_count": 26, "id": "4e06ccb0", "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.Size([1, 8, 2, 4])"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["results"]}, {"cell_type": "code", "execution_count": 20, "id": "6ecf1e8e", "metadata": {}, "outputs": [], "source": ["t = torch.cat((x_0[:, :4, :, :], x_1[:, :4, :, :], x_2[:, :4, :, :], x_3[:, :4, :, :]), -1)\n", "t_ = torch.cat((x_0[:, 4:, :, :].flip(1), x_1[:, 4:, :, :].flip(1), x_2[:, 4:, :, :].flip(1), x_3[:, 4:, :, :].flip(1)), -1)"]}, {"cell_type": "code", "execution_count": 25, "id": "bfb1b16e", "metadata": {}, "outputs": [{"data": {"text/plain": ["(tensor([[[[ 0,  1,  2,  3],\n", "           [ 4,  5,  6,  7]],\n", " \n", "          [[ 8,  9, 10, 11],\n", "           [12, 13, 14, 15]],\n", " \n", "          [[16, 17, 18, 19],\n", "           [20, 21, 22, 23]],\n", " \n", "          [[24, 25, 26, 27],\n", "           [28, 29, 30, 31]]]]),\n", " tensor([[[[56, 57, 58, 59],\n", "           [60, 61, 62, 63]],\n", " \n", "          [[48, 49, 50, 51],\n", "           [52, 53, 54, 55]],\n", " \n", "          [[40, 41, 42, 43],\n", "           [44, 45, 46, 47]],\n", " \n", "          [[32, 33, 34, 35],\n", "           [36, 37, 38, 39]]]]))"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["t, t_"]}, {"cell_type": "code", "execution_count": 83, "id": "91db431c", "metadata": {}, "outputs": [], "source": ["results = torch.empty(B, H//1, W//4, 4*1)\n", "results[:, 0::2, :, :] = t\n", "results[:, 1::2, :, :] = t_"]}, {"cell_type": "code", "execution_count": 84, "id": "9df039b3", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[[[ 0.,  1.,  2.,  3.],\n", "          [ 4.,  5.,  6.,  7.]],\n", "\n", "         [[56., 57., 58., 59.],\n", "          [60., 61., 62., 63.]],\n", "\n", "         [[ 8.,  9., 10., 11.],\n", "          [12., 13., 14., 15.]],\n", "\n", "         [[48., 49., 50., 51.],\n", "          [52., 53., 54., 55.]],\n", "\n", "         [[16., 17., 18., 19.],\n", "          [20., 21., 22., 23.]],\n", "\n", "         [[40., 41., 42., 43.],\n", "          [44., 45., 46., 47.]],\n", "\n", "         [[24., 25., 26., 27.],\n", "          [28., 29., 30., 31.]],\n", "\n", "         [[32., 33., 34., 35.],\n", "          [36., 37., 38., 39.]]]])"]}, "execution_count": 84, "metadata": {}, "output_type": "execute_result"}], "source": ["results"]}, {"cell_type": "code", "execution_count": 61, "id": "269fc854", "metadata": {}, "outputs": [], "source": ["x = results.view(B, -1, 4 * 1)"]}, {"cell_type": "code", "execution_count": 72, "id": "dffd4476", "metadata": {}, "outputs": [], "source": ["x = torch.cat([x_0_0, x_1_0, x_2_0, x_3_0], -1)"]}, {"cell_type": "code", "execution_count": 73, "id": "369ad985", "metadata": {}, "outputs": [], "source": ["x_ = x_.view(B, -1, 4 * 1)"]}, {"cell_type": "code", "execution_count": 76, "id": "48b93c61", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[  0,   1,   2,   3],\n", "        [  4,   5,   6,   7],\n", "        [  8,   9,  10,  11],\n", "        [ 12,  13,  14,  15],\n", "        [ 16,  17,  18,  19],\n", "        [ 20,  21,  22,  23],\n", "        [ 24,  25,  26,  27],\n", "        [ 28,  29,  30,  31],\n", "        [ 32,  33,  34,  35],\n", "        [ 36,  37,  38,  39],\n", "        [ 40,  41,  42,  43],\n", "        [ 44,  45,  46,  47],\n", "        [ 48,  49,  50,  51],\n", "        [ 52,  53,  54,  55],\n", "        [ 56,  57,  58,  59],\n", "        [ 60,  61,  62,  63],\n", "        [ 64,  65,  66,  67],\n", "        [ 68,  69,  70,  71],\n", "        [ 72,  73,  74,  75],\n", "        [ 76,  77,  78,  79],\n", "        [ 80,  81,  82,  83],\n", "        [ 84,  85,  86,  87],\n", "        [ 88,  89,  90,  91],\n", "        [ 92,  93,  94,  95],\n", "        [ 96,  97,  98,  99],\n", "        [100, 101, 102, 103],\n", "        [104, 105, 106, 107],\n", "        [108, 109, 110, 111],\n", "        [112, 113, 114, 115],\n", "        [116, 117, 118, 119],\n", "        [120, 121, 122, 123],\n", "        [124, 125, 126, 127]])"]}, "execution_count": 76, "metadata": {}, "output_type": "execute_result"}], "source": ["x_[0, :32]"]}, {"cell_type": "code", "execution_count": null, "id": "56b209df", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 40, "id": "f23131eb", "metadata": {}, "outputs": [], "source": ["import torch.nn as nn"]}, {"cell_type": "code", "execution_count": 41, "id": "6406a5e2", "metadata": {}, "outputs": [], "source": ["reduction = nn.Linear(4 * 1, 2 * 1, bias=False)"]}, {"cell_type": "code", "execution_count": 43, "id": "49f1e900", "metadata": {}, "outputs": [], "source": ["x_ = reduction(x.float())"]}, {"cell_type": "code", "execution_count": 45, "id": "e6934069", "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.Size([1, 256, 2])"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["x_"]}, {"cell_type": "code", "execution_count": 49, "id": "cb00bfa2", "metadata": {}, "outputs": [], "source": ["x_1 = x_.view(1, 16, 16, 2)"]}, {"cell_type": "code", "execution_count": 50, "id": "1b5d9d05", "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([1, 16, 16, 2])"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["x_1.shape"]}, {"cell_type": "code", "execution_count": 51, "id": "1055a5f3", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[-4.4765e-02, -2.0975e+00, -4.1503e+00, -6.2031e+00, -8.2558e+00,\n", "         -1.0309e+01, -1.2361e+01, -1.4414e+01, -5.0913e+02, -5.1118e+02,\n", "         -5.1324e+02, -5.1529e+02, -5.1734e+02, -5.1939e+02, -5.2145e+02,\n", "         -5.2350e+02],\n", "        [-1.6467e+01, -1.8520e+01, -2.0572e+01, -2.2625e+01, -2.4678e+01,\n", "         -2.6731e+01, -2.8783e+01, -3.0836e+01, -4.9271e+02, -4.9476e+02,\n", "         -4.9681e+02, -4.9887e+02, -5.0092e+02, -5.0297e+02, -5.0503e+02,\n", "         -5.0708e+02],\n", "        [-3.2889e+01, -3.4942e+01, -3.6995e+01, -3.9047e+01, -4.1100e+01,\n", "         -4.3153e+01, -4.5206e+01, -4.7258e+01, -4.7629e+02, -4.7834e+02,\n", "         -4.8039e+02, -4.8244e+02, -4.8450e+02, -4.8655e+02, -4.8860e+02,\n", "         -4.9066e+02],\n", "        [-4.9311e+01, -5.1364e+01, -5.3417e+01, -5.5469e+01, -5.7522e+01,\n", "         -5.9575e+01, -6.1628e+01, -6.3681e+01, -4.5986e+02, -4.6192e+02,\n", "         -4.6397e+02, -4.6602e+02, -4.6808e+02, -4.7013e+02, -4.7218e+02,\n", "         -4.7423e+02],\n", "        [-6.5733e+01, -6.7786e+01, -6.9839e+01, -7.1892e+01, -7.3944e+01,\n", "         -7.5997e+01, -7.8050e+01, -8.0103e+01, -4.4344e+02, -4.4549e+02,\n", "         -4.4755e+02, -4.4960e+02, -4.5165e+02, -4.5371e+02, -4.5576e+02,\n", "         -4.5781e+02],\n", "        [-8.2155e+01, -8.4208e+01, -8.6261e+01, -8.8314e+01, -9.0366e+01,\n", "         -9.2419e+01, -9.4472e+01, -9.6525e+01, -4.2702e+02, -4.2907e+02,\n", "         -4.3113e+02, -4.3318e+02, -4.3523e+02, -4.3728e+02, -4.3934e+02,\n", "         -4.4139e+02],\n", "        [-9.8578e+01, -1.0063e+02, -1.0268e+02, -1.0474e+02, -1.0679e+02,\n", "         -1.0884e+02, -1.1089e+02, -1.1295e+02, -4.1060e+02, -4.1265e+02,\n", "         -4.1470e+02, -4.1676e+02, -4.1881e+02, -4.2086e+02, -4.2291e+02,\n", "         -4.2497e+02],\n", "        [-1.1500e+02, -1.1705e+02, -1.1911e+02, -1.2116e+02, -1.2321e+02,\n", "         -1.2526e+02, -1.2732e+02, -1.2937e+02, -3.9418e+02, -3.9623e+02,\n", "         -3.9828e+02, -4.0033e+02, -4.0239e+02, -4.0444e+02, -4.0649e+02,\n", "         -4.0855e+02],\n", "        [-1.3142e+02, -1.3347e+02, -1.3553e+02, -1.3758e+02, -1.3963e+02,\n", "         -1.4169e+02, -1.4374e+02, -1.4579e+02, -3.7775e+02, -3.7981e+02,\n", "         -3.8186e+02, -3.8391e+02, -3.8596e+02, -3.8802e+02, -3.9007e+02,\n", "         -3.9212e+02],\n", "        [-1.4784e+02, -1.4990e+02, -1.5195e+02, -1.5400e+02, -1.5605e+02,\n", "         -1.5811e+02, -1.6016e+02, -1.6221e+02, -3.6133e+02, -3.6338e+02,\n", "         -3.6544e+02, -3.6749e+02, -3.6954e+02, -3.7160e+02, -3.7365e+02,\n", "         -3.7570e+02],\n", "        [-1.6427e+02, -1.6632e+02, -1.6837e+02, -1.7042e+02, -1.7248e+02,\n", "         -1.7453e+02, -1.7658e+02, -1.7864e+02, -3.4491e+02, -3.4696e+02,\n", "         -3.4901e+02, -3.5107e+02, -3.5312e+02, -3.5517e+02, -3.5723e+02,\n", "         -3.5928e+02],\n", "        [-1.8069e+02, -1.8274e+02, -1.8479e+02, -1.8685e+02, -1.8890e+02,\n", "         -1.9095e+02, -1.9300e+02, -1.9506e+02, -3.2849e+02, -3.3054e+02,\n", "         -3.3259e+02, -3.3465e+02, -3.3670e+02, -3.3875e+02, -3.4080e+02,\n", "         -3.4286e+02],\n", "        [-1.9711e+02, -1.9916e+02, -2.0122e+02, -2.0327e+02, -2.0532e+02,\n", "         -2.0737e+02, -2.0943e+02, -2.1148e+02, -3.1207e+02, -3.1412e+02,\n", "         -3.1617e+02, -3.1822e+02, -3.2028e+02, -3.2233e+02, -3.2438e+02,\n", "         -3.2643e+02],\n", "        [-2.1353e+02, -2.1559e+02, -2.1764e+02, -2.1969e+02, -2.2174e+02,\n", "         -2.2380e+02, -2.2585e+02, -2.2790e+02, -2.9564e+02, -2.9770e+02,\n", "         -2.9975e+02, -3.0180e+02, -3.0385e+02, -3.0591e+02, -3.0796e+02,\n", "         -3.1001e+02],\n", "        [-2.2995e+02, -2.3201e+02, -2.3406e+02, -2.3611e+02, -2.3817e+02,\n", "         -2.4022e+02, -2.4227e+02, -2.4432e+02, -2.7922e+02, -2.8127e+02,\n", "         -2.8333e+02, -2.8538e+02, -2.8743e+02, -2.8948e+02, -2.9154e+02,\n", "         -2.9359e+02],\n", "        [-2.4638e+02, -2.4843e+02, -2.5048e+02, -2.5253e+02, -2.5459e+02,\n", "         -2.5664e+02, -2.5869e+02, -2.6075e+02, -2.6280e+02, -2.6485e+02,\n", "         -2.6690e+02, -2.6896e+02, -2.7101e+02, -2.7306e+02, -2.7512e+02,\n", "         -2.7717e+02]], grad_fn=<SelectBackward>)"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["x_1[0, :, :, 0]"]}, {"cell_type": "code", "execution_count": 282, "id": "d1f66ca3", "metadata": {}, "outputs": [], "source": ["avgpool = nn.AdaptiveAvgPool1d(1)\n", "x_ = avgpool(x_.transpose(1, 2))  # B C 1"]}, {"cell_type": "code", "execution_count": 287, "id": "4513f63d", "metadata": {}, "outputs": [], "source": ["x = torch.flatten(x_, 1)\n"]}, {"cell_type": "code", "execution_count": 288, "id": "d6d2482d", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[ -2.2822, -16.3735]], grad_fn=<ViewBackward>)"]}, "execution_count": 288, "metadata": {}, "output_type": "execute_result"}], "source": ["x"]}, {"cell_type": "markdown", "id": "8577eb0a", "metadata": {}, "source": ["# Testing"]}, {"cell_type": "code", "execution_count": 16, "id": "912ef2a0", "metadata": {}, "outputs": [], "source": ["import torch \n", "x_ = torch.arange(64).reshape(8,8)"]}, {"cell_type": "code", "execution_count": 63, "id": "7c340427", "metadata": {}, "outputs": [], "source": ["x_0_0 = x_[:, 0::4].reshape(2, 4, 2)[0]  # B H/2 W/2 C\n", "x_0_1 = x_[:, 0::4].reshape(2, 4, 2)[1].flip(0)  # B H/2 W/2 C\n", "x_1_0 = x_[:, 1::4].reshape(2, 4, 2)[0]\n", "x_1_1 = x_[:, 1::4].reshape(2, 4, 2)[1].flip(0)# B H/2 W/2 C\n", "x_2_0 = x_[:, 2::4].reshape(2, 4, 2)[0]\n", "x_2_1 = x_[:, 2::4].reshape(2, 4, 2)[1].flip(0)\n", "x_3_0 = x_[:, 3::4].reshape(2, 4, 2)[0]# B H/2 W/2 C\n", "x_3_1 = x_[:, 3::4].reshape(2, 4, 2)[1].flip(0)  # B H/2 W/2 C"]}, {"cell_type": "code", "execution_count": 64, "id": "d10ef5a4", "metadata": {}, "outputs": [], "source": ["t = torch.cat((x_0_0.reshape(4, 2, 1), x_1_0.reshape(4, 2, 1), x_2_0.reshape(4, 2, 1), x_3_0.reshape(4, 2, 1)), -1)\n", "t_ = torch.cat((x_0_1.reshape(4, 2, 1), x_1_1.reshape(4, 2, 1), x_2_1.reshape(4, 2, 1), x_3_1.reshape(4, 2, 1)), -1)"]}, {"cell_type": "code", "execution_count": 68, "id": "6c7af9c3", "metadata": {}, "outputs": [], "source": ["results = torch.empty(8, 2, 4)"]}, {"cell_type": "code", "execution_count": 69, "id": "c531f5b7", "metadata": {}, "outputs": [], "source": ["results[0::2, :, :] = t\n", "results[1::2, :, :] = t_"]}, {"cell_type": "code", "execution_count": 70, "id": "d2ee9adf", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[[ 0.,  1.,  2.,  3.],\n", "         [ 4.,  5.,  6.,  7.]],\n", "\n", "        [[56., 57., 58., 59.],\n", "         [60., 61., 62., 63.]],\n", "\n", "        [[ 8.,  9., 10., 11.],\n", "         [12., 13., 14., 15.]],\n", "\n", "        [[48., 49., 50., 51.],\n", "         [52., 53., 54., 55.]],\n", "\n", "        [[16., 17., 18., 19.],\n", "         [20., 21., 22., 23.]],\n", "\n", "        [[40., 41., 42., 43.],\n", "         [44., 45., 46., 47.]],\n", "\n", "        [[24., 25., 26., 27.],\n", "         [28., 29., 30., 31.]],\n", "\n", "        [[32., 33., 34., 35.],\n", "         [36., 37., 38., 39.]]])"]}, "execution_count": 70, "metadata": {}, "output_type": "execute_result"}], "source": ["results"]}, {"cell_type": "code", "execution_count": 45, "id": "b9b04351", "metadata": {}, "outputs": [], "source": ["x_0_1 = t[0, :, :]\n", "x_0_2 = t[1, :, :]"]}, {"cell_type": "code", "execution_count": 49, "id": "7e682b6a", "metadata": {}, "outputs": [{"data": {"text/plain": ["(tensor([[ 0,  4],\n", "         [ 8, 12],\n", "         [16, 20],\n", "         [24, 28]]),\n", " tensor([[56, 60],\n", "         [48, 52],\n", "         [40, 44],\n", "         [32, 36]]))"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["x_0_1, x_0_2.flip(0)"]}, {"cell_type": "code", "execution_count": 50, "id": "cff94c11", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[ 0,  1,  2,  3,  4,  5,  6,  7],\n", "        [ 8,  9, 10, 11, 12, 13, 14, 15],\n", "        [16, 17, 18, 19, 20, 21, 22, 23],\n", "        [24, 25, 26, 27, 28, 29, 30, 31],\n", "        [32, 33, 34, 35, 36, 37, 38, 39],\n", "        [40, 41, 42, 43, 44, 45, 46, 47],\n", "        [48, 49, 50, 51, 52, 53, 54, 55],\n", "        [56, 57, 58, 59, 60, 61, 62, 63]])"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["x_"]}, {"cell_type": "code", "execution_count": 21, "id": "930740ac", "metadata": {}, "outputs": [], "source": ["import torch \n", "t = torch.arange(32*16).reshape(1, 32, 16, 1)"]}, {"cell_type": "code", "execution_count": 18, "id": "bf1596ca", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[ 0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15],\n", "        [16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31],\n", "        [32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47],\n", "        [48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63]])"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["t.reshape(4, 16)"]}, {"cell_type": "code", "execution_count": 13, "id": "dacadc68", "metadata": {}, "outputs": [], "source": ["t = torch.cat((t[:16], t[16:].flip(0)), 1)"]}, {"cell_type": "code", "execution_count": 15, "id": "d562d493", "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([16, 16])"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["t.shape"]}, {"cell_type": "code", "execution_count": null, "id": "be347af1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "swin", "language": "python", "name": "swin"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.13"}}, "nbformat": 4, "nbformat_minor": 5}