# !/bin/bash 
export WANDB_MODE="disabled"
# export NCCL_BLOCKING_WAIT=1 
# export NCCL_DEBUG=INFO
# export PYTHONFAULTHANDLER=1


python -m torch.distributed.launch \
--nproc_per_node 3 \
--master_port 12345  main_wood.py \
--cfg configs/swin_ellip/woodsc_ddr_da.yaml \
--output /home-local2/akath.extra.nobkp/woodscape_resume \
--data-path /home-local2/akath.extra.nobkp/woodscapes \
--fov 175.0 \
--xi 0.0 \
--batch-size 4


# --resume /home/<USER>/scratch/Radial-unet_stan/CVRG_25_4/default/ckpt_epoch_80.pth \
# --cfg configs/swin/CVRG_den_unet.yaml \
