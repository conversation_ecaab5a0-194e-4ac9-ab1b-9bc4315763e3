# !/bin/bash 
export WANDB_MODE="disabled"
# export NCCL_BLOCKING_WAIT=1 
# export NCCL_DEBUG=INFO
# export PYTHONFAULTHANDLER=1

# GPU optimization settings
export CUDA_LAUNCH_BLOCKING=0
export TORCH_CUDNN_V8_API_ENABLED=1

python -m torch.distributed.launch \
--nproc_per_node 3 \
--master_port 12345  main_depth.py \
--cfg configs/swin_ellip/stanford_ellip_2_depth.yaml \
--output /home-local2/akath.extra.nobkp/stanford_depth \
--data-path  /home-local2/akath.extra.nobkp/2D3D \
--fov 175.0 \
--xi 0.0 \
--batch-size 4 \
--accumulation-steps 2



# --resume /home/<USER>/scratch/Radial-unet_stan/CVRG_25_4/default/ckpt_epoch_80.pth \
# --cfg configs/swin/CVRG_den_unet.yaml \
