#!/usr/bin/env python3
"""
Test script for the clean HRTNet segmentation model
"""

import torch
import torch.nn as nn
from models.build import SwinUnet
from config import get_config
import argparse

def create_dummy_config():
    """Create a dummy config for testing HRTNet"""
    class DummyConfig:
        def __init__(self):
            self.MODEL = type('obj', (object,), {
                'TYPE': 'hrtnet',
                'NUM_CLASSES': 19,
                'OPS': [1, 2, 3, 4, 5, 6, 7, 8, 9],
                'PATHS': [[0, 0, 0, 0, 0, 0, 0, 0, 0]],
                'DOWNS': [[0, 0, 0, 0, 0, 0, 0, 0, 0]],
                'WIDTHS': [[1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]],
                'LASTS': [0],
                'LAYERS': 9,
                'FCH': 12,
                'WIDTH_MULT_LIST': [1.0],
                'STEM_HEAD_WIDTH': (1.0, 1.0),
                'NORM': 'naiveSyncBN',
                'ALIGN_CORNERS': False,
                'HRT_MODEL_TYPE': '0'
            })()
            self.DATA = type('obj', (object,), {
                'IMG_SIZE': (512, 1024)  # Smaller size for testing
            })()
    
    return DummyConfig()

def test_hrtnet():
    """Test the HRTNet model"""
    print("Testing HRTNet segmentation model...")
    
    # Create dummy config
    config = create_dummy_config()
    
    # Create model
    try:
        model = SwinUnet(config, img_size=512, num_classes=19)
        print("✓ Model created successfully")
    except Exception as e:
        print(f"✗ Model creation failed: {e}")
        return False
    
    # Test forward pass
    try:
        model.eval()
        batch_size = 2
        height, width = 512, 1024
        
        # Create dummy input
        dummy_input = torch.randn(batch_size, 3, height, width)
        dummy_dist = torch.randn(batch_size, 1)  # Dummy distance parameter
        dummy_class_batch = torch.randn(batch_size, 1)  # Dummy class batch
        
        print(f"Input shape: {dummy_input.shape}")
        
        # Forward pass
        with torch.no_grad():
            output = model(dummy_input, dummy_dist, dummy_class_batch)
        
        print(f"Output shape: {output.shape}")
        print(f"Expected shape: ({batch_size}, {config.MODEL.NUM_CLASSES}, {height}, {width})")
        
        # Check output shape
        expected_shape = (batch_size, config.MODEL.NUM_CLASSES, height, width)
        if output.shape == expected_shape:
            print("✓ Forward pass successful - output shape is correct")
            return True
        else:
            print(f"✗ Forward pass failed - output shape mismatch")
            return False
            
    except Exception as e:
        print(f"✗ Forward pass failed: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Test HRTNet model')
    parser.add_argument('--config', type=str, help='Path to config file (optional)')
    args = parser.parse_args()
    
    if args.config:
        # Load config from file
        config = get_config(args.config)
    else:
        # Use dummy config
        config = create_dummy_config()
    
    success = test_hrtnet()
    
    if success:
        print("\n🎉 HRTNet test completed successfully!")
        print("\nTo use HRTNet in your training:")
        print("1. Set MODEL.TYPE: 'hrtnet' in your config file")
        print("2. Configure the HRTNet parameters (OPS, PATHS, DOWNS, etc.)")
        print("3. See configs/hrtnet_example.yaml for a complete example")
    else:
        print("\n❌ HRTNet test failed!")

if __name__ == "__main__":
    main()
