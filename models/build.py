# --------------------------------------------------------
# Swin Transformer
# Copyright (c) 2021 Microsoft
# Licensed under The MIT License [see LICENSE for details]
# Written by <PERSON><PERSON>
# --------------------------------------------------------

# coding=utf-8
from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

import copy
import logging
import math

from os.path import join as pjoin

import torch
import torch.nn as nn
import numpy as np

from torch.nn import CrossEntropyLoss, Dropout, Softmax, Linear, Conv2d, LayerNorm
from torch.nn.modules.utils import _pair
from scipy import ndimage
from .swin_unet import SwinTransformerSys
from .swin_unet_depth import SwinTransformerSys_depth
from .swin_unet_inv_depth import SwinTransformerSys_inv_depth
from .swin_unet_inv import SwinTransformerSys_inv
from .swin_unet_cube import SwinTransformerSys_cube
from .swin_unet_erp import SwinTransformerSys_erp
from .swin_unet_wood_v2 import SwinTransformerSys_v2
from .swin_unet_inv_wood_v2 import SwinTransformerSys_inv_v2
from .ddr_net import DualResNet, BasicBlock
from .ddr_net_da import DualResNet_da
from .hrtlab import HRTLab


class HRTNetClean(nn.Module):
    """Clean HRTNet segmentation model without loss functions and training logic"""
    def __init__(self, ops, paths, downs, widths, lasts, num_classes=19, layers=9, Fch=12,
                 width_mult_list=[1.,], stem_head_width=(1., 1.), input_size=(1024, 2048),
                 norm='naiveSyncBN', align_corners=False, model_type='0'):
        super(HRTNetClean, self).__init__()
        self.num_classes = num_classes
        self.align_corners = align_corners
        self.input_size = input_size
        self.model_type = model_type

        # Create a dummy loss function (not used in inference)
        dummy_loss = nn.CrossEntropyLoss()

        # Use HRTLab but only for semantic segmentation
        self.hrtlab = HRTLab(
            ops=ops,
            paths=paths,
            downs=downs,
            widths=widths,
            lasts=lasts,
            semantic_loss=dummy_loss,
            semantic_loss_weight=1.0,
            center_loss=dummy_loss,
            center_loss_weight=0.0,
            offset_loss=dummy_loss,
            offset_loss_weight=0.0,
            lamb=0.2,
            eval_flag=True,
            num_classes=num_classes,
            layers=layers,
            Fch=Fch,
            width_mult_list=width_mult_list,
            stem_head_width=stem_head_width,
            input_size=input_size,
            norm=norm,
            align_corners=align_corners,
            pretrain='',
            model_type=model_type,
            sem_only=True,  # Only semantic segmentation
            use_aux=False   # No auxiliary heads
        )

    def forward(self, input):
        # Set to evaluation mode to get clean output
        self.hrtlab.eval()
        with torch.no_grad():
            # HRTLab in eval mode with sem_only=True returns just the semantic output
            semantic_output = self.hrtlab(input)
        return semantic_output


class SwinUnet(nn.Module):
    def __init__(self, config, img_size=224, num_classes=21843, zero_head=False, vis=False):
        super(SwinUnet, self).__init__()
        model_type = config.MODEL.TYPE
        if model_type == 'swin_unet':
            self.swin_unet = SwinTransformerSys(img_size=config.DATA.IMG_SIZE_WOOD,
                        patch_size=config.MODEL.SWIN.PATCH_SIZE,
                        in_chans=config.MODEL.SWIN.IN_CHANS,
                        num_classes=config.MODEL.NUM_CLASSES,
                        embed_dim=config.MODEL.SWIN.EMBED_DIM,
                        depths=config.MODEL.SWIN.DEPTHS,
                        num_heads=config.MODEL.SWIN.NUM_HEADS,
                        window_size=config.MODEL.SWIN.WINDOW_SIZE_GRID,
                        mlp_ratio=config.MODEL.SWIN.MLP_RATIO,
                        qkv_bias=config.MODEL.SWIN.QKV_BIAS,
                        qk_scale=config.MODEL.SWIN.QK_SCALE,
                        drop_rate=config.MODEL.DROP_RATE,
                        drop_path_rate=config.MODEL.DROP_PATH_RATE,
                        ape=config.MODEL.SWIN.APE,
                        patch_norm=config.MODEL.SWIN.PATCH_NORM,
                        use_checkpoint=config.TRAIN.USE_CHECKPOINT)
        elif model_type == 'swin_unet_depth':
            self.swin_unet = SwinTransformerSys_depth(img_size=config.DATA.IMG_SIZE_WOOD,
                        patch_size=config.MODEL.SWIN.PATCH_SIZE,
                        in_chans=config.MODEL.SWIN.IN_CHANS,
                        num_classes=config.MODEL.NUM_CLASSES,
                        embed_dim=config.MODEL.SWIN.EMBED_DIM,
                        depths=config.MODEL.SWIN.DEPTHS,
                        num_heads=config.MODEL.SWIN.NUM_HEADS,
                        window_size=config.MODEL.SWIN.WINDOW_SIZE_GRID,
                        mlp_ratio=config.MODEL.SWIN.MLP_RATIO,
                        qkv_bias=config.MODEL.SWIN.QKV_BIAS,
                        qk_scale=config.MODEL.SWIN.QK_SCALE,
                        drop_rate=config.MODEL.DROP_RATE,
                        drop_path_rate=config.MODEL.DROP_PATH_RATE,
                        ape=config.MODEL.SWIN.APE,
                        patch_norm=config.MODEL.SWIN.PATCH_NORM,
                        use_checkpoint=config.TRAIN.USE_CHECKPOINT,
                        activation_type=getattr(config.MODEL, 'ACTIVATION_TYPE', 'sigmoid'))
        elif model_type == 'swin_unet_v2':
            self.swin_unet = SwinTransformerSys_v2(img_size=config.DATA.IMG_SIZE_WOOD,
                        patch_size=config.MODEL.SWIN.PATCH_SIZE,
                        shift_size = config.MODEL.SWIN.SHIFT_SIZE,
                        in_chans=config.MODEL.SWIN.IN_CHANS,
                        num_classes=config.MODEL.NUM_CLASSES,
                        embed_dim=config.MODEL.SWIN.EMBED_DIM,
                        depths=config.MODEL.SWIN.DEPTHS,
                        num_heads=config.MODEL.SWIN.NUM_HEADS,
                        window_size=config.MODEL.SWIN.WINDOW_SIZE_GRID,
                        mlp_ratio=config.MODEL.SWIN.MLP_RATIO,
                        qkv_bias=config.MODEL.SWIN.QKV_BIAS,
                        qk_scale=config.MODEL.SWIN.QK_SCALE,
                        drop_rate=config.MODEL.DROP_RATE,
                        attn_drop=config.MODEL.ATTN_DROP_RATE,
                        drop_path_rate=config.MODEL.DROP_PATH_RATE,
                        ape=config.MODEL.SWIN.APE,
                        patch_norm=config.MODEL.SWIN.PATCH_NORM,
                        use_checkpoint=config.TRAIN.USE_CHECKPOINT, 
                        use_v2_norm_placement=config.MODEL.use_v2_norm_placement,
                        use_cos_attn=config.MODEL.use_cos_attn)
        elif model_type == 'swin_unet_inv_v2':
            self.swin_unet = SwinTransformerSys_inv_v2(img_size=config.DATA.IMG_SIZE,
                        patch_size=config.MODEL.SWIN.PATCH_SIZE,
                        in_chans=config.MODEL.SWIN.IN_CHANS,
                        num_classes=config.MODEL.NUM_CLASSES,
                        embed_dim=config.MODEL.SWIN.EMBED_DIM,
                        depths=config.MODEL.SWIN.DEPTHS,
                        num_heads=config.MODEL.SWIN.NUM_HEADS,
                        window_size=config.MODEL.SWIN.WINDOW_SIZE_GRID,
                        mlp_ratio=config.MODEL.SWIN.MLP_RATIO,
                        qkv_bias=config.MODEL.SWIN.QKV_BIAS,
                        qk_scale=config.MODEL.SWIN.QK_SCALE,
                        drop_rate=config.MODEL.DROP_RATE,
                        drop_path_rate=config.MODEL.DROP_PATH_RATE,
                        ape=config.MODEL.SWIN.APE,
                        patch_norm=config.MODEL.SWIN.PATCH_NORM,
                        use_checkpoint=config.TRAIN.USE_CHECKPOINT,
                        use_v2_norm_placement=config.MODEL.use_v2_norm_placement,
                        use_cos_attn=config.MODEL.use_cos_attn)
        elif model_type == 'swin_unet_inv':
            self.swin_unet = SwinTransformerSys_inv(img_size=config.DATA.IMG_SIZE_WOOD,
                        patch_size=config.MODEL.SWIN.PATCH_SIZE,
                        in_chans=config.MODEL.SWIN.IN_CHANS,
                        num_classes=config.MODEL.NUM_CLASSES,
                        embed_dim=config.MODEL.SWIN.EMBED_DIM,
                        depths=config.MODEL.SWIN.DEPTHS,
                        num_heads=config.MODEL.SWIN.NUM_HEADS,
                        window_size=config.MODEL.SWIN.WINDOW_SIZE_GRID,
                        mlp_ratio=config.MODEL.SWIN.MLP_RATIO,
                        qkv_bias=config.MODEL.SWIN.QKV_BIAS,
                        qk_scale=config.MODEL.SWIN.QK_SCALE,
                        drop_rate=config.MODEL.DROP_RATE,
                        drop_path_rate=config.MODEL.DROP_PATH_RATE,
                        ape=config.MODEL.SWIN.APE,
                        patch_norm=config.MODEL.SWIN.PATCH_NORM,
                        use_checkpoint=config.TRAIN.USE_CHECKPOINT)
        elif model_type == 'swin_unet_inv_depth':
            self.swin_unet = SwinTransformerSys_inv_depth(img_size=config.DATA.IMG_SIZE_WOOD,
                        patch_size=config.MODEL.SWIN.PATCH_SIZE,
                        in_chans=config.MODEL.SWIN.IN_CHANS,
                        num_classes=config.MODEL.NUM_CLASSES,
                        embed_dim=config.MODEL.SWIN.EMBED_DIM,
                        depths=config.MODEL.SWIN.DEPTHS,
                        num_heads=config.MODEL.SWIN.NUM_HEADS,
                        window_size=config.MODEL.SWIN.WINDOW_SIZE_GRID,
                        mlp_ratio=config.MODEL.SWIN.MLP_RATIO,
                        qkv_bias=config.MODEL.SWIN.QKV_BIAS,
                        qk_scale=config.MODEL.SWIN.QK_SCALE,
                        drop_rate=config.MODEL.DROP_RATE,
                        drop_path_rate=config.MODEL.DROP_PATH_RATE,
                        ape=config.MODEL.SWIN.APE,
                        patch_norm=config.MODEL.SWIN.PATCH_NORM,
                        use_checkpoint=config.TRAIN.USE_CHECKPOINT,
                        activation_type=getattr(config.MODEL, 'ACTIVATION_TYPE', 'sigmoid'))
        elif model_type == 'swin_unet_cube':
            self.swin_unet = SwinTransformerSys_cube(img_size=config.DATA.IMG_SIZE_WOOD,
                        patch_size=config.MODEL.SWIN.PATCH_SIZE,
                        in_chans=config.MODEL.SWIN.IN_CHANS,
                        num_classes=config.MODEL.NUM_CLASSES,
                        embed_dim=config.MODEL.SWIN.EMBED_DIM,
                        depths=config.MODEL.SWIN.DEPTHS,
                        num_heads=config.MODEL.SWIN.NUM_HEADS,
                        window_size=config.MODEL.SWIN.WINDOW_SIZE_GRID,
                        mlp_ratio=config.MODEL.SWIN.MLP_RATIO,
                        qkv_bias=config.MODEL.SWIN.QKV_BIAS,
                        qk_scale=config.MODEL.SWIN.QK_SCALE,
                        drop_rate=config.MODEL.DROP_RATE,
                        drop_path_rate=config.MODEL.DROP_PATH_RATE,
                        ape=config.MODEL.SWIN.APE,
                        patch_norm=config.MODEL.SWIN.PATCH_NORM,
                        use_checkpoint=config.TRAIN.USE_CHECKPOINT)
        elif model_type == 'swin_unet_erp':
            self.swin_unet = SwinTransformerSys_erp(img_size=config.DATA.IMG_SIZE_WOOD,
                        patch_size=config.MODEL.SWIN.PATCH_SIZE,
                        in_chans=config.MODEL.SWIN.IN_CHANS,
                        num_classes=config.MODEL.NUM_CLASSES,
                        embed_dim=config.MODEL.SWIN.EMBED_DIM,
                        depths=config.MODEL.SWIN.DEPTHS,
                        num_heads=config.MODEL.SWIN.NUM_HEADS,
                        window_size=config.MODEL.SWIN.WINDOW_SIZE_GRID,
                        mlp_ratio=config.MODEL.SWIN.MLP_RATIO,
                        qkv_bias=config.MODEL.SWIN.QKV_BIAS,
                        qk_scale=config.MODEL.SWIN.QK_SCALE,
                        drop_rate=config.MODEL.DROP_RATE,
                        drop_path_rate=config.MODEL.DROP_PATH_RATE,
                        ape=config.MODEL.SWIN.APE,
                        patch_norm=config.MODEL.SWIN.PATCH_NORM,
                        use_checkpoint=config.TRAIN.USE_CHECKPOINT)
        elif model_type == 'ddrnet':
            self.swin_unet = DualResNet(BasicBlock, [2, 2, 2, 2], num_classes=config.MODEL.NUM_CLASSES, planes=64, spp_planes=128, head_planes=128, augment=True)
        elif model_type == 'ddrnet_da':
            self.swin_unet = DualResNet_da(BasicBlock, [2, 2, 2, 2], num_classes=config.MODEL.NUM_CLASSES, planes=64, spp_planes=128, head_planes=128, augment=True)
        elif model_type == 'deeplab':   
            self.swin_unet = DeepLab(num_classes=config.MODEL.NUM_CLASSES, backbone='resnet', output_stride=16, sync_bn=True, freeze_bn=False)
            self.swin_unet = DualResNet_da(BasicBlock, [2, 2, 2, 2], num_classes=config.MODEL.NUM_CLASSES, planes=64, spp_planes=128, head_planes=128, augment=True)
        elif model_type == 'deeplab_da':
            self.swin_unet = DualResNet_da(BasicBlock, [2, 2, 2, 2], num_classes=config.MODEL.NUM_CLASSES, planes=64, spp_planes=128, head_planes=128, augment=True)
        elif model_type == 'hrtnet':
            # Clean HRTNet segmentation model
            self.swin_unet = HRTNetClean(
                ops=getattr(config.MODEL, 'OPS', [1, 2, 3, 4, 5, 6, 7, 8, 9]),
                paths=getattr(config.MODEL, 'PATHS', [[0, 0, 0, 0, 0, 0, 0, 0, 0]]),
                downs=getattr(config.MODEL, 'DOWNS', [[0, 0, 0, 0, 0, 0, 0, 0, 0]]),
                widths=getattr(config.MODEL, 'WIDTHS', [[1., 1., 1., 1., 1., 1., 1., 1.]]),
                lasts=getattr(config.MODEL, 'LASTS', [0]),
                num_classes=config.MODEL.NUM_CLASSES,
                layers=getattr(config.MODEL, 'LAYERS', 9),
                Fch=getattr(config.MODEL, 'FCH', 12),
                width_mult_list=getattr(config.MODEL, 'WIDTH_MULT_LIST', [1.,]),
                stem_head_width=getattr(config.MODEL, 'STEM_HEAD_WIDTH', (1., 1.)),
                input_size=getattr(config.DATA, 'IMG_SIZE', (1024, 2048)),
                norm=getattr(config.MODEL, 'NORM', 'naiveSyncBN'),
                align_corners=getattr(config.MODEL, 'ALIGN_CORNERS', False),
                model_type=getattr(config.MODEL, 'HRT_MODEL_TYPE', '0')
            )
        else:
            raise NotImplementedError(f"Unkown model: {model_type}")

    def forward(self, x, dist,class_batch):
        if x.size()[1] == 1:
            x = x.repeat(1,3,1,1)
        logits = self.swin_unet(x,dist,class_batch)
        return logits

    def load_from(self, config):
        # breakpoint()
        pretrained_path = config.MODEL.PRETRAIN_CKPT
        if pretrained_path is not None:
            # breakpoint()
            # print(config.MODEL.BASE_MODEL)
            if config.MODEL.BASE_MODEL == 'swin':
                print("pretrained_path:{}".format(pretrained_path))
                device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
                pretrained_dict = torch.load(pretrained_path, map_location=device)
                if "model"  not in pretrained_dict:
                    print("---start load pretrained modle by splitting---")
                    pretrained_dict = {k[17:]:v for k,v in pretrained_dict.items()}
                    for k in list(pretrained_dict.keys()):
                        if "output" in k:
                            print("delete key:{}".format(k))
                            del pretrained_dict[k]
                    msg = self.swin_unet.load_state_dict(pretrained_dict,strict=False)
                    # print(msg)
                    return
                pretrained_dict = pretrained_dict['model']
                print("---start load pretrained modle of swin encoder---")
                # breakpoint()
                model_dict = self.swin_unet.state_dict()
                full_dict = copy.deepcopy(pretrained_dict)
                for k, v in pretrained_dict.items():
                    if "layers." in k:
                        current_layer_num = 3-int(k[7:8])
                        current_k = "layers_up." + str(current_layer_num) + k[8:]
                        full_dict.update({current_k:v})
                for k in list(full_dict.keys()):
                    if k in model_dict:
                        if full_dict[k].shape != model_dict[k].shape:
                            print("delete:{};shape pretrain:{};shape model:{}".format(k,v.shape,model_dict[k].shape))
                            del full_dict[k]
                # breakpoint()
                msg = self.swin_unet.load_state_dict(full_dict, strict=False)
                # print(msg)
            elif config.MODEL.BASE_MODEL == 'ddr':
                # breakpoint()
                print("ass")
                print("pretrained_path:{}".format(pretrained_path))
                device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
                checkpoint = torch.load(pretrained_path, map_location=device) 
                msg = self.swin_unet.load_state_dict(checkpoint, strict=False)
        else:
            print("none pretrain")
